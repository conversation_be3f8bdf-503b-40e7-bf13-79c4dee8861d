# ==============================================
# APCSA Learning Platform - Environment Variables
# ==============================================

# Application Settings
APP_NAME=APCSA Learning Platform
APP_VERSION=1.0.0
ENVIRONMENT=development

# Port Configuration
FRONTEND_PORT=3000
BACKEND_PORT=8000
POSTGRES_PORT=5432
REDIS_PORT=6379
PGADMIN_PORT=5050

# Database Configuration
POSTGRES_DB=apcsa_platform
POSTGRES_USER=apcsa_user
POSTGRES_PASSWORD=apcsa_password_change_me
DATABASE_URL=postgresql://apcsa_user:apcsa_password_change_me@localhost:5432/apcsa_platform

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=redis_password_change_me

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-too
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# AI Services
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# PgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123

# Production Settings
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ORIGINS=http://localhost:3000,https://your-domain.com

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_RATE_LIMITING=true

# External Services
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Monitoring (Optional)
SENTRY_DSN=your-sentry-dsn-here
