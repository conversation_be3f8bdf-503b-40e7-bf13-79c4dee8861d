# 📦 APCSA数据库备份与恢复指南

## 🎯 概述

本指南提供了APCSA学习平台数据库的完整备份和恢复解决方案，包括自动化脚本和详细的操作说明。

---

## 📥 数据库备份

### 快速备份

```bash
# 执行完整数据库备份
python database_backup.py
```

### 备份结果

备份完成后会生成：

1. **备份目录**: `database_backup_YYYYMMDD_HHMMSS/`
2. **压缩包**: `apcsa_database_backup_YYYYMMDD.tar.gz`

### 备份内容

当前备份包含以下表：

| 表名 | 记录数 | 说明 |
|------|--------|------|
| `units` | 10 | APCSA课程单元 |
| `topics` | 62 | 课程主题 |
| `learning_resources` | 0 | 学习资源 |
| `ai_generated_content` | 0 | AI生成内容 |

**总计**: 72条记录

---

## 📤 数据库恢复

### 基本恢复

```bash
# 恢复所有表（插入模式）
python database_restore.py database_backup_20250602_181430/

# 恢复指定表
python database_restore.py database_backup_20250602_181430/ --tables units topics

# 干运行（仅显示计划，不执行）
python database_restore.py database_backup_20250602_181430/ --dry-run
```

### 恢复模式

#### 1. Insert模式（默认）
```bash
python database_restore.py backup_dir/ --mode insert
```
- 插入新记录
- 如果ID冲突会失败
- 最安全的模式

#### 2. Upsert模式
```bash
python database_restore.py backup_dir/ --mode upsert
```
- 更新现有记录或插入新记录
- 基于主键进行匹配
- 适合数据更新

#### 3. Truncate模式（危险）
```bash
python database_restore.py backup_dir/ --mode truncate
```
- ⚠️ **警告**: 会删除表中所有现有数据
- 完全替换表内容
- 需要输入 `YES` 确认

---

## 📁 备份文件结构

```
database_backup_20250602_181430/
├── README.md                    # 备份摘要（可读格式）
├── backup_summary.json          # 详细备份信息
├── units.json                   # Units表数据
├── topics.json                  # Topics表数据
├── learning_resources.json      # Learning Resources表数据
└── ai_generated_content.json    # AI Generated Content表数据
```

### 文件格式

每个表的备份文件格式：

```json
{
  "table_name": "units",
  "backup_time": "2025-06-02T18:14:31.536948",
  "record_count": 10,
  "data": [
    {
      "id": "unit-1",
      "title": "Primitive Types",
      "description": "Learn about primitive data types...",
      // ... 其他字段
    }
    // ... 更多记录
  ]
}
```

---

## 🔧 高级用法

### 1. 定时备份

创建定时任务：

```bash
# 编辑crontab
crontab -e

# 添加每日凌晨2点备份
0 2 * * * cd /path/to/project && python database_backup.py
```

### 2. 远程备份

```bash
# 备份并上传到远程服务器
python database_backup.py
scp apcsa_database_backup_*.tar.gz user@remote-server:/backup/
```

### 3. 选择性恢复

```bash
# 只恢复units表
python database_restore.py backup_dir/ --tables units

# 恢复多个指定表
python database_restore.py backup_dir/ --tables units topics learning_resources
```

---

## 🛡️ 安全注意事项

### 备份安全

1. **定期备份**: 建议每日备份
2. **多地存储**: 本地+云端双重备份
3. **访问控制**: 限制备份文件访问权限
4. **加密存储**: 敏感数据建议加密

### 恢复安全

1. **测试环境**: 先在测试环境验证恢复
2. **数据验证**: 恢复后检查数据完整性
3. **权限确认**: 确保有足够的数据库权限
4. **备份现状**: 恢复前先备份当前数据

---

## 🚨 故障排除

### 常见问题

#### 1. 连接失败
```
❌ 数据库连接失败
```
**解决方案**:
- 检查网络连接
- 验证Supabase配置
- 确认API密钥有效

#### 2. 权限不足
```
❌ 权限不足，无法访问表
```
**解决方案**:
- 检查Supabase项目权限
- 确认API密钥权限级别
- 联系管理员提升权限

#### 3. 表不存在
```
❌ 表不存在: table_name
```
**解决方案**:
- 检查表名拼写
- 确认表已创建
- 运行数据库初始化脚本

#### 4. 数据冲突
```
❌ 主键冲突
```
**解决方案**:
- 使用 `--mode upsert` 模式
- 或先清理冲突数据
- 或使用 `--mode truncate` 完全替换

### 调试模式

```bash
# 查看详细错误信息
python database_backup.py 2>&1 | tee backup.log

# 干运行查看恢复计划
python database_restore.py backup_dir/ --dry-run
```

---

## 📊 监控和验证

### 备份验证

```bash
# 检查备份完整性
python -c "
import json
with open('database_backup_20250602_181430/backup_summary.json') as f:
    summary = json.load(f)
    print(f'备份时间: {summary["backup_time"]}')
    print(f'总记录数: {summary["total_records_backed_up"]}')
    print(f'备份表数: {summary["available_tables"]}')
"
```

### 恢复验证

恢复脚本会自动验证：
- 记录数量匹配
- 数据完整性检查
- 表结构一致性

---

## 📈 最佳实践

### 1. 备份策略

- **频率**: 每日备份，重要更新后立即备份
- **保留**: 保留最近30天的备份
- **测试**: 定期测试恢复流程

### 2. 存储策略

- **本地**: 快速访问和恢复
- **云端**: 灾难恢复和长期保存
- **版本**: 保留多个版本以防数据损坏

### 3. 恢复策略

- **测试优先**: 总是先在测试环境恢复
- **增量恢复**: 优先使用upsert模式
- **完整恢复**: 仅在必要时使用truncate模式

---

## 🎯 快速参考

### 常用命令

```bash
# 完整备份
python database_backup.py

# 查看备份内容
cat database_backup_*/README.md

# 测试恢复
python database_restore.py backup_dir/ --dry-run

# 安全恢复
python database_restore.py backup_dir/ --mode upsert

# 完整恢复（危险）
python database_restore.py backup_dir/ --mode truncate
```

### 文件位置

- **备份脚本**: `database_backup.py`
- **恢复脚本**: `database_restore.py`
- **备份目录**: `database_backup_YYYYMMDD_HHMMSS/`
- **压缩包**: `apcsa_database_backup_YYYYMMDD.tar.gz`

---

## 📞 支持

如有问题，请：

1. 查看错误日志
2. 检查网络和权限
3. 参考故障排除部分
4. 联系技术支持

**备份是数据安全的最后一道防线，请定期执行并验证备份的有效性！** 🛡️ 