# 📚 APCSA学习平台 - 文档索引

> ⚠️ **重要提示**: 本文档已被新的主文档索引替代  
> 请查看 **[DOCUMENTATION_MASTER_INDEX.md](DOCUMENTATION_MASTER_INDEX.md)** 获取完整的文档导航

---

## 🎯 快速跳转

- **📖 完整文档索引**: [DOCUMENTATION_MASTER_INDEX.md](DOCUMENTATION_MASTER_INDEX.md)
- **🚀 项目主页**: [README.md](README.md)
- **📊 项目状态**: [PRODUCTION_READY_STATUS.md](PRODUCTION_READY_STATUS.md)
- **🛠️ 开发指南**: [docs/PROJECT_DESIGN.md](docs/PROJECT_DESIGN.md)

---

## 🚀 核心文档 (精简版)

### 必读文档
- **[README.md](README.md)** - 项目概述和快速开始指南
- **[PRODUCTION_READY_STATUS.md](PRODUCTION_READY_STATUS.md)** - 生产就绪状态报告
- **[PROJECT_COMPLETION_REPORT.md](PROJECT_COMPLETION_REPORT.md)** - 项目完成报告

### 开发文档
- **[docs/PROJECT_DESIGN.md](docs/PROJECT_DESIGN.md)** - 项目设计文档
- **[docs/CONTRIBUTING.md](docs/CONTRIBUTING.md)** - 贡献指南

### 部署文档
- **[env.example](env.example)** - 环境变量示例
- **[docker-compose.yml](docker-compose.yml)** - Docker容器编排
- **[DATABASE_BACKUP_GUIDE.md](DATABASE_BACKUP_GUIDE.md)** - 数据库备份指南

---

## 📁 项目结构

```
boruiCourses/
├── 📄 DOCUMENTATION_MASTER_INDEX.md    # 主文档索引 ⭐
├── 📄 README.md                        # 项目主页
├── 📁 backend/                         # FastAPI后端
├── 📁 frontend/                        # React前端
├── 📁 database/                        # 数据库文件
├── 📁 docs/                           # 详细文档
└── 📄 各种配置文件和脚本
```

---

*本文档为简化版索引，完整文档导航请查看 [DOCUMENTATION_MASTER_INDEX.md](DOCUMENTATION_MASTER_INDEX.md)* 