# 🚀 APCSA 平台分布式架构迁移规划

## 📋 迁移概述

从当前的FastAPI单体架构迁移到Scrapy+Redis+Celery分布式架构，实现企业级的抓取能力、智能调度和实时监控。

### 🎯 迁移目标

- **高可用性**: 99.9% 系统可用性
- **高并发**: 支持1000+并发抓取任务
- **智能调度**: 基于网站负载和API限制的智能调度
- **实时监控**: 全方位系统监控和告警
- **自动恢复**: 故障自动检测和恢复机制
- **水平扩展**: 支持动态扩容和负载均衡

---

## 🏗️ 新架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Nginx Load Balancer]
    end
    
    subgraph "API Gateway"
        API1[FastAPI Instance 1]
        API2[FastAPI Instance 2]
        API3[FastAPI Instance 3]
    end
    
    subgraph "Task Queue & Cache"
        REDIS[(Redis Cluster)]
        CELERY[Celery Beat Scheduler]
    end
    
    subgraph "Scrapy Cluster"
        SCRAPYD1[Scrapyd Node 1]
        SCRAPYD2[Scrapyd Node 2]
        SCRAPYD3[Scrapyd Node 3]
        SPIDER1[Khan Academy Spider]
        SPIDER2[CodeHS Spider]
        SPIDER3[YouTube Spider]
        SPIDER4[Documentation Spider]
    end
    
    subgraph "Workers"
        WORKER1[Celery Worker 1]
        WORKER2[Celery Worker 2]
        WORKER3[Celery Worker 3]
    end
    
    subgraph "Monitoring"
        FLOWER[Flower Dashboard]
        GRAFANA[Grafana]
        PROMETHEUS[Prometheus]
        ALERTMANAGER[AlertManager]
    end
    
    subgraph "Storage"
        POSTGRES[(PostgreSQL)]
        MONGODB[(MongoDB)]
        ELASTICSEARCH[(Elasticsearch)]
    end
    
    LB --> API1
    LB --> API2
    LB --> API3
    
    API1 --> REDIS
    API2 --> REDIS
    API3 --> REDIS
    
    REDIS --> CELERY
    CELERY --> WORKER1
    CELERY --> WORKER2
    CELERY --> WORKER3
    
    WORKER1 --> SCRAPYD1
    WORKER2 --> SCRAPYD2
    WORKER3 --> SCRAPYD3
    
    SCRAPYD1 --> SPIDER1
    SCRAPYD2 --> SPIDER2
    SCRAPYD3 --> SPIDER3
    
    SPIDER1 --> POSTGRES
    SPIDER2 --> MONGODB
    SPIDER3 --> ELASTICSEARCH
    
    PROMETHEUS --> GRAFANA
    PROMETHEUS --> ALERTMANAGER
```

### 核心组件

1. **API Gateway**: FastAPI集群 (保留并增强)
2. **Task Queue**: Redis + Celery
3. **Scraping Engine**: Scrapy + Scrapyd
4. **Monitoring**: Prometheus + Grafana + AlertManager
5. **Storage**: PostgreSQL + MongoDB + Elasticsearch
6. **Load Balancer**: Nginx

---

## 📁 新项目结构

```
apcsa_distributed/
├── api_gateway/                    # FastAPI API网关
│   ├── app/
│   │   ├── api/
│   │   │   ├── v1/
│   │   │   │   ├── scraping.py     # 抓取任务API
│   │   │   │   ├── monitoring.py   # 监控API
│   │   │   │   └── content.py      # 内容管理API
│   │   │   └── dependencies.py
│   │   ├── core/
│   │   │   ├── config.py
│   │   │   ├── security.py
│   │   │   └── database.py
│   │   ├── models/
│   │   ├── schemas/
│   │   └── services/
│   ├── requirements.txt
│   └── Dockerfile
│
├── scrapy_cluster/                 # Scrapy爬虫集群
│   ├── apcsa_scraper/
│   │   ├── spiders/
│   │   │   ├── khan_academy.py
│   │   │   ├── codehs.py
│   │   │   ├── youtube.py
│   │   │   ├── oracle_docs.py
│   │   │   └── base_spider.py
│   │   ├── items.py
│   │   ├── pipelines/
│   │   │   ├── database_pipeline.py
│   │   │   ├── validation_pipeline.py
│   │   │   ├── deduplication_pipeline.py
│   │   │   └── monitoring_pipeline.py
│   │   ├── middlewares/
│   │   │   ├── retry_middleware.py
│   │   │   ├── proxy_middleware.py
│   │   │   └── rate_limit_middleware.py
│   │   ├── settings/
│   │   │   ├── base.py
│   │   │   ├── development.py
│   │   │   └── production.py
│   │   └── utils/
│   ├── scrapyd.conf
│   ├── requirements.txt
│   └── Dockerfile
│
├── task_queue/                     # Celery任务队列
│   ├── tasks/
│   │   ├── scraping_tasks.py
│   │   ├── monitoring_tasks.py
│   │   ├── cleanup_tasks.py
│   │   └── notification_tasks.py
│   ├── celery_app.py
│   ├── celery_config.py
│   ├── beat_schedule.py
│   ├── requirements.txt
│   └── Dockerfile
│
├── monitoring/                     # 监控系统
│   ├── prometheus/
│   │   ├── prometheus.yml
│   │   ├── alert_rules.yml
│   │   └── Dockerfile
│   ├── grafana/
│   │   ├── dashboards/
│   │   │   ├── scrapy_dashboard.json
│   │   │   ├── celery_dashboard.json
│   │   │   └── system_dashboard.json
│   │   ├── provisioning/
│   │   └── Dockerfile
│   ├── alertmanager/
│   │   ├── alertmanager.yml
│   │   └── Dockerfile
│   └── exporters/
│       ├── scrapy_exporter.py
│       └── celery_exporter.py
│
├── infrastructure/                 # 基础设施
│   ├── docker/
│   │   ├── docker-compose.yml
│   │   ├── docker-compose.prod.yml
│   │   └── nginx/
│   │       ├── nginx.conf
│   │       └── Dockerfile
│   ├── kubernetes/
│   │   ├── namespace.yaml
│   │   ├── configmaps/
│   │   ├── deployments/
│   │   ├── services/
│   │   └── ingress/
│   └── terraform/
│       ├── main.tf
│       ├── variables.tf
│       └── outputs.tf
│
├── shared/                         # 共享组件
│   ├── database/
│   │   ├── models/
│   │   ├── migrations/
│   │   └── connections.py
│   ├── utils/
│   │   ├── logging.py
│   │   ├── metrics.py
│   │   └── notifications.py
│   ├── schemas/
│   └── constants.py
│
├── tests/                          # 测试
│   ├── unit/
│   ├── integration/
│   ├── load/
│   └── e2e/
│
├── scripts/                        # 部署脚本
│   ├── deploy.sh
│   ├── migrate.sh
│   ├── backup.sh
│   └── health_check.sh
│
├── docs/                          # 文档
│   ├── api/
│   ├── deployment/
│   └── monitoring/
│
├── .env.example
├── docker-compose.yml
├── requirements.txt
└── README.md
```

---

## 🔧 核心组件实现

### 1. Scrapy 爬虫集群

#### 基础爬虫类
```python
# scrapy_cluster/apcsa_scraper/spiders/base_spider.py
import scrapy
from scrapy_redis.spiders import RedisSpider
from prometheus_client import Counter, Histogram, Gauge
import time

class BaseAPCSASpider(RedisSpider):
    """APCSA爬虫基类"""
    
    # Prometheus指标
    scraped_items = Counter('scrapy_items_scraped_total', 'Total scraped items', ['spider', 'item_type'])
    request_duration = Histogram('scrapy_request_duration_seconds', 'Request duration', ['spider'])
    active_requests = Gauge('scrapy_active_requests', 'Active requests', ['spider'])
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.start_time = time.time()
        
    def start_requests(self):
        """智能起始请求生成"""
        for url in self.get_start_urls():
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                meta={
                    'start_time': time.time(),
                    'retry_count': 0,
                    'priority': self.get_url_priority(url)
                }
            )
    
    def parse(self, response):
        """基础解析方法"""
        # 记录请求时长
        duration = time.time() - response.meta['start_time']
        self.request_duration.labels(spider=self.name).observe(duration)
        
        # 调用具体解析逻辑
        yield from self.parse_content(response)
    
    def parse_content(self, response):
        """子类实现具体解析逻辑"""
        raise NotImplementedError
    
    def get_start_urls(self):
        """获取起始URL列表"""
        raise NotImplementedError
    
    def get_url_priority(self, url):
        """URL优先级计算"""
        # 基于URL类型和重要性计算优先级
        if 'khan' in url:
            return 10
        elif 'oracle' in url:
            return 9
        elif 'codehs' in url:
            return 8
        else:
            return 5
```

#### Khan Academy 爬虫
```python
# scrapy_cluster/apcsa_scraper/spiders/khan_academy.py
from .base_spider import BaseAPCSASpider
from ..items import VideoItem
import json

class KhanAcademySpider(BaseAPCSASpider):
    name = 'khan_academy'
    allowed_domains = ['khanacademy.org']
    
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 3,
        'RETRY_TIMES': 3,
    }
    
    def get_start_urls(self):
        """获取Khan Academy起始URL"""
        base_paths = [
            '/computing/computer-programming/programming/intro-to-programming',
            '/computing/computer-programming/programming-java',
            '/computing/ap-computer-science-a'
        ]
        return [f'https://www.khanacademy.org{path}' for path in base_paths]
    
    def parse_content(self, response):
        """解析Khan Academy内容"""
        # 提取视频信息
        video_data = response.css('script[type="application/ld+json"]::text').get()
        if video_data:
            try:
                data = json.loads(video_data)
                if data.get('@type') == 'VideoObject':
                    item = VideoItem()
                    item['title'] = data.get('name')
                    item['description'] = data.get('description')
                    item['url'] = response.url
                    item['duration'] = self.parse_duration(data.get('duration'))
                    item['thumbnail_url'] = data.get('thumbnailUrl')
                    item['source_platform'] = 'khan_academy'
                    item['lesson_code'] = self.extract_lesson_code(response.url)
                    
                    self.scraped_items.labels(spider=self.name, item_type='video').inc()
                    yield item
            except json.JSONDecodeError:
                self.logger.error(f"Failed to parse JSON from {response.url}")
        
        # 提取下一页链接
        next_urls = response.css('a[href*="/v/"]::attr(href)').getall()
        for url in next_urls:
            if not url.startswith('http'):
                url = response.urljoin(url)
            yield scrapy.Request(url, callback=self.parse_content)
    
    def parse_duration(self, duration_str):
        """解析视频时长"""
        # 实现ISO 8601时长解析
        pass
    
    def extract_lesson_code(self, url):
        """从URL提取lesson代码"""
        # 实现lesson代码提取逻辑
        pass
```

### 2. Celery 任务队列

#### 任务定义
```python
# task_queue/tasks/scraping_tasks.py
from celery import Celery
from celery.utils.log import get_task_logger
from prometheus_client import Counter, Histogram
import requests
import time

logger = get_task_logger(__name__)

# Prometheus指标
task_counter = Counter('celery_tasks_total', 'Total tasks', ['task_name', 'status'])
task_duration = Histogram('celery_task_duration_seconds', 'Task duration', ['task_name'])

app = Celery('apcsa_scraper')

@app.task(bind=True, max_retries=3)
def start_spider_task(self, spider_name, **kwargs):
    """启动爬虫任务"""
    start_time = time.time()
    
    try:
        # 调用Scrapyd API启动爬虫
        scrapyd_url = "http://scrapyd:6800/schedule.json"
        data = {
            'project': 'apcsa_scraper',
            'spider': spider_name,
            **kwargs
        }
        
        response = requests.post(scrapyd_url, data=data)
        response.raise_for_status()
        
        result = response.json()
        job_id = result['jobid']
        
        # 记录成功指标
        task_counter.labels(task_name='start_spider', status='success').inc()
        duration = time.time() - start_time
        task_duration.labels(task_name='start_spider').observe(duration)
        
        logger.info(f"Started spider {spider_name} with job_id {job_id}")
        
        # 启动监控任务
        monitor_spider_task.delay(job_id, spider_name)
        
        return {'job_id': job_id, 'spider': spider_name, 'status': 'started'}
        
    except Exception as exc:
        task_counter.labels(task_name='start_spider', status='error').inc()
        logger.error(f"Failed to start spider {spider_name}: {exc}")
        
        # 重试逻辑
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying spider {spider_name} in 60 seconds...")
            raise self.retry(countdown=60, exc=exc)
        else:
            # 发送告警
            send_alert_task.delay(
                'spider_start_failed',
                f"Failed to start spider {spider_name} after {self.max_retries} retries"
            )
            raise

@app.task
def monitor_spider_task(job_id, spider_name):
    """监控爬虫执行状态"""
    try:
        # 查询爬虫状态
        status_url = f"http://scrapyd:6800/listjobs.json?project=apcsa_scraper"
        response = requests.get(status_url)
        response.raise_for_status()
        
        jobs = response.json()
        
        # 查找对应的job
        for status in ['running', 'pending', 'finished']:
            for job in jobs.get(status, []):
                if job['id'] == job_id:
                    if status == 'finished':
                        # 爬虫完成，处理结果
                        process_spider_results.delay(job_id, spider_name)
                        return
                    elif status == 'running':
                        # 继续监控
                        monitor_spider_task.apply_async(
                            args=[job_id, spider_name],
                            countdown=30
                        )
                        return
        
        # 如果没找到job，可能出错了
        send_alert_task.delay(
            'spider_lost',
            f"Lost track of spider {spider_name} job {job_id}"
        )
        
    except Exception as exc:
        logger.error(f"Failed to monitor spider {spider_name}: {exc}")
        send_alert_task.delay(
            'monitor_failed',
            f"Failed to monitor spider {spider_name}: {exc}"
        )

@app.task
def process_spider_results(job_id, spider_name):
    """处理爬虫结果"""
    try:
        # 获取爬虫日志和统计信息
        log_url = f"http://scrapyd:6800/logs/apcsa_scraper/{spider_name}/{job_id}.log"
        response = requests.get(log_url)
        
        if response.status_code == 200:
            log_content = response.text
            
            # 解析统计信息
            stats = parse_spider_stats(log_content)
            
            # 保存到数据库
            save_spider_stats.delay(job_id, spider_name, stats)
            
            # 发送完成通知
            if stats.get('item_scraped_count', 0) > 0:
                send_notification_task.delay(
                    'spider_completed',
                    f"Spider {spider_name} completed successfully. "
                    f"Scraped {stats['item_scraped_count']} items."
                )
            else:
                send_alert_task.delay(
                    'spider_no_items',
                    f"Spider {spider_name} completed but scraped no items"
                )
        
    except Exception as exc:
        logger.error(f"Failed to process results for {spider_name}: {exc}")
        send_alert_task.delay(
            'result_processing_failed',
            f"Failed to process results for spider {spider_name}: {exc}"
        )

@app.task
def send_alert_task(alert_type, message):
    """发送告警"""
    try:
        # 发送到AlertManager
        alert_data = {
            'alerts': [{
                'labels': {
                    'alertname': alert_type,
                    'service': 'apcsa_scraper',
                    'severity': 'warning'
                },
                'annotations': {
                    'summary': message,
                    'description': message
                }
            }]
        }
        
        response = requests.post(
            'http://alertmanager:9093/api/v1/alerts',
            json=alert_data
        )
        response.raise_for_status()
        
        logger.info(f"Sent alert: {alert_type} - {message}")
        
    except Exception as exc:
        logger.error(f"Failed to send alert: {exc}")

def parse_spider_stats(log_content):
    """解析爬虫统计信息"""
    stats = {}
    
    # 使用正则表达式提取统计信息
    import re
    
    patterns = {
        'item_scraped_count': r"'item_scraped_count': (\d+)",
        'request_count': r"'downloader/request_count': (\d+)",
        'response_count': r"'downloader/response_count': (\d+)",
        'item_dropped_count': r"'item_dropped_count': (\d+)",
    }
    
    for key, pattern in patterns.items():
        match = re.search(pattern, log_content)
        if match:
            stats[key] = int(match.group(1))
        else:
            stats[key] = 0
    
    return stats
```

#### Celery 配置
```python
# task_queue/celery_config.py
from kombu import Queue
import os

# Redis配置
BROKER_URL = os.getenv('REDIS_URL', 'redis://redis:6379/0')
CELERY_RESULT_BACKEND = os.getenv('REDIS_URL', 'redis://redis:6379/0')

# 任务路由
CELERY_ROUTES = {
    'tasks.scraping_tasks.start_spider_task': {'queue': 'scraping'},
    'tasks.monitoring_tasks.*': {'queue': 'monitoring'},
    'tasks.cleanup_tasks.*': {'queue': 'cleanup'},
}

# 队列定义
CELERY_QUEUES = (
    Queue('scraping', routing_key='scraping'),
    Queue('monitoring', routing_key='monitoring'),
    Queue('cleanup', routing_key='cleanup'),
    Queue('alerts', routing_key='alerts'),
)

# 任务配置
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TIMEZONE = 'UTC'
CELERY_ENABLE_UTC = True

# 重试配置
CELERY_TASK_ANNOTATIONS = {
    'tasks.scraping_tasks.start_spider_task': {
        'rate_limit': '10/m',
        'max_retries': 3,
        'default_retry_delay': 60,
    },
    'tasks.monitoring_tasks.*': {
        'rate_limit': '100/m',
    }
}

# Beat调度配置
CELERYBEAT_SCHEDULE = {
    'daily_full_scrape': {
        'task': 'tasks.scraping_tasks.start_full_scrape',
        'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点
    },
    'hourly_incremental_scrape': {
        'task': 'tasks.scraping_tasks.start_incremental_scrape',
        'schedule': crontab(minute=0),  # 每小时
    },
    'cleanup_old_data': {
        'task': 'tasks.cleanup_tasks.cleanup_old_data',
        'schedule': crontab(hour=3, minute=0),  # 每天凌晨3点
    },
    'health_check': {
        'task': 'tasks.monitoring_tasks.health_check',
        'schedule': 300.0,  # 每5分钟
    }
}
```

### 3. 监控系统

#### Prometheus 配置
```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'scrapy-cluster'
    static_configs:
      - targets: ['scrapyd1:6800', 'scrapyd2:6800', 'scrapyd3:6800']
    metrics_path: '/metrics'

  - job_name: 'celery-workers'
    static_configs:
      - targets: ['worker1:9540', 'worker2:9540', 'worker3:9540']

  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api1:8000', 'api2:8000', 'api3:8000']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:9121']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:9187']
```

#### 告警规则
```yaml
# monitoring/prometheus/alert_rules.yml
groups:
  - name: scrapy_alerts
    rules:
      - alert: SpiderDown
        expr: up{job="scrapy-cluster"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Scrapy spider is down"
          description: "Spider {{ $labels.instance }} has been down for more than 5 minutes"

      - alert: HighErrorRate
        expr: rate(scrapy_items_dropped_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate in spider"
          description: "Spider {{ $labels.spider }} has error rate > 10%"

      - alert: CeleryWorkerDown
        expr: up{job="celery-workers"} == 0
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "Celery worker is down"
          description: "Celery worker {{ $labels.instance }} is down"

      - alert: QueueBacklog
        expr: celery_queue_length > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Large queue backlog"
          description: "Queue {{ $labels.queue }} has {{ $value }} pending tasks"

      - alert: DatabaseConnectionFailed
        expr: postgres_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failed"
          description: "Cannot connect to PostgreSQL database"
```

### 4. 智能调度系统

```python
# task_queue/tasks/intelligent_scheduler.py
from celery import Celery
from datetime import datetime, timedelta
import redis
import json

class IntelligentScheduler:
    """智能调度器"""
    
    def __init__(self):
        self.redis_client = redis.Redis(host='redis', port=6379, db=1)
        self.celery_app = Celery('scheduler')
    
    def schedule_spider(self, spider_name, priority='normal'):
        """智能调度爬虫"""
        
        # 1. 检查网站负载
        site_load = self.check_site_load(spider_name)
        if site_load > 0.8:
            # 网站负载过高，延迟执行
            delay = self.calculate_delay(site_load)
            self.schedule_delayed_task(spider_name, delay)
            return
        
        # 2. 检查API限制
        api_limit = self.check_api_limits(spider_name)
        if api_limit['remaining'] < 10:
            # API配额不足，等待重置
            reset_time = api_limit['reset_time']
            delay = (reset_time - datetime.now()).total_seconds()
            self.schedule_delayed_task(spider_name, delay)
            return
        
        # 3. 检查系统资源
        system_load = self.check_system_resources()
        if system_load['cpu'] > 0.8 or system_load['memory'] > 0.8:
            # 系统负载过高，降低并发
            self.adjust_concurrency(spider_name, 'low')
        
        # 4. 选择最优节点
        best_node = self.select_best_node(spider_name)
        
        # 5. 启动任务
        self.start_spider_on_node(spider_name, best_node, priority)
    
    def check_site_load(self, spider_name):
        """检查目标网站负载"""
        site_mapping = {
            'khan_academy': 'khanacademy.org',
            'codehs': 'codehs.com',
            'youtube': 'youtube.com'
        }
        
        site = site_mapping.get(spider_name)
        if not site:
            return 0.0
        
        # 从Redis获取最近的响应时间
        key = f"site_load:{site}"
        recent_times = self.redis_client.lrange(key, 0, 10)
        
        if not recent_times:
            return 0.0
        
        # 计算平均响应时间
        avg_time = sum(float(t) for t in recent_times) / len(recent_times)
        
        # 转换为负载指标 (0-1)
        # 假设正常响应时间为1秒，超过5秒认为负载过高
        load = min(avg_time / 5.0, 1.0)
        return load
    
    def check_api_limits(self, spider_name):
        """检查API限制"""
        api_keys = {
            'youtube': 'YOUTUBE_API_KEY',
            'khan_academy': 'KHAN_API_KEY'
        }
        
        if spider_name not in api_keys:
            return {'remaining': 1000, 'reset_time': datetime.now() + timedelta(hours=1)}
        
        # 从Redis获取API使用情况
        key = f"api_usage:{spider_name}"
        usage_data = self.redis_client.get(key)
        
        if usage_data:
            usage = json.loads(usage_data)
            return usage
        else:
            # 默认值
            return {'remaining': 1000, 'reset_time': datetime.now() + timedelta(hours=1)}
    
    def check_system_resources(self):
        """检查系统资源"""
        import psutil
        
        return {
            'cpu': psutil.cpu_percent(interval=1) / 100.0,
            'memory': psutil.virtual_memory().percent / 100.0,
            'disk': psutil.disk_usage('/').percent / 100.0
        }
    
    def select_best_node(self, spider_name):
        """选择最优节点"""
        nodes = ['scrapyd1', 'scrapyd2', 'scrapyd3']
        node_loads = {}
        
        for node in nodes:
            # 获取节点负载信息
            key = f"node_load:{node}"
            load_data = self.redis_client.get(key)
            
            if load_data:
                load = json.loads(load_data)
                node_loads[node] = load['total_load']
            else:
                node_loads[node] = 0.0
        
        # 选择负载最低的节点
        best_node = min(node_loads, key=node_loads.get)
        return best_node
    
    def calculate_delay(self, load):
        """根据负载计算延迟时间"""
        # 负载越高，延迟越长
        base_delay = 60  # 基础延迟60秒
        max_delay = 3600  # 最大延迟1小时
        
        delay = base_delay * (load * 10)
        return min(delay, max_delay)
    
    def adjust_concurrency(self, spider_name, level):
        """调整并发级别"""
        concurrency_settings = {
            'low': {'concurrent_requests': 1, 'download_delay': 5},
            'normal': {'concurrent_requests': 3, 'download_delay': 2},
            'high': {'concurrent_requests': 8, 'download_delay': 1}
        }
        
        settings = concurrency_settings[level]
        
        # 更新爬虫设置
        key = f"spider_settings:{spider_name}"
        self.redis_client.setex(key, 3600, json.dumps(settings))
```

---

## 🚀 迁移实施计划

### Phase 1: 基础设施搭建 (2周)

**Week 1:**
- [ ] 搭建Redis集群
- [ ] 部署Celery集群
- [ ] 配置Prometheus监控
- [ ] 搭建Grafana仪表板

**Week 2:**
- [ ] 部署Scrapyd集群
- [ ] 配置Nginx负载均衡
- [ ] 搭建AlertManager
- [ ] 配置日志聚合

### Phase 2: 爬虫迁移 (3周)

**Week 3:**
- [ ] 重构Khan Academy爬虫
- [ ] 重构CodeHS爬虫
- [ ] 实现数据管道

**Week 4:**
- [ ] 重构YouTube爬虫
- [ ] 重构文档爬虫
- [ ] 实现去重和验证

**Week 5:**
- [ ] 集成测试
- [ ] 性能调优
- [ ] 错误处理完善

### Phase 3: 智能调度 (2周)

**Week 6:**
- [ ] 实现智能调度器
- [ ] 配置自动扩缩容
- [ ] 实现负载均衡

**Week 7:**
- [ ] 实现故障恢复
- [ ] 配置告警规则
- [ ] 性能优化

### Phase 4: API网关增强 (1周)

**Week 8:**
- [ ] FastAPI集群部署
- [ ] API版本管理
- [ ] 限流和认证
- [ ] 文档更新

### Phase 5: 测试和上线 (1周)

**Week 9:**
- [ ] 压力测试
- [ ] 故障演练
- [ ] 数据迁移
- [ ] 生产部署

---

## 📊 性能指标

### 目标性能

| 指标 | 当前 | 目标 | 提升 |
|------|------|------|------|
| 并发抓取任务 | 10 | 1000+ | 100x |
| 系统可用性 | 95% | 99.9% | 5% |
| 故障恢复时间 | 30分钟 | 2分钟 | 15x |
| 数据处理延迟 | 10分钟 | 30秒 | 20x |
| 错误率 | 5% | <0.1% | 50x |

### 监控指标

- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: 任务成功率、处理时长、队列长度
- **业务指标**: 抓取成功率、数据质量、覆盖率
- **用户指标**: API响应时间、错误率、可用性

---

## 💰 成本分析

### 基础设施成本 (月)

| 组件 | 规格 | 数量 | 单价 | 小计 |
|------|------|------|------|------|
| API Gateway | 2C4G | 3 | $50 | $150 |
| Scrapy Nodes | 4C8G | 3 | $100 | $300 |
| Celery Workers | 2C4G | 3 | $50 | $150 |
| Redis Cluster | 2C4G | 3 | $60 | $180 |
| Monitoring | 2C4G | 2 | $50 | $100 |
| Load Balancer | 1C2G | 1 | $30 | $30 |
| **总计** | | | | **$910** |

### 开发成本

- **架构设计**: 1周 × $2000 = $2000
- **开发实施**: 8周 × $3000 = $24000
- **测试部署**: 1周 × $2000 = $2000
- **总计**: $28000

### ROI分析

- **性能提升**: 100x并发能力
- **运维成本降低**: 自动化程度提升80%
- **故障时间减少**: 从30分钟降至2分钟
- **预计年节省**: $50000+

---

## 🔒 安全考虑

### 网络安全
- VPC隔离
- 防火墙规则
- SSL/TLS加密
- API密钥管理

### 数据安全
- 数据加密存储
- 访问权限控制
- 审计日志
- 备份策略

### 应用安全
- 输入验证
- SQL注入防护
- XSS防护
- 限流保护

---

## 📝 总结

这个分布式架构迁移方案将显著提升APCSA平台的：

1. **可扩展性**: 支持1000+并发抓取任务
2. **可靠性**: 99.9%系统可用性
3. **智能化**: 自动调度和故障恢复
4. **可观测性**: 全方位监控和告警

通过9周的分阶段实施，可以平滑迁移到新架构，同时保证业务连续性。投资回报率高，长期运维成本显著降低。 