# 🎓 APCSA AI Learning Platform

> 基于 AI 的 AP 计算机科学 A 智能学习平台

## 🏗️ 项目架构

```
apcsa-platform/
├── frontend/              # React + TypeScript 前端
├── backend/               # FastAPI + Python 后端
├── nginx/                 # 反向代理配置
├── scripts/               # 便捷脚本
├── docker-compose.yml     # 开发环境
├── docker-compose.prod.yml # 生产环境
└── .env.example           # 环境变量模板
```

## 🚀 技术栈

### 前端
- **React 19** + TypeScript
- **TailwindCSS** + Framer Motion
- **React Router v7**
- **Zustand** 状态管理
- **Axios** + React Query

### 后端
- **FastAPI** + Python 3.11
- **PostgreSQL 15** 主数据库
- **Redis 7** 缓存和会话
- **SQLAlchemy 2.0** ORM
- **Alembic** 数据库迁移

### DevOps
- **Docker** + Docker Compose
- **Nginx** 反向代理
- **GitHub Actions** CI/CD

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd apcsa-platform
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（重要！）
nano .env
```

### 3. 启动开发环境
```bash
# 使用便捷脚本（推荐）
./scripts/dev.sh start

# 或直接使用 Docker Compose
docker-compose up -d
```

### 4. 访问应用
- 🌐 **前端**: http://localhost:3000
- 🔧 **后端 API**: http://localhost:8000
- 📚 **API 文档**: http://localhost:8000/docs
- 🗄️ **数据库管理**: http://localhost:5050

## 📋 开发命令

```bash
# 启动开发环境
./scripts/dev.sh start

# 查看日志
./scripts/dev.sh logs
./scripts/dev.sh logs backend  # 查看特定服务日志

# 停止环境
./scripts/dev.sh stop

# 重启环境
./scripts/dev.sh restart

# 数据库迁移
./scripts/dev.sh migrate

# 重置数据库
./scripts/dev.sh reset-db

# 清理所有容器和数据
./scripts/dev.sh clean
```

## 🔧 环境变量配置

关键环境变量说明：

```bash
# 数据库配置
POSTGRES_DB=apcsa_platform
POSTGRES_USER=apcsa_user
POSTGRES_PASSWORD=your_secure_password

# AI 服务
OPENAI_API_KEY=your_openai_api_key

# 安全密钥
SECRET_KEY=your_super_secret_key
JWT_SECRET_KEY=your_jwt_secret_key

# 端口配置
FRONTEND_PORT=3000
BACKEND_PORT=8000
```

## 🏭 生产部署

### 1. 服务器准备
```bash
# 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 克隆项目
git clone <your-repo-url>
cd apcsa-platform
```

### 2. 生产环境配置
```bash
# 配置生产环境变量
cp .env.example .env.prod
nano .env.prod

# 配置 SSL 证书
mkdir -p nginx/ssl
# 将证书文件放入 nginx/ssl/ 目录
```

### 3. 启动生产环境
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📊 监控和日志

```bash
# 查看所有服务状态
docker-compose ps

# 查看资源使用情况
docker stats

# 查看日志
docker-compose logs -f [service_name]

# 数据库备份
docker-compose exec postgres pg_dump -U apcsa_user apcsa_platform > backup.sql
```

## 🧪 测试

```bash
# 前端测试
cd frontend
npm test

# 后端测试
docker-compose exec backend pytest

# 端到端测试
npm run test:e2e
```

## 🔒 安全配置

### 生产环境安全检查清单：
- [ ] 更改所有默认密码
- [ ] 配置 SSL 证书
- [ ] 设置防火墙规则
- [ ] 启用日志监控
- [ ] 配置备份策略
- [ ] 设置环境变量保护

## 📈 性能优化

### 前端优化：
- 代码分割和懒加载
- 图片压缩和 CDN
- Service Worker 缓存

### 后端优化：
- 数据库索引优化
- Redis 缓存策略
- API 响应压缩

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 故障排除

### 常见问题：

**Q: 前端无法连接后端？**
A: 检查 `REACT_APP_API_URL` 环境变量是否正确设置。

**Q: 数据库连接失败？**
A: 确保 PostgreSQL 容器已启动，检查数据库凭据。

**Q: Docker 构建失败？**
A: 清理 Docker 缓存：`docker system prune -a`

**Q: 端口冲突？**
A: 修改 `.env` 文件中的端口配置。

## 📞 支持

如有问题，请：
1. 查看 [Issues](https://github.com/your-repo/issues)
2. 创建新的 Issue
3. 联系开发团队
