# 🎓 APCSA AI Learning Platform

> 专为AP Computer Science A课程设计的智能学习平台，结合AI自动化与教师手动管理。

![Platform Preview](https://via.placeholder.com/800x400?text=APCSA+AI+Platform+Preview)

## ✨ 核心特性

🤖 **AI驱动学习**
- 自动出题和智能判分
- 个性化学习建议
- 实时学习数据分析

📚 **完整APCSA课程**
- 覆盖全部10个单元
- 基于College Board标准
- 丰富的练习题库

💻 **在线编程环境**
- 内置Java IDE
- 实时编译运行
- 代码自动评测

📊 **学习分析系统**
- 详细进度追踪
- 智能复习提醒
- 学习效果可视化

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Python 3.11+
- Poetry (Python包管理)

### 一键安装
```bash
# 克隆项目
git clone https://github.com/your-username/apcsa-ai-platform.git
cd apcsa-ai-platform

# 安装所有依赖
npm run install:all

# 配置环境变量
cp env.example .env

# 启动开发服务器
npm run dev
```

### 访问应用
- 🌐 **前端**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8000
- 📖 **API文档**: http://localhost:8000/docs

## 🛠️ 技术栈

### 前端
![React](https://img.shields.io/badge/React-18-blue?logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?logo=typescript)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3-blue?logo=tailwindcss)

### 后端
![FastAPI](https://img.shields.io/badge/FastAPI-0.104-green?logo=fastapi)
![Python](https://img.shields.io/badge/Python-3.11-green?logo=python)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15-green?logo=postgresql)

### AI & 云服务
![OpenAI](https://img.shields.io/badge/OpenAI-GPT--4-orange?logo=openai)
![Google](https://img.shields.io/badge/Google-Gemini-orange?logo=google)
![Supabase](https://img.shields.io/badge/Supabase-DB-orange?logo=supabase)

## 💻 平台支持

| 平台 | 开发环境 | 生产部署 | 状态 |
|------|----------|----------|------|
| **Windows** | ✅ 完全支持 | ✅ 支持 | 🟢 Ready |
| **macOS Intel** | ✅ 完全支持 | ✅ 支持 | 🟢 Ready |
| **macOS Apple Silicon** | ✅ 原生支持 | ✅ 跨平台部署 | 🟢 Ready |
| **Linux** | ✅ 完全支持 | ✅ 推荐 | 🟢 Ready |

### Mac M1/M2 用户
对于Apple Silicon用户，我们提供了特殊优化：
- 🚀 [Mac M1 开发指南](MAC_M1_SETUP.md) - 原生ARM64支持
- 🌍 [跨平台部署指南](CROSS_PLATFORM_DEPLOYMENT.md) - M1 → Ubuntu服务器

## 📖 文档

| 文档类型 | 链接 | 描述 |
|----------|------|------|
| 📚 **文档中心** | [docs/](docs/README.md) | 完整文档导航 |
| 🚀 **快速开始** | [GETTING_STARTED.md](GETTING_STARTED.md) | 详细设置步骤 |
| 🏗️ **项目设计** | [docs/PROJECT_DESIGN.md](docs/PROJECT_DESIGN.md) | 功能设计说明 |
| 🔧 **开发计划** | [DEVELOPMENT_PLAN.md](DEVELOPMENT_PLAN.md) | 开发路线图 |
| 🗄️ **数据库设计** | [database/schema.sql](database/schema.sql) | 完整数据结构 |

## 📁 项目结构

```
apcsa-ai-platform/
├── 📱 frontend/          # React 前端应用
├── 🔧 backend/           # FastAPI 后端服务
├── 🗄️ database/          # 数据库设计和种子数据
├── 📚 docs/             # 完整项目文档
├── 🚀 deploy/           # 部署配置文件
├── 🔨 scripts/          # 自动化脚本
└── 📄 各种配置文件
```

## 🗓️ 开发路线图

- [x] **Phase 1** - 项目架构和基础设施 ✅
- [ ] **Phase 2** - 用户认证和内容管理 🔄
- [ ] **Phase 3** - AI集成和智能功能 📅
- [ ] **Phase 4** - 高级功能和优化 📅

查看详细的 [开发计划](DEVELOPMENT_PLAN.md)

## 🤝 贡献

我们欢迎所有形式的贡献！

1. 🍴 Fork 项目
2. 🌟 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 📝 提交更改 (`git commit -m 'Add amazing feature'`)
4. 📤 推送分支 (`git push origin feature/amazing-feature`)
5. 🔄 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🌟 Star History

[![Star History Chart](https://api.star-history.com/svg?repos=your-username/apcsa-ai-platform&type=Date)](https://star-history.com/#your-username/apcsa-ai-platform&Date)

## 📞 联系我们

- 🐛 **报告Bug** → [GitHub Issues](https://github.com/your-username/apcsa-ai-platform/issues)
- 💡 **功能建议** → [GitHub Discussions](https://github.com/your-username/apcsa-ai-platform/discussions)
- 📧 **邮件联系** → <EMAIL>
- 💬 **社区交流** → [Discord](https://discord.gg/your-server)

---

<div align="center">

**让AI赋能编程教育，让学习更高效！** 🎓✨

[开始使用](GETTING_STARTED.md) · [查看文档](docs/README.md) · [贡献代码](#🤝-贡献)

</div> 