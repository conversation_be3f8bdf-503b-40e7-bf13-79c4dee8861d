# 🕷️ APCSA 学习平台抓取系统整理报告

## 📋 系统概述

当前项目**没有使用Scrapy框架**，而是基于自定义的抓取服务架构，主要使用以下技术栈：

- **主要抓取框架**: 自定义异步抓取服务 (基于 aiohttp)
- **外部API集成**: Firecrawl API, YouTube API, Khan Academy API
- **内容聚合**: 多平台内容统一管理
- **数据存储**: PostgreSQL (通过Supabase)

---

## 🏗️ 抓取架构概览

### 核心抓取服务
```
backend/
├── content_scraper_service.py      # 🎯 核心抓取服务
├── lesson_content_service.py       # 📚 课程内容服务  
├── external_content_service.py     # 🌐 外部内容聚合服务
├── firecrawl_service.py           # 🔥 Firecrawl API 集成
└── resource_manager.py            # 📁 资源管理服务
```

### 抓取流程架构
```mermaid
graph TD
    A[ContentScraperService] --> B[Khan Academy Videos]
    A --> C[CodeHS Notes]
    A --> D[YouTube Videos]
    A --> E[AI Generated Questions]
    
    F[ExternalContentService] --> G[Educational Articles]
    F --> H[Interactive Exercises]
    
    I[FirecrawlService] --> J[Oracle Documentation]
    I --> K[GeeksforGeeks]
    I --> L[TutorialsPoint]
    
    B --> M[Database Storage]
    C --> M
    D --> M
    E --> M
    G --> M
    H --> M
    J --> M
    K --> M
    L --> M
```

---

## 🎯 抓取目标与URL配置

### 1. Khan Academy 视频内容

**基础URL**: `https://www.khanacademy.org`

**抓取目标**:
```python
# 主要课程路径
khan_academy_paths = {
    "intro": "/computing/computer-programming/programming/intro-to-programming/v/programming-intro",
    "java_intro": "/computing/computer-programming/programming-java/intro-to-java/v/java-intro", 
    "variables": "/computing/computer-programming/programming-java/variables-data-types/v/java-variables",
    "expressions": "/computing/computer-programming/programming-java/expressions/v/java-expressions",
    "objects": "/computing/computer-programming/programming-java/objects-classes/v/java-objects",
    "boolean": "/computing/computer-programming/programming-java/boolean/v/boolean-expressions",
    "if_statements": "/computing/computer-programming/programming-java/if-statements/v/if-statements",
    "while_loops": "/computing/computer-programming/programming-java/while-loops/v/while-loops",
    "for_loops": "/computing/computer-programming/programming-java/for-loops/v/for-loops"
}
```

**内容类型**: 教学视频、嵌入代码、字幕、元数据

### 2. CodeHS 教科书内容

**基础URL**: `https://codehs.com/textbook/apcsa_textbook/`

**抓取目标**:
```python
# 按单元和课程结构化抓取
codehs_lessons = {
    "1.1": "https://codehs.com/textbook/apcsa_textbook/1/1",  # 编程介绍
    "1.2": "https://codehs.com/textbook/apcsa_textbook/1/2",  # 变量和数据类型
    "1.3": "https://codehs.com/textbook/apcsa_textbook/1/3",  # 表达式和赋值
    "1.4": "https://codehs.com/textbook/apcsa_textbook/1/4",  # 复合赋值操作符
    "1.5": "https://codehs.com/textbook/apcsa_textbook/1/5",  # 类型转换和范围
    "1.6": "https://codehs.com/textbook/apcsa_textbook/1/6",  # 变量作用域
    "2.1": "https://codehs.com/textbook/apcsa_textbook/2/1",  # 对象：类的实例
    "2.2": "https://codehs.com/textbook/apcsa_textbook/2/2",  # 创建和存储对象
    # ... 继续到 10.2
}
```

**内容类型**: 结构化教材、代码示例、练习题、概念解释

### 3. YouTube 教育内容

**API端点**: `https://www.googleapis.com/youtube/v3/search`

**目标频道**:
```python
educational_channels = [
    "UCeVMnSShP_Iviwkknt83cww",  # CS Dojo
    "UCWr0mx597DnSGLFk1WfvSkQ",  # CS50
    "UCXAHpX2xDhmjqtA-ANgsGmw",  # CodingBat  
    "UCErssr3CrjV8DLMfI-TGnTQ"   # Computer Science
]
```

**搜索查询模式**:
```python
search_patterns = [
    "APCSA {lesson_title} Java tutorial",
    "AP Computer Science A {topic}",
    "Java {keywords} explained",
    "Programming {concept} tutorial"
]
```

### 4. 官方文档和教程

**Oracle Java 文档**:
```python
oracle_base = "https://docs.oracle.com/javase/tutorial/java/"
oracle_paths = {
    "datatypes": "nutsandbolts/datatypes.html",
    "variables": "nutsandbolts/variables.html", 
    "objects": "concepts/object.html",
    "expressions": "nutsandbolts/expressions.html"
}
```

**GeeksforGeeks**:
```python
gfg_base = "https://www.geeksforgeeks.org/"
gfg_patterns = [
    "data-types-in-java/",
    "variables-in-java/",
    "operators-in-java/",
    "control-flow-in-java/"
]
```

**TutorialsPoint**:
```python
tp_base = "https://www.tutorialspoint.com/java/"
tp_patterns = [
    "java_basic_datatypes.htm",
    "java_variable_types.htm",
    "java_basic_operators.htm"
]
```

### 5. 在线练习平台

**CodeHS 课程**:
```python
codehs_courses = [
    "https://codehs.com/course/apcsa/lesson/1.1",
    "https://codehs.com/course/apcsa/lesson/1.2"
]
```

**HackerRank**:
```python
hackerrank_domains = [
    "https://www.hackerrank.com/domains/java"
]
```

**LeetCode**:
```python
leetcode_easy = "https://leetcode.com/problemset/all/?difficulty=Easy"
```

---

## 🛠️ 当前抓取实现

### 1. ContentScraperService 核心功能

```python
class ContentScraperService:
    async def scrape_all_content_for_lesson(self, lesson_code, unit_id, topic_id):
        """为指定lesson抓取所有类型内容"""
        # 并行抓取多种内容
        - Khan Academy 视频
        - CodeHS 笔记
        - YouTube 教育视频  
        - AI 生成的练习题
```

### 2. 数据库存储结构

```sql
-- 主要内容表
learning_content (
    id, unit_id, topic_id, lesson_code,
    title, description, content_type, 
    source_platform, source_url,
    quality_score, is_verified
)

-- 视频专用表  
video_content (
    learning_content_id, embed_code,
    duration_seconds, channel_name,
    thumbnail_url
)

-- 笔记专用表
note_content (
    learning_content_id, markdown_content,
    sections, code_examples
)

-- 问题专用表
question_content (
    learning_content_id, question_type,
    question_data, correct_answer
)
```

### 3. API端点

```python
# 内容抓取 API
POST /api/content/scrape/lesson/{lesson_code}     # 抓取单个lesson
POST /api/content/scrape/all-lessons              # 批量抓取所有lesson  
POST /api/content/scrape/unit/{unit_id}           # 抓取整个单元

# 内容查看 API
GET  /api/content/content                         # 获取内容列表
GET  /api/content/content/{id}                    # 获取内容详情
GET  /api/content/lesson/{lesson_code}/content    # 获取lesson所有内容
```

---

## 📊 抓取状态和进度

### 已完成的抓取内容

根据 `CODEHS_SCRAPING_REPORT.md`:

```
✅ Unit 1 内容抓取完成
├── 📹 Khan Academy视频: 5个
├── 📝 CodeHS笔记: 3个  
├── 📄 CodeHS文档: 4个
├── 🧪 Quiz: 3个
└── 📋 Assignment: 3个

总计: 18个学习资源
```

### 待抓取内容

```
🔄 Units 2-10 (待抓取)
├── 预计资源数: ~162个 (18 * 9个单元)
├── 预计视频: ~45个
├── 预计笔记: ~27个
└── 预计练习: ~90个
```

---

## 🚀 迁移到Scrapy的建议

如果要迁移到Scrapy框架，建议以下架构：

### 1. Scrapy项目结构
```
scrapy_apcsa/
├── scrapy.cfg
├── apcsa_scraper/
│   ├── __init__.py
│   ├── items.py           # 数据结构定义
│   ├── pipelines.py       # 数据处理管道
│   ├── settings.py        # 抓取配置
│   ├── middlewares.py     # 中间件
│   └── spiders/
│       ├── __init__.py
│       ├── khan_academy.py    # Khan Academy 爬虫
│       ├── codehs.py          # CodeHS 爬虫
│       ├── youtube.py         # YouTube 爬虫
│       └── documentation.py   # 文档站点爬虫
```

### 2. 数据结构定义
```python
# items.py
class VideoItem(scrapy.Item):
    lesson_code = scrapy.Field()
    title = scrapy.Field()
    url = scrapy.Field()
    embed_code = scrapy.Field()
    duration = scrapy.Field()
    description = scrapy.Field()

class NoteItem(scrapy.Item):
    lesson_code = scrapy.Field()
    title = scrapy.Field()
    content = scrapy.Field()
    sections = scrapy.Field()
```

### 3. 数据管道配置
```python
# pipelines.py
class DatabasePipeline:
    def process_item(self, item, spider):
        # 保存到 PostgreSQL 数据库
        pass

class DuplicatesPipeline:
    def process_item(self, item, spider):
        # 去重处理
        pass
```

---

## 🔧 推荐的改进方案

### 短期改进 (保持当前架构)

1. **增强错误处理**
   ```python
   async def safe_scrape_with_retry(self, url, max_retries=3):
       for attempt in range(max_retries):
           try:
               return await self.scrape_content(url)
           except Exception as e:
               if attempt == max_retries - 1:
                   raise e
               await asyncio.sleep(2 ** attempt)
   ```

2. **添加进度跟踪**
   ```python
   async def track_scraping_progress(self, lesson_codes):
       total = len(lesson_codes)
       for i, lesson_code in enumerate(lesson_codes):
           progress = (i + 1) / total * 100
           print(f"进度: {progress:.1f}% ({i+1}/{total})")
   ```

3. **内容质量验证**
   ```python
   def validate_content_quality(self, content):
       score = 0
       if len(content.get('description', '')) > 50:
           score += 20
       if content.get('duration', 0) > 300:  # 5分钟以上
           score += 30
       return score
   ```

### 长期改进 (迁移到Scrapy)

1. **分布式抓取**: 使用 Scrapy-Redis 实现分布式抓取
2. **智能调度**: 基于网站负载和API限制的智能调度
3. **内容监控**: 定期检查内容更新和失效链接
4. **缓存优化**: 使用 Redis 缓存已抓取内容

---

## 📝 总结

当前系统特点：
- ✅ **异步高效**: 基于 aiohttp 的异步抓取
- ✅ **多源整合**: 支持多个教育平台
- ✅ **结构化存储**: 统一的数据库模式
- ✅ **API就绪**: 完整的RESTful API

改进空间：
- 🔄 **可扩展性**: 当前架构在大规模抓取时可能遇到瓶颈
- 🔄 **容错性**: 需要更强的错误处理和重试机制
- 🔄 **监控性**: 缺乏实时监控和告警机制

**建议**: 当前架构适合中小规模抓取需求，如需要企业级的抓取能力，可考虑迁移到Scrapy+Redis+Celery的分布式架构。 