# 🌐 APCSA 抓取系统 URL 清单

## 📋 概述

本文档详细记录了APCSA学习平台抓取系统中配置的所有URL地址，按平台和内容类型进行分类整理。

---

## 🎯 主要抓取目标平台

### 1. Khan Academy (主要视频内容)

**基础域名**: `www.khanacademy.org`

#### Unit 1 - Primitive Types
```
https://www.khanacademy.org/computing/computer-programming/programming/intro-to-programming/v/programming-intro
https://www.khanacademy.org/computing/computer-programming/programming-java/intro-to-java/v/java-intro
https://www.khanacademy.org/computing/computer-programming/programming-java/variables-data-types/v/java-variables
https://www.khanacademy.org/computing/computer-programming/programming-java/primitive-types/v/primitive-types
https://www.khanacademy.org/computing/computer-programming/programming-java/expressions/v/java-expressions
```

#### Unit 2 - Using Objects
```
https://www.khanacademy.org/computing/computer-programming/programming-java/objects-classes/v/java-objects
https://www.khanacademy.org/computing/computer-programming/programming-java/creating-objects/v/creating-objects
https://www.khanacademy.org/computing/computer-programming/programming-java/calling-methods/v/calling-methods
```

#### Unit 3 - Boolean Expressions and if Statements
```
https://www.khanacademy.org/computing/computer-programming/programming-java/boolean/v/boolean-expressions
https://www.khanacademy.org/computing/computer-programming/programming-java/if-statements/v/if-statements
```

#### Unit 4 - Iteration
```
https://www.khanacademy.org/computing/computer-programming/programming-java/while-loops/v/while-loops
https://www.khanacademy.org/computing/computer-programming/programming-java/for-loops/v/for-loops
```

---

### 2. CodeHS 教科书内容

**基础域名**: `codehs.com`

#### 教科书页面 (Unit 1-10)
```
https://codehs.com/textbook/apcsa_textbook/1/1    # 1.1 - Why Programming? Why Java?
https://codehs.com/textbook/apcsa_textbook/1/2    # 1.2 - Variables and Data Types
https://codehs.com/textbook/apcsa_textbook/1/3    # 1.3 - Expressions and Assignment
https://codehs.com/textbook/apcsa_textbook/1/4    # 1.4 - Compound Assignment Operators
https://codehs.com/textbook/apcsa_textbook/1/5    # 1.5 - Casting and Ranges of Variables
https://codehs.com/textbook/apcsa_textbook/1/6    # 1.6 - Variable Scope

https://codehs.com/textbook/apcsa_textbook/2/1    # 2.1 - Objects: Instances of Classes
https://codehs.com/textbook/apcsa_textbook/2/2    # 2.2 - Creating and Storing Objects
https://codehs.com/textbook/apcsa_textbook/2/3    # 2.3 - Calling a Void Method
https://codehs.com/textbook/apcsa_textbook/2/4    # 2.4 - Calling a Void Method with Parameters
https://codehs.com/textbook/apcsa_textbook/2/5    # 2.5 - Calling a Non-void Method
https://codehs.com/textbook/apcsa_textbook/2/6    # 2.6 - String Objects
https://codehs.com/textbook/apcsa_textbook/2/7    # 2.7 - String Methods
https://codehs.com/textbook/apcsa_textbook/2/8    # 2.8 - Wrapper Classes
https://codehs.com/textbook/apcsa_textbook/2/9    # 2.9 - Using the Math Class

# ... 继续到 10/2
```

#### 课程交互页面
```
https://codehs.com/course/apcsa/lesson/1.1
https://codehs.com/course/apcsa/lesson/1.2
# ... 等等
```

---

### 3. 官方文档和教程

#### Oracle Java 官方文档
**基础域名**: `docs.oracle.com`

```
https://docs.oracle.com/javase/tutorial/java/nutsandbolts/datatypes.html
https://docs.oracle.com/javase/tutorial/java/nutsandbolts/variables.html
https://docs.oracle.com/javase/tutorial/java/concepts/object.html
https://docs.oracle.com/javase/tutorial/java/nutsandbolts/expressions.html
https://docs.oracle.com/javase/tutorial/java/nutsandbolts/operators.html
https://docs.oracle.com/javase/tutorial/java/nutsandbolts/controlflow.html
```

#### GeeksforGeeks 教程
**基础域名**: `www.geeksforgeeks.org`

```
https://www.geeksforgeeks.org/data-types-in-java/
https://www.geeksforgeeks.org/variables-in-java/
https://www.geeksforgeeks.org/operators-in-java/
https://www.geeksforgeeks.org/control-flow-in-java/
https://www.geeksforgeeks.org/object-oriented-programming-oops-concept-in-java/
```

#### TutorialsPoint 教程
**基础域名**: `www.tutorialspoint.com`

```
https://www.tutorialspoint.com/java/java_basic_datatypes.htm
https://www.tutorialspoint.com/java/java_variable_types.htm
https://www.tutorialspoint.com/java/java_basic_operators.htm
https://www.tutorialspoint.com/java/java_loop_control.htm
```

---

### 4. 在线练习平台

#### HackerRank
```
https://www.hackerrank.com/domains/java
https://www.hackerrank.com/domains/algorithms
```

#### LeetCode
```
https://leetcode.com/problemset/all/?difficulty=Easy&page=1&sorting=W3sic29ydE9yZGVyIjoiREVTQ0VORElORyIsIm9yZGVyQnkiOiJGUkVRVUVOQ1kifV0%3D
```

---

### 5. YouTube 教育内容

#### API 端点
```
https://www.googleapis.com/youtube/v3/search
```

#### 目标教育频道和视频
```
# CS Dojo
https://www.youtube.com/watch?v=eIrMbAQSU34    # Java Tutorial for Beginners

# Java Programming
https://www.youtube.com/watch?v=A74TOX803D0    # Java Variables and Data Types

# Programming with Mosh  
https://www.youtube.com/watch?v=wxznTynyUiQ    # Java for Beginners

# Coding with John
https://www.youtube.com/watch?v=Qgl81fPcLc8    # Java Objects and Classes

# Derek Banas
https://www.youtube.com/watch?v=xk4_1vDrzzo    # Java Control Structures
```

---

## 🔧 API 端点和配置

### Supabase 数据库
```
https://gfbfgiboyqkhgktflanr.supabase.co
```

### Firecrawl API
```
https://api.firecrawl.dev
```

### YouTube Data API v3
```
https://www.googleapis.com/youtube/v3/search
```

---

## 🎯 抓取配置映射

### Khan Academy 内容映射

```python
KHAN_ACADEMY_MAPPING = {
    "1.1": {
        "intro": "/computing/computer-programming/programming/intro-to-programming/v/programming-intro",
        "java_intro": "/computing/computer-programming/programming-java/intro-to-java/v/java-intro"
    },
    "1.2": {
        "variables": "/computing/computer-programming/programming-java/variables-data-types/v/java-variables",
        "primitives": "/computing/computer-programming/programming-java/primitive-types/v/primitive-types"
    },
    "1.3": {
        "expressions": "/computing/computer-programming/programming-java/expressions/v/java-expressions"
    },
    "2.1": {
        "objects": "/computing/computer-programming/programming-java/objects-classes/v/java-objects"
    },
    "2.2": {
        "creating_objects": "/computing/computer-programming/programming-java/creating-objects/v/creating-objects"
    },
    "2.3": {
        "calling_methods": "/computing/computer-programming/programming-java/calling-methods/v/calling-methods"
    },
    "3.1": {
        "boolean": "/computing/computer-programming/programming-java/boolean/v/boolean-expressions"
    },
    "3.2": {
        "if_statements": "/computing/computer-programming/programming-java/if-statements/v/if-statements"
    },
    "4.1": {
        "while_loops": "/computing/computer-programming/programming-java/while-loops/v/while-loops"
    },
    "4.2": {
        "for_loops": "/computing/computer-programming/programming-java/for-loops/v/for-loops"
    }
}
```

### CodeHS 课程结构映射

```python
CODEHS_LESSONS = {
    "1.1": "https://codehs.com/textbook/apcsa_textbook/1/1",
    "1.2": "https://codehs.com/textbook/apcsa_textbook/1/2",
    "1.3": "https://codehs.com/textbook/apcsa_textbook/1/3",
    "1.4": "https://codehs.com/textbook/apcsa_textbook/1/4",
    "1.5": "https://codehs.com/textbook/apcsa_textbook/1/5",
    "1.6": "https://codehs.com/textbook/apcsa_textbook/1/6",
    "2.1": "https://codehs.com/textbook/apcsa_textbook/2/1",
    "2.2": "https://codehs.com/textbook/apcsa_textbook/2/2",
    "2.3": "https://codehs.com/textbook/apcsa_textbook/2/3",
    "2.4": "https://codehs.com/textbook/apcsa_textbook/2/4",
    "2.5": "https://codehs.com/textbook/apcsa_textbook/2/5",
    "2.6": "https://codehs.com/textbook/apcsa_textbook/2/6",
    "2.7": "https://codehs.com/textbook/apcsa_textbook/2/7",
    "2.8": "https://codehs.com/textbook/apcsa_textbook/2/8",
    "2.9": "https://codehs.com/textbook/apcsa_textbook/2/9",
    # ... 继续到 10.2
}
```

### 文档站点模式匹配

```python
URL_PATTERNS = {
    "oracle": {
        "base": "https://docs.oracle.com/javase/tutorial/java/",
        "patterns": [
            "nutsandbolts/{topic}.html",
            "concepts/{concept}.html",
            "javaOO/{feature}.html"
        ]
    },
    "geeksforgeeks": {
        "base": "https://www.geeksforgeeks.org/",
        "patterns": [
            "{topic}-in-java/",
            "java-{feature}/",
            "{concept}-java/"
        ]
    },
    "tutorialspoint": {
        "base": "https://www.tutorialspoint.com/java/",
        "patterns": [
            "java_{topic}.htm",
            "java_basic_{feature}.htm"
        ]
    }
}
```

---

## 📊 URL 状态监控

### 已验证可访问的 URL

✅ **Khan Academy** (2024年验证)
- 基础路径可访问
- 视频嵌入代码有效
- API响应正常

✅ **CodeHS** (2024年验证) 
- 教科书页面可访问
- 内容结构稳定
- 需要处理登录验证

✅ **Oracle 官方文档** (长期稳定)
- 官方维护，稳定性高
- URL结构规范
- 内容质量最高

✅ **GeeksforGeeks** (2024年验证)
- 大部分页面可访问
- 内容更新频繁
- 偶有广告干扰

⚠️ **YouTube** (需要API Key)
- 需要有效的 YouTube Data API v3 密钥
- 有请求限制
- 内容质量需要筛选

### 需要特殊处理的 URL

🔐 **需要认证**:
- CodeHS 某些高级内容需要登录
- HackerRank 练习需要账户

🚫 **可能被限制**:
- YouTube API 有配额限制
- 某些教育网站有爬虫检测

⏰ **内容可能过期**:
- 教程网站的具体文章URL可能变更
- 视频链接可能失效

---

## 🔧 URL 管理建议

### 1. URL 有效性检查

```python
async def check_url_validity(url):
    """检查URL是否有效可访问"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.head(url) as response:
                return response.status == 200
    except:
        return False
```

### 2. 失效URL处理

```python
FALLBACK_URLS = {
    "khan_academy": [
        "https://www.khanacademy.org/computing/ap-computer-science-a",
        "https://www.khanacademy.org/computing/computer-programming"
    ],
    "official_docs": [
        "https://docs.oracle.com/javase/tutorial/",
        "https://www.oracle.com/java/technologies/javase-documentation.html"
    ]
}
```

### 3. URL 更新策略

- 🔄 **定期验证**: 每月检查主要URL的有效性
- 📝 **记录变更**: 维护URL变更历史
- 🚨 **异常告警**: URL失效时发送通知
- 🔄 **自动重试**: 使用备用URL和重试机制

---

## 📝 总结

当前抓取系统覆盖了以下主要URL类别：

- **🎓 教育平台**: Khan Academy, CodeHS  
- **📖 官方文档**: Oracle Java Documentation
- **📝 教程网站**: GeeksforGeeks, TutorialsPoint
- **🎥 视频内容**: YouTube 教育频道
- **💻 练习平台**: HackerRank, LeetCode

总计配置了 **100+ 个URL**，覆盖 APCSA 全部 10 个单元的学习内容。系统设计支持动态URL生成和失效URL的自动处理。 