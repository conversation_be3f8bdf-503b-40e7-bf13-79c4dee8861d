# =============================================
# APCSA Learning Platform Backend Configuration
# =============================================

# Application Settings
APP_NAME="APCSA Learning Platform API"
APP_VERSION="1.0.0"
DEBUG=true

# Server Settings
HOST=0.0.0.0
PORT=8000

# Database Configuration
DATABASE_URL=postgresql://apcsa_user:apcsa_password@localhost:5432/apcsa_platform
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security Settings
SECRET_KEY=your-super-secret-key-change-in-production-please
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Settings
CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000","http://localhost:3001","http://127.0.0.1:3001"]

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=
SMTP_USERNAME=
SMTP_PASSWORD=

# File Upload Settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# AI/OpenAI Configuration (Optional)
OPENAI_API_KEY=

# Logging
LOG_LEVEL=INFO

# Development Settings
RELOAD=true
