"""
AI Service Module for APCSA Learning Platform
提供各种AI功能: 代码助手、智能解释、个性化学习建议等, 用英文回答
使用Google Gemini API
"""

import os
import google.generativeai as genai
from typing import List, Dict, Optional, Any
import asyncio
import json
from datetime import datetime
from fastapi import HTTPException

class AIService:
    def __init__(self):
        self.google_api_key = os.getenv("GOOGLE_API_KEY")
        self.gemini_model = os.getenv("GEMINI_MODEL", "gemini-pro")
        
        if self.google_api_key:
            genai.configure(api_key=self.google_api_key)
            self.model = genai.GenerativeModel(self.gemini_model)
            print("✅ Gemini AI服务已初始化")
        else:
            print("⚠️ 警告: 未配置 GOOGLE_API_KEY，AI功能将使用模拟响应")
            self.model = None

    async def explain_concept(self, topic: str, concept: str, user_level: str = "beginner") -> Dict[str, Any]:
        """解释Java概念，适合APCSA学生"""
        try:
            if not self.model:
                raise HTTPException(status_code=503, detail="AI service not configured. Please set GOOGLE_API_KEY environment variable.")
            
            prompt = f"""
            作为一名经验丰富的APCSA (AP Computer Science A) Java编程老师，请为{user_level}水平的学生解释以下概念：

            主题: {topic}
            概念: {concept}

            请用简单易懂的语言解释，包括：
            1. 概念的定义和作用
            2. 简单的代码示例 (如果适用)
            3. 实际应用场景
            4. 常见错误和注意事项
            5. 与其他概念的关联

            用英文回答，代码注释用英文，保持友好和鼓励的语调。
            """

            response = self.model.generate_content(prompt)
            
            return {
                "success": True,
                "explanation": response.text,
                "model_used": self.gemini_model,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Gemini AI解释错误: {e}")
            raise HTTPException(status_code=500, detail=f"AI service error: {str(e)}")

    async def review_code(self, code: str, exercise_type: str = "general") -> Dict[str, Any]:
        """AI代码审查和改进建议"""
        try:
            if not self.model:
                raise HTTPException(status_code=503, detail="AI service not configured. Please set GOOGLE_API_KEY environment variable.")

            prompt = f"""
            作为APCSA Java编程导师，请审查以下学生代码：

            练习类型: {exercise_type}
            学生代码:
            ```java
            {code}
            ```

            请提供：
            1. 代码质量评分 (1-10分)
            2. 优点和做得好的地方
            3. 需要改进的地方
            4. 具体的改进建议
            5. 代码风格建议
            6. 潜在的错误或问题

            用英文回答，保持建设性和鼓励性的语调。
            """

            response = self.model.generate_content(prompt)

            return {
                "success": True,
                "review": response.text,
                "model_used": self.gemini_model,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Gemini AI代码审查错误: {e}")
            raise HTTPException(status_code=500, detail=f"AI service error: {str(e)}")

    async def generate_hint(self, exercise_description: str, student_code: str = "") -> Dict[str, Any]:
        """为困难的练习生成智能提示"""
        try:
            if not self.model:
                raise HTTPException(status_code=503, detail="AI service not configured. Please set GOOGLE_API_KEY environment variable.")

            prompt = f"""
            作为APCSA编程助教，学生在做以下练习时遇到困难：

            练习描述: {exercise_description}
            
            学生当前代码 (如果有):
            ```java
            {student_code}
            ```

            请提供一个有用但不直接给出答案的提示，帮助学生思考正确方向：
            1. 引导性问题
            2. 相关概念提醒
            3. 思路建议
            4. 如果有代码，指出需要关注的部分

            提示要循序渐进，不要直接给出完整答案。用英文回答。
            """

            response = self.model.generate_content(prompt)

            return {
                "success": True,
                "hint": response.text,
                "model_used": self.gemini_model,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Gemini AI提示生成错误: {e}")
            raise HTTPException(status_code=500, detail=f"AI service error: {str(e)}")

    async def interactive_qa(self, question: str, context: str = "") -> Dict[str, Any]:
        """AI助手回答学生问题"""
        try:
            if not self.model:
                raise HTTPException(status_code=503, detail="AI service not configured. Please set GOOGLE_API_KEY environment variable.")

            prompt = f"""
            作为APCSA Java编程助手，请回答学生的问题：

            学生问题: {question}
            
            上下文信息: {context}

            请提供：
            1. 直接回答问题
            2. 相关的代码示例 (如果适用)
            3. 拓展知识点
            4. 学习建议

            用英文回答，保持友好和教育性的语调。
            """

            response = self.model.generate_content(prompt)

            return {
                "success": True,
                "answer": response.text,
                "model_used": self.gemini_model,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Gemini AI问答错误: {e}")
            raise HTTPException(status_code=500, detail=f"AI service error: {str(e)}")

    async def personalized_learning_path(self, user_progress: Dict, current_topic: str) -> Dict[str, Any]:
        """基于学习进度生成个性化学习建议"""
        try:
            if not self.model:
                raise HTTPException(status_code=503, detail="AI service not configured. Please set GOOGLE_API_KEY environment variable.")

            prompt = f"""
            作为APCSA个性化学习顾问，基于学生的学习进度分析：

            当前主题: {current_topic}
            学习进度数据: {json.dumps(user_progress, ensure_ascii=False)}

            请提供个性化学习建议：
            1. 当前学习状态评估
            2. 建议的下一步学习内容
            3. 需要加强的概念领域
            4. 推荐的练习类型和难度
            5. 学习时间安排建议

            用英文回答，保持鼓励和支持的语调。
            """

            response = self.model.generate_content(prompt)

            return {
                "success": True,
                "learning_path": response.text,
                "model_used": self.gemini_model,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Gemini AI学习路径生成错误: {e}")
            raise HTTPException(status_code=500, detail=f"AI service error: {str(e)}")



# 全局AI服务实例
ai_service = AIService() 