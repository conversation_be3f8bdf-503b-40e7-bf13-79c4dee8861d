from fastapi import APIRouter, HTTPException, Depends
from typing import List
from database import init_supabase, get_current_time
from models import Unit, Topic, UnitWithTopics, APIResponse, UserResponse
from auth import get_current_user, get_current_active_user
from ai_service import ai_service
from document_library import document_library
from pydantic import BaseModel
import hashlib
import secrets
import uuid
from datetime import datetime
from firecrawl_service import firecrawl_service

# 认证相关模型
class LoginRequest(BaseModel):
    email: str
    password: str

class RegisterRequest(BaseModel):
    email: str
    password: str
    full_name: str
    role: str = "student"
    school: str = None
    grade_level: str = None

class AuthResponse(BaseModel):
    success: bool
    message: str = None
    user: dict = None
    token: str = None

# 文档库相关的模型
class DocumentSaveRequest(BaseModel):
    type: str
    data: dict
    unit_id: str = None
    topic: str = None

class DocumentSearchRequest(BaseModel):
    query: str = None
    doc_type: str = None
    category: str = None
    tags: List[str] = None
    limit: int = 10

# 用户进度相关的模型
class UserProgressData(BaseModel):
    totalUnits: int
    completedUnits: int
    inProgressUnits: int
    totalExercises: int
    completedExercises: int
    totalTimeSpent: int
    currentStreak: int
    achievements: List[dict]
    weeklyProgress: List[dict]
    recentActivities: List[dict]

class UserStatsData(BaseModel):
    unitsCompleted: str
    totalUnits: str
    problemsSolved: str
    totalProblems: str
    currentStreak: str
    averageScore: str

router = APIRouter()

# ===============================
# 认证路由
# ===============================

@router.post("/auth/login", response_model=AuthResponse)
async def login(request: LoginRequest):
    """用户登录"""
    try:
        supabase = init_supabase()
        
        # 实际的用户验证逻辑
        # 查询数据库中的用户
        user_query = supabase.table("users").select("*").eq("email", request.email).single().execute()
        
        if not user_query.data:
            return AuthResponse(
                success=False,
                message="用户不存在"
            )
        
        user_data = user_query.data
        
        # 验证密码 (在实际应用中应该使用bcrypt等安全的哈希方法)
        # 这里简化处理
        if len(request.password) >= 6:  # 简单验证
            # 生成JWT token
            token = f"jwt_{secrets.token_hex(32)}"
            
            # 在实际应用中，应该将token存储在Redis或数据库中
            # 这里简化存储在内存中
            
            return AuthResponse(
                success=True,
                message="登录成功",
                user=user_data,
                token=token
            )
        else:
            return AuthResponse(
                success=False,
                message="密码错误"
            )
    except Exception as e:
        return AuthResponse(
            success=False,
            message=f"登录失败: {str(e)}"
        )

@router.post("/auth/register", response_model=AuthResponse) 
async def register(request: RegisterRequest):
    """用户注册"""
    try:
        supabase = init_supabase()
        
        # 检查用户是否已存在
        existing_user = supabase.table("users").select("id").eq("email", request.email).execute()
        
        if existing_user.data:
            return AuthResponse(
                success=False,
                message="用户已存在"
            )
        
        # 创建新用户
        user_data = {
            "email": request.email,
            "full_name": request.full_name,
            "role": request.role,
            "school": request.school,
            "grade_level": request.grade_level,
            "is_active": True,
            "created_at": datetime.utcnow().isoformat()
        }
        
        result = supabase.table("users").insert(user_data).execute()
        
        if result.data:
            return AuthResponse(
                success=True,
                message="注册成功，请登录"
            )
        else:
            return AuthResponse(
                success=False,
                message="注册失败"
            )
    except Exception as e:
        return AuthResponse(
            success=False,
            message=f"注册失败: {str(e)}"
        )

# ===============================
# 课程相关路由 (需要认证)
# ===============================

@router.get("/units", response_model=List[Unit])
async def get_units(current_user: UserResponse = Depends(get_current_active_user)):
    """获取所有 APCSA 单元"""
    try:
        supabase = init_supabase()
        result = supabase.table("units").select("*").eq("is_active", True).order("order_index").execute()
        return result.data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/units/{unit_id}", response_model=UnitWithTopics)
async def get_unit_with_topics(unit_id: str, current_user: UserResponse = Depends(get_current_active_user)):
    """获取特定单元及其主题"""
    try:
        supabase = init_supabase()
        
        # 获取单元信息
        unit_result = supabase.table("units").select("*").eq("id", unit_id).single().execute()
        if not unit_result.data:
            raise HTTPException(status_code=404, detail="Unit not found")
        
        # 获取该单元的主题
        topics_result = supabase.table("topics").select("*").eq("unit_id", unit_id).eq("is_active", True).order("order_index").execute()
        
        return UnitWithTopics(
            unit=Unit(**unit_result.data),
            topics=[Topic(**topic) for topic in topics_result.data]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/units/{unit_id}/topics")
async def get_unit_topics_detailed(unit_id: str, current_user: UserResponse = Depends(get_current_active_user)):
    """获取指定单元的所有主题详细信息"""
    try:
        supabase = init_supabase()
        result = supabase.table("topics").select("*").eq("unit_id", unit_id).eq("is_active", True).order("order_index").execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="No topics found for this unit")
        
        return {"success": True, "data": result.data, "count": len(result.data)}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/topics/{topic_id}/exercises")
async def get_topic_exercises(topic_id: str, current_user: UserResponse = Depends(get_current_active_user)):
    """获取指定主题的所有练习题"""
    try:
        supabase = init_supabase()
        result = supabase.table("exercises").select("*").eq("topic_id", topic_id).execute()
        
        return {"success": True, "data": result.data, "count": len(result.data)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/topics/{topic_id}/resources")
async def get_topic_resources(topic_id: str, current_user: UserResponse = Depends(get_current_active_user)):
    """获取指定主题的所有学习资源"""
    try:
        supabase = init_supabase()
        result = supabase.table("learning_resources").select("*").eq("topic_id", topic_id).order("order_index").execute()
        
        return {"success": True, "data": result.data, "count": len(result.data)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/topics/{topic_id}", response_model=Topic)
async def get_topic(topic_id: str, current_user: UserResponse = Depends(get_current_active_user)):
    """获取特定主题信息"""
    try:
        supabase = init_supabase()
        result = supabase.table("topics").select("*").eq("id", topic_id).single().execute()
        if not result.data:
            raise HTTPException(status_code=404, detail="Topic not found")
        return Topic(**result.data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/exercises/{exercise_id}")
async def get_exercise(exercise_id: str, current_user: UserResponse = Depends(get_current_active_user)):
    """获取练习详情"""
    try:
        supabase = init_supabase()
        result = supabase.table("exercises").select("*").eq("id", exercise_id).single().execute()
        if not result.data:
            raise HTTPException(status_code=404, detail="Exercise not found")
        return result.data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/me", response_model=UserResponse)
async def get_me(current_user: UserResponse = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return current_user

# ===============================
# 系统路由
# ===============================

@router.get("/database/test", response_model=APIResponse)
async def test_database_connection():
    """测试数据库连接"""
    try:
        supabase = init_supabase()
        current_time = get_current_time()
        
        # 简单查询测试连接
        result = supabase.table("units").select("count", count="exact").execute()
        count = result.count
        
        return APIResponse(
            success=True,
            message=f"Database connection successful. Found {count} units.",
            data={
                "units_count": count,
                "server_time": current_time.strftime('%Y-%m-%d %H:%M:%S %Z'),
                "timezone": "America/Vancouver"
            }
        )
    except Exception as e:
        return APIResponse(
            success=False,
            message=f"Database connection failed: {str(e)}",
            data=None
        )

@router.get("/time", response_model=APIResponse)
async def get_server_time():
    """获取服务器时间 (温哥华时区)"""
    current_time = get_current_time()
    return APIResponse(
        success=True,
        message="Current server time",
        data={
            "current_time": current_time.strftime('%Y-%m-%d %H:%M:%S %Z'),
            "timezone": "America/Vancouver",
            "iso_format": current_time.isoformat()
        }
    )

# ===============================
# AI 服务相关路由
# ===============================

@router.post("/ai/explain")
async def explain_concept(
    request: dict,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """AI解释概念"""
    try:
        topic = request.get("topic", "")
        concept = request.get("concept", "")
        user_level = request.get("user_level", "beginner")
        
        result = await ai_service.explain_concept(topic, concept, user_level)
        
        # 保存AI对话到数据库
        await save_ai_conversation(current_user.id, "explain", request, result)
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI服务错误: {str(e)}")

@router.post("/ai/review-code")
async def review_code(
    request: dict,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """AI代码审查"""
    try:
        code = request.get("code", "")
        exercise_type = request.get("exercise_type", "general")
        
        result = await ai_service.review_code(code, exercise_type)
        
        # 保存AI对话到数据库
        await save_ai_conversation(current_user.id, "review_code", request, result)
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI服务错误: {str(e)}")

@router.post("/ai/hint")
async def generate_hint(
    request: dict,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """AI生成提示"""
    try:
        exercise_description = request.get("exercise_description", "")
        student_code = request.get("student_code", "")
        
        result = await ai_service.generate_hint(exercise_description, student_code)
        
        # 保存AI对话到数据库
        await save_ai_conversation(current_user.id, "hint", request, result)
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI服务错误: {str(e)}")

@router.post("/ai/ask")
async def ask_ai(
    request: dict,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """AI问答助手"""
    try:
        question = request.get("question", "")
        context = request.get("context", "")
        
        result = await ai_service.interactive_qa(question, context)
        
        # 保存AI对话到数据库
        await save_ai_conversation(current_user.id, "qa", request, result)
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI服务错误: {str(e)}")

# ===============================
# 文档库系统端点
# ===============================

@router.post("/document-library/save")
async def save_to_document_library(
    request: DocumentSaveRequest,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """保存文档到文档库"""
    try:
        doc_id = await document_library.save_document(
            doc_type=request.type,
            data=request.data,
            unit_id=request.unit_id,
            topic=request.topic,
            user_id=current_user.id
        )
        
        return {
            "success": True,
            "message": "文档已保存到库中",
            "document_id": doc_id
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"保存失败: {str(e)}"
        }

@router.post("/document-library/search")
async def search_document_library(
    request: DocumentSearchRequest,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """搜索文档库"""
    try:
        results = await document_library.search_documents(
            query=request.query,
            doc_type=request.doc_type,
            category=request.category,
            tags=request.tags,
            limit=request.limit
        )
        
        return {
            "success": True,
            "results": results,
            "count": len(results)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/document-library/stats")
async def get_document_library_stats(current_user: UserResponse = Depends(get_current_active_user)):
    """获取文档库统计信息"""
    try:
        stats = await document_library.get_document_stats()
        return {
            "success": True,
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

# ===============================
# 用户进度和统计
# ===============================

@router.get("/user/progress")
async def get_user_progress(current_user: UserResponse = Depends(get_current_active_user)):
    """获取用户学习进度"""
    try:
        supabase = init_supabase()
        
        # 获取用户进度数据
        progress_query = supabase.table("student_progress").select("*").eq("student_id", current_user.id).execute()
        
        # 获取总单元数
        units_query = supabase.table("units").select("*").eq("is_active", True).execute()
        total_units = len(units_query.data)
        
        # 获取用户学习活动
        activities_query = supabase.table("learning_activities").select("*").eq("student_id", current_user.id).order("created_at", desc=True).limit(10).execute()
        
        # 处理进度数据
        if progress_query.data:
            # 有真实数据
            progress_data = progress_query.data[0]
            
            return APIResponse(
                success=True,
                message="用户进度获取成功",
                data={
                    "totalUnits": total_units,
                    "completedUnits": progress_data.get("completed_units", 0),
                    "inProgressUnits": progress_data.get("in_progress_units", 0),
                    "totalExercises": progress_data.get("total_exercises", 0),
                    "completedExercises": progress_data.get("completed_exercises", 0),
                    "totalTimeSpent": progress_data.get("total_time_spent", 0),
                    "currentStreak": progress_data.get("current_streak", 0),
                    "achievements": progress_data.get("achievements", []),
                    "weeklyProgress": progress_data.get("weekly_progress", []),
                    "recentActivities": activities_query.data
                }
            )
        else:
            # 新用户，初始化进度
            initial_progress = {
                "student_id": current_user.id,
                "completed_units": 0,
                "in_progress_units": 0,
                "total_exercises": 0,
                "completed_exercises": 0,
                "total_time_spent": 0,
                "current_streak": 0,
                "achievements": [],
                "weekly_progress": [],
                "created_at": datetime.utcnow().isoformat()
            }
            
            # 创建初始进度记录
            supabase.table("student_progress").insert(initial_progress).execute()
            
            return APIResponse(
                success=True,
                message="用户进度初始化成功",
                data={
                    "totalUnits": total_units,
                    "completedUnits": 0,
                    "inProgressUnits": 0,
                    "totalExercises": 0,
                    "completedExercises": 0,
                    "totalTimeSpent": 0,
                    "currentStreak": 0,
                    "achievements": [],
                    "weeklyProgress": [],
                    "recentActivities": []
                }
            )
        
    except Exception as e:
        return APIResponse(
            success=False,
            message=f"获取用户进度失败: {str(e)}"
        )

@router.get("/user/stats")
async def get_user_stats(current_user: UserResponse = Depends(get_current_active_user)):
    """获取用户统计数据"""
    try:
        supabase = init_supabase()
        
        # 获取用户统计数据
        stats_query = supabase.table("student_progress").select("*").eq("student_id", current_user.id).single().execute()
        
        # 获取总数据
        units_query = supabase.table("units").select("*").eq("is_active", True).execute()
        total_units = len(units_query.data)
        
        if stats_query.data:
            stats = stats_query.data
            return APIResponse(
                success=True,
                message="用户统计获取成功",
                data={
                    "unitsCompleted": str(stats.get("completed_units", 0)),
                    "totalUnits": str(total_units),
                    "problemsSolved": str(stats.get("completed_exercises", 0)),
                    "totalProblems": str(stats.get("total_exercises", 0)),
                    "currentStreak": str(stats.get("current_streak", 0)),
                    "averageScore": str(stats.get("average_score", 0))
                }
            )
        else:
            return APIResponse(
                success=True,
                message="新用户统计",
                data={
                    "unitsCompleted": "0",
                    "totalUnits": str(total_units),
                    "problemsSolved": "0",
                    "totalProblems": "0",
                    "currentStreak": "0",
                    "averageScore": "0"
                }
            )
        
    except Exception as e:
        return APIResponse(
            success=False,
            message=f"获取用户统计失败: {str(e)}"
        )

# ===============================
# Chatbot对话系统
# ===============================

@router.post("/chatbot/conversation")
async def save_chatbot_conversation(
    conversation_data: dict,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """保存chatbot对话到数据库"""
    try:
        supabase = init_supabase()
        
        conversation_record = {
            "user_id": current_user.id,
            "conversation_type": conversation_data.get("type", "general"),
            "user_message": conversation_data.get("userMessage", ""),
            "ai_response": conversation_data.get("aiResponse", ""),
            "context_topic": conversation_data.get("context", {}).get("topic"),
            "context_unit": conversation_data.get("context", {}).get("unit"),
            "metadata": conversation_data.get("metadata", {}),
            "created_at": datetime.utcnow().isoformat()
        }
        
        result = supabase.table("chatbot_conversations").insert(conversation_record).execute()
        
        return {
            "success": True,
            "message": "对话已保存",
            "data": {
                "id": result.data[0]["id"],
                "saved_at": result.data[0]["created_at"]
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"保存对话失败: {str(e)}"
        }

@router.get("/chatbot/history")
async def get_chatbot_history(
    limit: int = 50,
    context_topic: str = None,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取用户的chatbot对话历史"""
    try:
        supabase = init_supabase()
        
        query = supabase.table("chatbot_conversations").select("*").eq("user_id", current_user.id)
        
        if context_topic:
            query = query.eq("context_topic", context_topic)
            
        result = query.order("created_at", desc=True).limit(limit).execute()
        
        return {
            "success": True,
            "data": result.data,
            "count": len(result.data)
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"获取对话历史失败: {str(e)}",
            "data": []
        }

# ===============================
# 外部资源和Firecrawl集成
# ===============================

@router.get("/external-content/unit/{unit_id}")
async def get_external_content_for_unit(
    unit_id: str,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取指定单元的外部学习内容"""
    try:
        from external_content_service import external_content_service
        
        # 获取单元信息
        supabase = init_supabase()
        unit_query = supabase.table("units").select("*").eq("id", unit_id).single().execute()
        
        if not unit_query.data:
            raise HTTPException(status_code=404, detail="Unit not found")
        
        unit_title = unit_query.data["title"]
        
        async with external_content_service:
            content = await external_content_service.get_content_for_unit(unit_id, unit_title)
        
        return {
            "success": True,
            "unit_id": unit_id,
            "unit_title": unit_title,
            "content": content,
            "total_resources": len(content.get('videos', [])) + len(content.get('articles', [])) + len(content.get('exercises', []))
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取外部内容失败: {str(e)}")

@router.post("/resources/collect/unit/{unit_id}")
async def collect_resources_for_unit(
    unit_id: str,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """收集并保存指定单元的外部资源到数据库"""
    try:
        from resource_manager import resource_manager
        
        # 获取单元信息
        supabase = init_supabase()
        unit_query = supabase.table("units").select("*").eq("id", unit_id).single().execute()
        
        if not unit_query.data:
            raise HTTPException(status_code=404, detail="Unit not found")
        
        unit_title = unit_query.data["title"]
        
        # 执行资源收集
        result = await resource_manager.collect_resources_for_unit(
            unit_id=unit_id,
            unit_title=unit_title,
            created_by=current_user.email
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"资源收集失败: {str(e)}")

@router.get("/resources/stored")
async def get_stored_resources(
    unit_id: str = None,
    resource_type: str = None,
    limit: int = 20,
    offset: int = 0,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取已存储的外部资源"""
    try:
        from resource_manager import resource_manager
        
        result = await resource_manager.get_stored_resources(
            unit_id=unit_id,
            resource_type=resource_type,
            limit=limit,
            offset=offset
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储资源失败: {str(e)}")

# ===============================
# Firecrawl API
# ===============================

@router.post("/firecrawl/scrape")
async def firecrawl_scrape_resource(
    request: dict,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """使用Firecrawl抓取单个教育资源"""
    try:
        url = request.get('url')
        if not url:
            raise HTTPException(status_code=400, detail="URL is required")
        
        options = request.get('options', {})
        
        result = await firecrawl_service.scrape_educational_resource(url, options)
        
        return {
            "success": True,
            "data": result,
            "message": "资源抓取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Firecrawl抓取失败: {str(e)}")

@router.post("/firecrawl/crawl")
async def firecrawl_crawl_site(
    request: dict,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """使用Firecrawl爬取整个教育网站"""
    try:
        base_url = request.get('base_url')
        if not base_url:
            raise HTTPException(status_code=400, detail="base_url is required")
        
        max_pages = request.get('max_pages', 10)
        
        result = await firecrawl_service.crawl_educational_site(base_url, max_pages)
        
        return {
            "success": True,
            "data": result,
            "message": "网站爬取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Firecrawl爬取失败: {str(e)}")

@router.get("/firecrawl/status")
async def firecrawl_get_status(current_user: UserResponse = Depends(get_current_active_user)):
    """获取Firecrawl服务状态"""
    try:
        status = firecrawl_service.get_service_status()
        
        return {
            "success": True,
            "data": status,
            "message": "Firecrawl服务状态获取成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

# ===============================
# 辅助函数
# ===============================

async def save_ai_conversation(user_id: str, conversation_type: str, request_data: dict, response_data: dict):
    """保存AI对话到数据库"""
    try:
        supabase = init_supabase()
        
        conversation_record = {
            "user_id": user_id,
            "conversation_type": conversation_type,
            "user_message": str(request_data),
            "ai_response": str(response_data),
            "created_at": datetime.utcnow().isoformat()
        }
        
        supabase.table("ai_conversations").insert(conversation_record).execute()
        
    except Exception as e:
        print(f"Failed to save AI conversation: {e}")
        # 不抛出异常，避免影响主要功能 