from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.models.user import User
from app.api.deps import get_current_user
from app.models.course import Topic, Exercise, LearningResource
from pydantic import BaseModel
from typing import Optional

router = APIRouter()

class TopicResponse(BaseModel):
    id: str
    unit_id: str
    title: str
    description: str
    order_index: int
    estimated_hours: int
    learning_objectives: Optional[List[str]]
    is_active: bool
    
    class Config:
        from_attributes = True

class ExerciseResponse(BaseModel):
    id: str
    topic_id: str
    title: str
    description: str
    type: str
    difficulty: str
    points: int
    content: Optional[str]
    
    class Config:
        from_attributes = True

class LearningResourceResponse(BaseModel):
    id: str
    topic_id: str
    title: str
    type: str
    url: Optional[str]
    description: Optional[str]
    order_index: int
    
    class Config:
        from_attributes = True

@router.get("/units/{unit_id}/topics", response_model=List[TopicResponse])
async def get_unit_topics(
    unit_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定单元的所有主题"""
    topics = db.query(Topic).filter(
        Topic.unit_id == unit_id,
        Topic.is_active == True
    ).order_by(Topic.order_index).all()
    
    if not topics:
        raise HTTPException(status_code=404, detail="No topics found for this unit")
    
    return topics

@router.get("/topics/{topic_id}/exercises", response_model=List[ExerciseResponse])
async def get_topic_exercises(
    topic_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定主题的所有练习题"""
    exercises = db.query(Exercise).filter(
        Exercise.topic_id == topic_id
    ).all()
    
    return exercises

@router.get("/topics/{topic_id}/resources", response_model=List[LearningResourceResponse])
async def get_topic_resources(
    topic_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定主题的所有学习资源"""
    resources = db.query(LearningResource).filter(
        LearningResource.topic_id == topic_id
    ).order_by(LearningResource.order_index).all()
    
    return resources

@router.get("/topics/{topic_id}", response_model=TopicResponse)
async def get_topic_detail(
    topic_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取主题详细信息"""
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found")
    
    return topic 