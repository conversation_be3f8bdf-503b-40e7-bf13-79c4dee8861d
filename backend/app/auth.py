"""
Authentication utilities and dependencies
"""
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import hashlib
import secrets

from .config import settings
from .database import get_async_db
from .models import User, UserSession
from .schemas import TokenData, UserResponse

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token security
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT refresh token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=settings.refresh_token_expire_days)
    
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> Optional[TokenData]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        
        # Check token type
        if payload.get("type") != token_type:
            return None
            
        user_id: str = payload.get("sub")
        email: str = payload.get("email")
        role: str = payload.get("role")
        
        if user_id is None or email is None:
            return None
            
        token_data = TokenData(user_id=user_id, email=email, role=role)
        return token_data
        
    except JWTError:
        return None


def generate_session_token() -> str:
    """Generate a secure session token"""
    return secrets.token_urlsafe(32)


def hash_token(token: str) -> str:
    """Hash a token for storage"""
    return hashlib.sha256(token.encode()).hexdigest()


async def authenticate_user(db: AsyncSession, email: str, password: str) -> Optional[User]:
    """Authenticate user with email and password"""
    try:
        # Get user by email
        result = await db.execute(select(User).where(User.email == email))
        user = result.scalar_one_or_none()

        if not user:
            return None

        # Verify password - handle both bcrypt and PostgreSQL crypt formats
        password_valid = False

        # Try bcrypt first (for new users)
        if user.password_hash.startswith('$2b$') or user.password_hash.startswith('$2a$'):
            password_valid = verify_password(password, user.password_hash)
        else:
            # For existing PostgreSQL crypt passwords, verify using SQL
            from sqlalchemy import text
            result = await db.execute(
                text("SELECT crypt(:password, :hash) = :hash as valid"),
                {"password": password, "hash": user.password_hash}
            )
            password_valid = result.scalar()

            # If valid, update to bcrypt for future use
            if password_valid:
                user.password_hash = get_password_hash(password)
                await db.commit()

        if not password_valid:
            return None

        # Check if user is active
        if user.status != "active":
            return None

        return user

    except Exception as e:
        print(f"Authentication error: {e}")  # Debug logging
        return None


async def get_user_by_id(db: AsyncSession, user_id: str) -> Optional[User]:
    """Get user by ID"""
    try:
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()
    except Exception:
        return None


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """Get user by email"""
    try:
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()
    except Exception:
        return None


async def create_user_session(
    db: AsyncSession, 
    user_id: str, 
    token: str, 
    expires_at: datetime,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
) -> UserSession:
    """Create a new user session"""
    session = UserSession(
        user_id=user_id,
        token_hash=hash_token(token),
        expires_at=expires_at,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    db.add(session)
    await db.commit()
    await db.refresh(session)
    return session


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Verify token
        token_data = verify_token(credentials.credentials, "access")
        if token_data is None:
            raise credentials_exception
            
        # Get user
        user = await get_user_by_id(db, str(token_data.user_id))
        if user is None:
            raise credentials_exception
            
        # Check if user is still active
        if user.status != "active":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is not active"
            )
            
        return user
        
    except JWTError:
        raise credentials_exception


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user"""
    if current_user.status != "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="Inactive user"
        )
    return current_user


async def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current admin user"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


async def get_current_teacher_or_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current teacher or admin user"""
    if current_user.role not in ["teacher", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


# Optional authentication (for public endpoints that can benefit from user context)
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_async_db)
) -> Optional[User]:
    """Get current user if authenticated, None otherwise"""
    if not credentials:
        return None
        
    try:
        token_data = verify_token(credentials.credentials, "access")
        if token_data is None:
            return None
            
        user = await get_user_by_id(db, str(token_data.user_id))
        if user is None or user.status != "active":
            return None
            
        return user
        
    except Exception:
        return None


async def revoke_user_sessions(db: AsyncSession, user_id: str) -> bool:
    """Revoke all user sessions"""
    try:
        # Delete all sessions for the user
        await db.execute(
            "DELETE FROM user_sessions WHERE user_id = :user_id",
            {"user_id": user_id}
        )
        await db.commit()
        return True
    except Exception:
        return False


async def cleanup_expired_sessions(db: AsyncSession) -> int:
    """Clean up expired sessions"""
    try:
        result = await db.execute(
            "DELETE FROM user_sessions WHERE expires_at < :now",
            {"now": datetime.utcnow()}
        )
        await db.commit()
        return result.rowcount
    except Exception:
        return 0
