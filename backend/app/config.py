"""
Configuration settings for APCSA Learning Platform
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    app_name: str = "APCSA Learning Platform API"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Database
    database_url: str = "postgresql://apcsa_user:apcsa_password@localhost:5432/apcsa_platform"
    database_echo: bool = False
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # CORS
    cors_origins: list[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
    ]
    
    # Email (for future use)
    smtp_host: Optional[str] = None
    smtp_port: Optional[int] = None
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    
    # File upload
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    upload_dir: str = "uploads"
    
    # AI/OpenAI (for future use)
    openai_api_key: Optional[str] = None
    
    # Logging
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Database configuration
def get_database_url() -> str:
    """Get database URL for SQLAlchemy"""
    return settings.database_url


def get_async_database_url() -> str:
    """Get async database URL for asyncpg"""
    return settings.database_url.replace("postgresql://", "postgresql+asyncpg://")


# Security configuration
def get_secret_key() -> str:
    """Get secret key for JWT"""
    return settings.secret_key


# Redis configuration
def get_redis_url() -> str:
    """Get Redis URL"""
    return settings.redis_url
