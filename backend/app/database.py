"""
Database configuration and connection management
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
import asyncpg
import asyncio
from typing import AsyncGenerator
import logging

from .config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy setup
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
    pool_recycle=300,
)

async_engine = create_async_engine(
    settings.database_url.replace("postgresql://", "postgresql+asyncpg://"),
    echo=settings.database_echo,
    pool_pre_ping=True,
    pool_recycle=300,
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = async_sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

Base = declarative_base()
metadata = MetaData()


# Dependency to get database session
def get_db():
    """Get synchronous database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Get asynchronous database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


# Database connection utilities
async def check_database_connection() -> bool:
    """Check if database connection is working"""
    try:
        # Parse database URL
        url_parts = settings.database_url.replace("postgresql://", "").split("@")
        user_pass = url_parts[0].split(":")
        host_db = url_parts[1].split("/")
        host_port = host_db[0].split(":")
        
        user = user_pass[0]
        password = user_pass[1]
        host = host_port[0]
        port = int(host_port[1]) if len(host_port) > 1 else 5432
        database = host_db[1]
        
        # Test connection
        conn = await asyncpg.connect(
            user=user,
            password=password,
            database=database,
            host=host,
            port=port
        )
        
        # Test query
        result = await conn.fetchval("SELECT 1")
        await conn.close()
        
        logger.info("Database connection successful")
        return result == 1
        
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


async def init_database():
    """Initialize database tables"""
    try:
        async with async_engine.begin() as conn:
            # Import all models to ensure they're registered
            from . import models
            
            # Create tables if they don't exist
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("Database tables initialized")
        return True
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False


async def close_database():
    """Close database connections"""
    try:
        await async_engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")


# Database health check
async def database_health_check() -> dict:
    """Perform database health check"""
    try:
        start_time = asyncio.get_event_loop().time()
        
        async with AsyncSessionLocal() as session:
            from sqlalchemy import text
            result = await session.execute(text("SELECT 1 as health_check"))
            health_result = result.scalar()
            
        end_time = asyncio.get_event_loop().time()
        response_time = round((end_time - start_time) * 1000, 2)  # ms
        
        return {
            "status": "healthy" if health_result == 1 else "unhealthy",
            "response_time_ms": response_time,
            "database": "postgresql",
            "connection_pool": {
                "size": async_engine.pool.size(),
                "checked_in": async_engine.pool.checkedin(),
                "checked_out": async_engine.pool.checkedout(),
            }
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "database": "postgresql"
        }
