"""
APCSA Learning Platform FastAPI Application
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import <PERSON><PERSON>NResponse
from contextlib import asynccontextmanager
import logging
import time
from datetime import datetime

from .config import settings
from .database import init_database, close_database, check_database_connection, database_health_check
from .routers import auth
from .schemas import HealthCheck, APIResponse

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting APCSA Learning Platform API...")
    
    # Check database connection
    db_connected = await check_database_connection()
    if not db_connected:
        logger.error("Failed to connect to database")
        raise Exception("Database connection failed")
    
    # Initialize database
    db_initialized = await init_database()
    if not db_initialized:
        logger.warning("Database initialization had issues")
    
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down APCSA Learning Platform API...")
    await close_database()
    logger.info("Application shutdown complete")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Backend API for APCSA AI Learning Platform",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add trusted host middleware (security)
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.apcsa.com"]
    )


# Request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time to response headers"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Global exception: {exc}", exc_info=True)
    
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "message": exc.detail,
                "error_type": "http_exception"
            }
        )
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error_type": "internal_error"
        }
    )


# Include routers
app.include_router(auth.router, prefix="/api")


# Health check endpoint
@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    try:
        # Check database health
        db_health = await database_health_check()
        
        # TODO: Add Redis health check when implemented
        redis_health = None
        
        overall_status = "healthy" if db_health.get("status") == "healthy" else "unhealthy"
        
        return HealthCheck(
            status=overall_status,
            timestamp=datetime.utcnow(),
            version=settings.app_version,
            database=db_health,
            redis=redis_health
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheck(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            version=settings.app_version,
            database={"status": "unhealthy", "error": str(e)},
            redis=None
        )


# Root endpoint
@app.get("/", response_model=APIResponse)
async def root():
    """Root endpoint"""
    return APIResponse(
        success=True,
        message=f"Welcome to {settings.app_name} API v{settings.app_version}",
        data={
            "version": settings.app_version,
            "docs_url": "/docs" if settings.debug else None,
            "health_url": "/health"
        }
    )


# API info endpoint
@app.get("/api", response_model=APIResponse)
async def api_info():
    """API information endpoint"""
    return APIResponse(
        success=True,
        message="APCSA Learning Platform API",
        data={
            "version": settings.app_version,
            "endpoints": {
                "authentication": "/api/auth",
                "health": "/health",
                "documentation": "/docs" if settings.debug else "disabled"
            },
            "features": [
                "User Authentication",
                "Course Management",
                "Progress Tracking",
                "Exercise Submissions",
                "AI-Powered Quizzes",
                "Resource Management"
            ]
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
