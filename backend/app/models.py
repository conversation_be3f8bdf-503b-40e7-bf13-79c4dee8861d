"""
SQLAlchemy models for APCSA Learning Platform
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ARRAY, JSON, ForeignKey, BigInteger
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from .database import Base


class User(Base):
    """User model"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False, default="student")  # student, teacher, admin
    status = Column(String(20), nullable=False, default="active")  # active, inactive, suspended
    avatar_url = Column(Text, nullable=True)
    school = Column(String(255), nullable=True)
    grade_level = Column(String(10), nullable=True)
    settings = Column(JSON, default={})
    email_verified = Column(Boolean, default=False)
    email_verified_at = Column(DateTime(timezone=True), nullable=True)
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    progress = relationship("UserProgress", back_populates="user", cascade="all, delete-orphan")
    submissions = relationship("ExerciseSubmission", back_populates="user", cascade="all, delete-orphan")
    quizzes = relationship("AIQuiz", back_populates="user", cascade="all, delete-orphan")
    activity_logs = relationship("UserActivityLog", back_populates="user", cascade="all, delete-orphan")


class UserSession(Base):
    """User session model"""
    __tablename__ = "user_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token_hash = Column(String(255), nullable=False, index=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    ip_address = Column(String(45), nullable=True)  # Support IPv6
    user_agent = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="sessions")


class Unit(Base):
    """Course unit model"""
    __tablename__ = "units"
    
    id = Column(Integer, primary_key=True)
    unit_code = Column(String(10), unique=True, nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    order_index = Column(Integer, nullable=False)
    is_active = Column(Boolean, default=True)
    estimated_hours = Column(Integer, default=0)
    difficulty_level = Column(Integer, default=1)
    prerequisites = Column(ARRAY(Text), default=[])
    learning_objectives = Column(ARRAY(Text), default=[])
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    topics = relationship("Topic", back_populates="unit", cascade="all, delete-orphan")
    progress = relationship("UserProgress", back_populates="unit", cascade="all, delete-orphan")
    resources = relationship("LearningResource", back_populates="unit")


class Topic(Base):
    """Topic model"""
    __tablename__ = "topics"
    
    id = Column(Integer, primary_key=True)
    unit_id = Column(Integer, ForeignKey("units.id", ondelete="CASCADE"), nullable=False)
    topic_code = Column(String(20), unique=True, nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    order_index = Column(Integer, nullable=False)
    is_active = Column(Boolean, default=True)
    estimated_minutes = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    unit = relationship("Unit", back_populates="topics")
    lessons = relationship("Lesson", back_populates="topic", cascade="all, delete-orphan")
    exercises = relationship("Exercise", back_populates="topic", cascade="all, delete-orphan")
    progress = relationship("UserProgress", back_populates="topic", cascade="all, delete-orphan")
    resources = relationship("LearningResource", back_populates="topic")


class Lesson(Base):
    """Lesson model"""
    __tablename__ = "lessons"
    
    id = Column(Integer, primary_key=True)
    topic_id = Column(Integer, ForeignKey("topics.id", ondelete="CASCADE"), nullable=False)
    lesson_code = Column(String(30), unique=True, nullable=False)
    title = Column(String(255), nullable=False)
    content_type = Column(String(20), nullable=False)  # text, video, interactive, quiz
    content_data = Column(JSON, default={})
    order_index = Column(Integer, nullable=False)
    is_active = Column(Boolean, default=True)
    estimated_minutes = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    topic = relationship("Topic", back_populates="lessons")
    progress = relationship("UserProgress", back_populates="lesson", cascade="all, delete-orphan")


class Exercise(Base):
    """Exercise model"""
    __tablename__ = "exercises"
    
    id = Column(Integer, primary_key=True)
    topic_id = Column(Integer, ForeignKey("topics.id", ondelete="CASCADE"), nullable=False)
    exercise_code = Column(String(30), unique=True, nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    exercise_type = Column(String(20), nullable=False)  # coding, multiple_choice, fill_blank, essay
    difficulty_level = Column(Integer, default=1)
    points = Column(Integer, default=10)
    time_limit_minutes = Column(Integer, nullable=True)
    content_data = Column(JSON, default={})
    solution_data = Column(JSON, default={})
    hints = Column(ARRAY(Text), default=[])
    tags = Column(ARRAY(Text), default=[])
    is_active = Column(Boolean, default=True)
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    topic = relationship("Topic", back_populates="exercises")
    submissions = relationship("ExerciseSubmission", back_populates="exercise", cascade="all, delete-orphan")


class UserProgress(Base):
    """User progress model"""
    __tablename__ = "user_progress"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    unit_id = Column(Integer, ForeignKey("units.id", ondelete="CASCADE"), nullable=True)
    topic_id = Column(Integer, ForeignKey("topics.id", ondelete="CASCADE"), nullable=True)
    lesson_id = Column(Integer, ForeignKey("lessons.id", ondelete="CASCADE"), nullable=True)
    progress_type = Column(String(20), nullable=False)  # unit, topic, lesson
    status = Column(String(20), nullable=False, default="not_started")  # not_started, in_progress, completed, skipped
    completion_percentage = Column(Integer, default=0)
    time_spent_seconds = Column(Integer, default=0)
    last_accessed_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="progress")
    unit = relationship("Unit", back_populates="progress")
    topic = relationship("Topic", back_populates="progress")
    lesson = relationship("Lesson", back_populates="progress")


class ExerciseSubmission(Base):
    """Exercise submission model"""
    __tablename__ = "exercise_submissions"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    exercise_id = Column(Integer, ForeignKey("exercises.id", ondelete="CASCADE"), nullable=False)
    submission_data = Column(JSON, default={})
    score = Column(Integer, nullable=True)
    max_score = Column(Integer, nullable=True)
    is_correct = Column(Boolean, nullable=True)
    feedback = Column(Text, nullable=True)
    time_spent_seconds = Column(Integer, nullable=True)
    attempt_number = Column(Integer, default=1)
    submitted_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="submissions")
    exercise = relationship("Exercise", back_populates="submissions")


class AIQuiz(Base):
    """AI-generated quiz model"""
    __tablename__ = "ai_quizzes"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    topic_id = Column(Integer, ForeignKey("topics.id", ondelete="SET NULL"), nullable=True)
    quiz_type = Column(String(20), nullable=False, default="practice")
    title = Column(String(255), nullable=False)
    questions = Column(JSON, default=[])
    answers = Column(JSON, default={})
    score = Column(Integer, nullable=True)
    max_score = Column(Integer, nullable=True)
    time_spent_seconds = Column(Integer, nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="quizzes")


class LearningResource(Base):
    """Learning resource model"""
    __tablename__ = "learning_resources"
    
    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    resource_type = Column(String(20), nullable=False)  # lesson_plan, video, document, exercise, quiz, image, link
    unit_id = Column(Integer, ForeignKey("units.id", ondelete="SET NULL"), nullable=True)
    topic_id = Column(Integer, ForeignKey("topics.id", ondelete="SET NULL"), nullable=True)
    file_url = Column(Text, nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(BigInteger, nullable=True)
    file_type = Column(String(100), nullable=True)
    external_url = Column(Text, nullable=True)
    content_data = Column(JSON, default={})
    tags = Column(ARRAY(Text), default=[])
    status = Column(String(20), nullable=False, default="draft")  # draft, published, archived
    download_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    unit = relationship("Unit", back_populates="resources")
    topic = relationship("Topic", back_populates="resources")
    access_logs = relationship("ResourceAccessLog", back_populates="resource", cascade="all, delete-orphan")


class ResourceAccessLog(Base):
    """Resource access log model"""
    __tablename__ = "resource_access_logs"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    resource_id = Column(Integer, ForeignKey("learning_resources.id", ondelete="CASCADE"), nullable=False)
    access_type = Column(String(20), nullable=False)  # view, download
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    accessed_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    resource = relationship("LearningResource", back_populates="access_logs")


class UserActivityLog(Base):
    """User activity log model"""
    __tablename__ = "user_activity_logs"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    activity_type = Column(String(50), nullable=False)
    activity_data = Column(JSON, default={})
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="activity_logs")


class SystemSetting(Base):
    """System settings model"""
    __tablename__ = "system_settings"
    
    id = Column(Integer, primary_key=True)
    setting_key = Column(String(100), unique=True, nullable=False)
    setting_value = Column(JSON, default={})
    description = Column(Text, nullable=True)
    is_public = Column(Boolean, default=False)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
