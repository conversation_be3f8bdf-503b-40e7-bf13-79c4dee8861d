from sqlalchemy import Column, <PERSON>, Inte<PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from app.database import Base

class Unit(Base):
    __tablename__ = "units"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    order_index = Column(Integer, nullable=False)
    estimated_hours = Column(Integer, default=0)
    prerequisites = Column(ARRAY(String), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    topics = relationship("Topic", back_populates="unit")

class Topic(Base):
    __tablename__ = "topics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    unit_id = Column(UUID(as_uuid=True), ForeignKey("units.id"), nullable=False)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    order_index = Column(Integer, nullable=False)
    estimated_hours = Column(Integer, default=0)
    learning_objectives = Column(ARRAY(String), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    unit = relationship("Unit", back_populates="topics")
    exercises = relationship("Exercise", back_populates="topic")
    learning_resources = relationship("LearningResource", back_populates="topic")

class Exercise(Base):
    __tablename__ = "exercises"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    topic_id = Column(UUID(as_uuid=True), ForeignKey("topics.id"), nullable=False)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    type = Column(String(50), nullable=False)  # 'coding', 'multiple_choice', 'fill_blank', 'true_false'
    difficulty = Column(String(20), nullable=False)  # 'easy', 'medium', 'hard'
    points = Column(Integer, default=10)
    content = Column(Text)  # 题目内容/代码模板
    solution = Column(Text)  # 参考答案
    test_cases = Column(ARRAY(String))  # 测试用例（JSON 格式）
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    topic = relationship("Topic", back_populates="exercises")

class LearningResource(Base):
    __tablename__ = "learning_resources"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    topic_id = Column(UUID(as_uuid=True), ForeignKey("topics.id"), nullable=False)
    title = Column(String(200), nullable=False)
    type = Column(String(50), nullable=False)  # 'video', 'article', 'external_link', 'pdf'
    url = Column(Text)
    description = Column(Text)
    order_index = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    topic = relationship("Topic", back_populates="learning_resources") 