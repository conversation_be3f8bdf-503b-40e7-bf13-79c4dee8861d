"""
Authentication routes
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import uuid

from ..database import get_async_db
from ..models import User, UserActivityLog
from ..schemas import (
    UserLogin, UserRegister, Token, APIResponse, 
    UserResponse, PasswordResetRequest, PasswordReset, PasswordChange
)
from ..auth import (
    authenticate_user, get_password_hash, create_access_token, 
    create_refresh_token, verify_token, get_current_user,
    create_user_session, get_user_by_email, hash_token
)
from ..config import settings

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/register", response_model=APIResponse)
async def register_user(
    user_data: UserRegister,
    request: Request,
    db: AsyncSession = Depends(get_async_db)
):
    """Register a new user"""
    try:
        # Check if user already exists
        existing_user = await get_user_by_email(db, user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        new_user = User(
            id=uuid.uuid4(),
            email=user_data.email,
            password_hash=hashed_password,
            full_name=user_data.full_name,
            role=user_data.role,
            school=user_data.school,
            grade_level=user_data.grade_level,
            email_verified=False,  # In production, require email verification
            status="active"
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # Log registration activity
        activity_log = UserActivityLog(
            user_id=new_user.id,
            activity_type="user_registered",
            activity_data={
                "email": user_data.email,
                "role": user_data.role,
                "registration_time": datetime.utcnow().isoformat()
            },
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        db.add(activity_log)
        await db.commit()
        
        return APIResponse(
            success=True,
            message="User registered successfully",
            data={"user_id": str(new_user.id), "email": new_user.email}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=APIResponse)
async def login_user(
    user_credentials: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_async_db)
):
    """Authenticate user and return tokens"""
    try:
        # Authenticate user
        user = await authenticate_user(db, user_credentials.email, user_credentials.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password"
            )
        
        # Create tokens
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        refresh_token_expires = timedelta(days=settings.refresh_token_expire_days)
        
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email, "role": user.role},
            expires_delta=access_token_expires
        )
        
        refresh_token = create_refresh_token(
            data={"sub": str(user.id), "email": user.email, "role": user.role},
            expires_delta=refresh_token_expires
        )
        
        # Create session record
        session_expires = datetime.utcnow() + refresh_token_expires
        await create_user_session(
            db=db,
            user_id=str(user.id),
            token=refresh_token,
            expires_at=session_expires,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        
        # Update last login time
        user.last_login_at = datetime.utcnow()
        await db.commit()
        
        # Log login activity
        activity_log = UserActivityLog(
            user_id=user.id,
            activity_type="user_login",
            activity_data={
                "login_time": datetime.utcnow().isoformat(),
                "login_method": "email_password"
            },
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        db.add(activity_log)
        await db.commit()
        
        # Prepare response
        token_response = Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60
        )
        
        user_response = UserResponse(
            id=user.id,
            email=user.email,
            full_name=user.full_name,
            role=user.role,
            status=user.status,
            school=user.school,
            grade_level=user.grade_level,
            avatar_url=user.avatar_url,
            settings=user.settings,
            email_verified=user.email_verified,
            email_verified_at=user.email_verified_at,
            last_login_at=user.last_login_at,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        
        return APIResponse(
            success=True,
            message="Login successful",
            data={
                "token": token_response.dict(),
                "user": user_response.dict()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=APIResponse)
async def refresh_token(
    refresh_token: str,
    db: AsyncSession = Depends(get_async_db)
):
    """Refresh access token using refresh token"""
    try:
        # Verify refresh token
        token_data = verify_token(refresh_token, "refresh")
        if not token_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Check if session exists and is valid
        result = await db.execute(
            select(User).where(User.id == token_data.user_id)
        )
        user = result.scalar_one_or_none()
        
        if not user or user.status != "active":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Create new access token
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email, "role": user.role},
            expires_delta=access_token_expires
        )
        
        return APIResponse(
            success=True,
            message="Token refreshed successfully",
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": settings.access_token_expire_minutes * 60
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout", response_model=APIResponse)
async def logout_user(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Logout user and invalidate session"""
    try:
        # Delete user sessions (logout from all devices)
        await db.execute(
            "DELETE FROM user_sessions WHERE user_id = :user_id",
            {"user_id": str(current_user.id)}
        )
        await db.commit()
        
        # Log logout activity
        activity_log = UserActivityLog(
            user_id=current_user.id,
            activity_type="user_logout",
            activity_data={
                "logout_time": datetime.utcnow().isoformat()
            }
        )
        db.add(activity_log)
        await db.commit()
        
        return APIResponse(
            success=True,
            message="Logout successful"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/me", response_model=APIResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """Get current user information"""
    user_response = UserResponse(
        id=current_user.id,
        email=current_user.email,
        full_name=current_user.full_name,
        role=current_user.role,
        status=current_user.status,
        school=current_user.school,
        grade_level=current_user.grade_level,
        avatar_url=current_user.avatar_url,
        settings=current_user.settings,
        email_verified=current_user.email_verified,
        email_verified_at=current_user.email_verified_at,
        last_login_at=current_user.last_login_at,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at
    )
    
    return APIResponse(
        success=True,
        message="User information retrieved successfully",
        data=user_response.dict()
    )


@router.post("/change-password", response_model=APIResponse)
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Change user password"""
    try:
        # Verify current password
        from ..auth import verify_password
        if not verify_password(password_data.current_password, current_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Update password
        current_user.password_hash = get_password_hash(password_data.new_password)
        await db.commit()
        
        # Revoke all sessions (force re-login)
        await db.execute(
            "DELETE FROM user_sessions WHERE user_id = :user_id",
            {"user_id": str(current_user.id)}
        )
        await db.commit()
        
        # Log password change
        activity_log = UserActivityLog(
            user_id=current_user.id,
            activity_type="password_changed",
            activity_data={
                "change_time": datetime.utcnow().isoformat()
            }
        )
        db.add(activity_log)
        await db.commit()
        
        return APIResponse(
            success=True,
            message="Password changed successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )
