"""
Pydantic schemas for API request/response models
"""
from pydantic import BaseModel, EmailStr, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum


# Enums
class UserRole(str, Enum):
    STUDENT = "student"
    TEACHER = "teacher"
    ADMIN = "admin"


class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class ProgressStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"


# Base schemas
class BaseSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)


# Authentication schemas
class UserLogin(BaseModel):
    email: EmailStr
    password: str


class UserRegister(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=100)
    full_name: str = Field(..., min_length=2, max_length=255)
    role: Optional[UserRole] = UserRole.STUDENT
    school: Optional[str] = Field(None, max_length=255)
    grade_level: Optional[str] = Field(None, max_length=10)


class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenData(BaseModel):
    user_id: Optional[UUID] = None
    email: Optional[str] = None
    role: Optional[str] = None


# User schemas
class UserBase(BaseSchema):
    email: EmailStr
    full_name: str
    role: UserRole
    status: UserStatus = UserStatus.ACTIVE
    school: Optional[str] = None
    grade_level: Optional[str] = None
    avatar_url: Optional[str] = None
    settings: Optional[Dict[str, Any]] = {}


class UserCreate(UserBase):
    password: str = Field(..., min_length=8)


class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    school: Optional[str] = None
    grade_level: Optional[str] = None
    avatar_url: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None


class UserResponse(UserBase):
    id: UUID
    email_verified: bool
    email_verified_at: Optional[datetime] = None
    last_login_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class UserProfile(UserResponse):
    """Extended user profile with additional information"""
    total_study_time: Optional[int] = 0
    completed_units: Optional[int] = 0
    current_unit: Optional[str] = None
    progress_percentage: Optional[float] = 0.0


# Unit schemas
class UnitBase(BaseSchema):
    unit_code: str
    title: str
    description: Optional[str] = None
    order_index: int
    is_active: bool = True
    estimated_hours: int = 0
    difficulty_level: int = Field(1, ge=1, le=5)
    prerequisites: List[str] = []
    learning_objectives: List[str] = []


class UnitCreate(UnitBase):
    pass


class UnitUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    estimated_hours: Optional[int] = None
    difficulty_level: Optional[int] = Field(None, ge=1, le=5)
    prerequisites: Optional[List[str]] = None
    learning_objectives: Optional[List[str]] = None


class UnitResponse(UnitBase):
    id: int
    created_at: datetime
    updated_at: datetime


# Topic schemas
class TopicBase(BaseSchema):
    topic_code: str
    title: str
    description: Optional[str] = None
    order_index: int
    is_active: bool = True
    estimated_minutes: int = 0


class TopicCreate(TopicBase):
    unit_id: int


class TopicUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    estimated_minutes: Optional[int] = None


class TopicResponse(TopicBase):
    id: int
    unit_id: int
    created_at: datetime
    updated_at: datetime


# Exercise schemas
class ExerciseBase(BaseSchema):
    exercise_code: str
    title: str
    description: Optional[str] = None
    exercise_type: str  # coding, multiple_choice, fill_blank, essay
    difficulty_level: int = Field(1, ge=1, le=5)
    points: int = 10
    time_limit_minutes: Optional[int] = None
    content_data: Dict[str, Any] = {}
    solution_data: Dict[str, Any] = {}
    hints: List[str] = []
    tags: List[str] = []
    is_active: bool = True


class ExerciseCreate(ExerciseBase):
    topic_id: int


class ExerciseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    difficulty_level: Optional[int] = Field(None, ge=1, le=5)
    points: Optional[int] = None
    time_limit_minutes: Optional[int] = None
    content_data: Optional[Dict[str, Any]] = None
    solution_data: Optional[Dict[str, Any]] = None
    hints: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class ExerciseResponse(ExerciseBase):
    id: int
    topic_id: int
    created_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime


# Progress schemas
class UserProgressBase(BaseSchema):
    progress_type: str  # unit, topic, lesson
    status: ProgressStatus = ProgressStatus.NOT_STARTED
    completion_percentage: int = Field(0, ge=0, le=100)
    time_spent_seconds: int = 0


class UserProgressCreate(UserProgressBase):
    user_id: UUID
    unit_id: Optional[int] = None
    topic_id: Optional[int] = None
    lesson_id: Optional[int] = None


class UserProgressUpdate(BaseModel):
    status: Optional[ProgressStatus] = None
    completion_percentage: Optional[int] = Field(None, ge=0, le=100)
    time_spent_seconds: Optional[int] = None
    last_accessed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class UserProgressResponse(UserProgressBase):
    id: int
    user_id: UUID
    unit_id: Optional[int] = None
    topic_id: Optional[int] = None
    lesson_id: Optional[int] = None
    last_accessed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


# Exercise submission schemas
class ExerciseSubmissionBase(BaseSchema):
    submission_data: Dict[str, Any] = {}
    time_spent_seconds: Optional[int] = None


class ExerciseSubmissionCreate(ExerciseSubmissionBase):
    exercise_id: int


class ExerciseSubmissionResponse(ExerciseSubmissionBase):
    id: int
    user_id: UUID
    exercise_id: int
    score: Optional[int] = None
    max_score: Optional[int] = None
    is_correct: Optional[bool] = None
    feedback: Optional[str] = None
    attempt_number: int
    submitted_at: datetime


# Learning resource schemas
class LearningResourceBase(BaseSchema):
    title: str
    description: Optional[str] = None
    resource_type: str  # lesson_plan, video, document, exercise, quiz, image, link
    external_url: Optional[str] = None
    content_data: Dict[str, Any] = {}
    tags: List[str] = []
    status: str = "draft"  # draft, published, archived


class LearningResourceCreate(LearningResourceBase):
    unit_id: Optional[int] = None
    topic_id: Optional[int] = None


class LearningResourceUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    external_url: Optional[str] = None
    content_data: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    status: Optional[str] = None


class LearningResourceResponse(LearningResourceBase):
    id: int
    unit_id: Optional[int] = None
    topic_id: Optional[int] = None
    file_url: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    file_type: Optional[str] = None
    download_count: int
    view_count: int
    created_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime


# API Response schemas
class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None


class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int


# Health check schema
class HealthCheck(BaseModel):
    status: str
    timestamp: datetime
    version: str
    database: Dict[str, Any]
    redis: Optional[Dict[str, Any]] = None


# Password reset schemas
class PasswordResetRequest(BaseModel):
    email: EmailStr


class PasswordReset(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)


class PasswordChange(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)
