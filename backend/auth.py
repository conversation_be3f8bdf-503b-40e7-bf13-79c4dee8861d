from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
import jwt
from jwt import PyJWTError
from fastapi import HTTPEx<PERSON>, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from database import init_supabase
from models import UserResponse, TokenData, UserRole
import os
import hashlib

security = HTTPBearer()

class AuthService:
    def __init__(self):
        self.supabase = None
    
    def get_supabase(self):
        if self.supabase is None:
            self.supabase = init_supabase()
        return self.supabase
    
    def validate_local_token(self, token: str) -> Optional[UserResponse]:
        """验证本地token"""
        try:
            # 检查token格式是否为我们的本地token
            if token.startswith("token_") and len(token) > 10:
                # 尝试从全局token存储中获取用户信息
                try:
                    # 动态导入避免循环导入
                    from api import local_token_store
                    
                    if token in local_token_store:
                        user_data = local_token_store[token]
                        return UserResponse(
                            id=user_data["id"],
                            email=user_data["email"],
                            full_name=user_data["full_name"],
                            role=user_data["role"],
                            school=user_data.get("school"),
                            grade_level=user_data.get("grade_level"),
                            is_active=True,
                            created_at=datetime.now().isoformat()
                        )
                except ImportError:
                    pass
                
                # 如果无法从存储中获取，生成演示用户
                user_id = f"user_{hashlib.md5(token.encode()).hexdigest()[:8]}"
                
                return UserResponse(
                    id=user_id,
                    email=f"user{user_id[-4:]}@apcsa.ai",
                    full_name="APCSA Student",
                    role="student",
                    school="APCSA Online",
                    grade_level="11",
                    is_active=True,
                    created_at=datetime.now().isoformat()
                )
            return None
        except Exception as e:
            print(f"本地token验证错误: {e}")
            return None
    
    async def get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserResponse:
        """获取当前用户 - 支持本地token和Supabase JWT"""
        try:
            token = credentials.credentials
            
            # 首先尝试本地token验证
            local_user = self.validate_local_token(token)
            if local_user:
                print(f"✅ 本地token验证成功: {local_user.email}")
                return local_user
            
            # 如果本地token验证失败，尝试Supabase验证
            try:
                supabase = self.get_supabase()
                user_response = supabase.auth.get_user(token)
                
                if not user_response.user:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Invalid authentication token"
                    )
                
                supabase_user = user_response.user
                print(f"✅ Supabase用户验证成功: {supabase_user.email}")
                
                # 从数据库获取用户详细信息
                try:
                    result = supabase.table("users").select("*").eq("id", supabase_user.id).single().execute()
                    
                    if result.data:
                        user_data = result.data
                        return UserResponse(
                            id=user_data["id"],
                            email=user_data["email"],
                            full_name=user_data["full_name"],
                            role=user_data.get("role", "student"),
                            school=user_data.get("school"),
                            grade_level=user_data.get("grade_level"),
                            avatar_url=user_data.get("avatar_url"),
                            is_active=user_data.get("is_active", True),
                            settings=user_data.get("settings", {}),
                            created_at=user_data.get("created_at")
                        )
                except Exception as db_error:
                    print(f"🔄 数据库查询失败，使用基础用户信息: {db_error}")
                
                # 如果数据库中没有用户信息，创建基础用户对象
                return UserResponse(
                    id=supabase_user.id,
                    email=supabase_user.email,
                    full_name=supabase_user.user_metadata.get("full_name", "User"),
                    role="student",
                    is_active=True,
                    created_at=supabase_user.created_at
                )
                    
            except Exception as supabase_error:
                print(f"❌ Supabase认证也失败: {supabase_error}")
                # 如果所有认证方式都失败，返回401
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired token"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            print(f"❌ 认证系统错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )

# 全局认证服务实例
auth_service = AuthService()

# 依赖注入函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserResponse:
    return await auth_service.get_current_user(credentials)

async def get_current_active_user(current_user: UserResponse = Depends(get_current_user)) -> UserResponse:
    if not current_user:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user 