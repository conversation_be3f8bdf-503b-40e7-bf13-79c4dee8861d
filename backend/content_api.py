"""
内容管理API
统一管理所有学习内容：视频、笔记、问题、测验等
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel

from database import init_supabase
from auth import get_current_active_user
from models import UserResponse
from content_scraper_service import content_scraper

router = APIRouter()

# ===============================
# 请求/响应模型
# ===============================

class ContentFilterRequest(BaseModel):
    unit_id: Optional[int] = None
    topic_id: Optional[int] = None
    lesson_code: Optional[str] = None
    content_type: Optional[str] = None  # 'video', 'note', 'question', 'quiz'
    source_platform: Optional[str] = None  # 'khan_academy', 'codehs', 'youtube', 'ai_generated'
    difficulty_level: Optional[str] = None
    is_verified: Optional[bool] = None
    tags: Optional[List[str]] = None
    limit: int = 20
    offset: int = 0

class ContentResponse(BaseModel):
    id: int
    unit_id: int
    topic_id: Optional[int]
    lesson_code: Optional[str]
    title: str
    description: Optional[str]
    content_type: str
    format: str
    source_platform: str
    source_url: Optional[str]
    channel_name: Optional[str]
    difficulty_level: str
    estimated_duration: Optional[int]
    tags: List[str]
    quality_score: int
    is_verified: bool
    is_featured: bool
    view_count: int
    average_rating: float
    created_at: str
    updated_at: str

class LessonContentResponse(BaseModel):
    lesson_code: str
    lesson_title: str
    videos: List[Dict[str, Any]]
    notes: List[Dict[str, Any]]
    questions: List[Dict[str, Any]]
    total_content: int
    estimated_duration: int

class ScrapingJobResponse(BaseModel):
    job_id: str
    status: str
    progress: int
    lesson_code: Optional[str]
    total_items: int
    completed_items: int
    error_message: Optional[str]
    started_at: str
    completed_at: Optional[str]

# ===============================
# 内容查看API
# ===============================

@router.get("/content", response_model=List[ContentResponse])
async def get_learning_content(
    unit_id: Optional[int] = Query(None),
    topic_id: Optional[int] = Query(None),
    lesson_code: Optional[str] = Query(None),
    content_type: Optional[str] = Query(None),
    source_platform: Optional[str] = Query(None),
    difficulty_level: Optional[str] = Query(None),
    is_verified: Optional[bool] = Query(None),
    tags: Optional[str] = Query(None),  # 逗号分隔的标签
    limit: int = Query(20, le=100),
    offset: int = Query(0, ge=0),
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取学习内容列表"""
    try:
        supabase = init_supabase()
        
        # 构建查询
        query = supabase.table("learning_content").select("*")
        
        # 应用过滤条件
        if unit_id:
            query = query.eq("unit_id", unit_id)
        if topic_id:
            query = query.eq("topic_id", topic_id)
        if lesson_code:
            query = query.eq("lesson_code", lesson_code)
        if content_type:
            query = query.eq("content_type", content_type)
        if source_platform:
            query = query.eq("source_platform", source_platform)
        if difficulty_level:
            query = query.eq("difficulty_level", difficulty_level)
        if is_verified is not None:
            query = query.eq("is_verified", is_verified)
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            query = query.contains("tags", tag_list)
        
        # 只显示活跃内容
        query = query.eq("is_active", True)
        
        # 排序和分页
        query = query.order("created_at", desc=True).range(offset, offset + limit - 1)
        
        result = query.execute()
        
        return result.data or []
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取内容失败: {str(e)}")

@router.get("/content/{content_id}")
async def get_content_detail(
    content_id: int,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取单个内容的详细信息"""
    try:
        supabase = init_supabase()
        
        # 获取主要内容信息
        content_result = supabase.table("learning_content").select("*").eq("id", content_id).single().execute()
        
        if not content_result.data:
            raise HTTPException(status_code=404, detail="内容不存在")
        
        content = content_result.data
        response_data = {
            "content": content,
            "additional_data": {}
        }
        
        # 根据内容类型获取额外数据
        if content["content_type"] == "video":
            video_result = supabase.table("video_content").select("*").eq("learning_content_id", content_id).execute()
            if video_result.data:
                response_data["additional_data"]["video"] = video_result.data[0]
        
        elif content["content_type"] == "note":
            note_result = supabase.table("note_content").select("*").eq("learning_content_id", content_id).execute()
            if note_result.data:
                response_data["additional_data"]["note"] = note_result.data[0]
        
        elif content["content_type"] == "question":
            question_result = supabase.table("question_content").select("*").eq("learning_content_id", content_id).execute()
            if question_result.data:
                response_data["additional_data"]["question"] = question_result.data[0]
        
        # 更新查看次数
        supabase.table("learning_content").update({
            "view_count": content["view_count"] + 1
        }).eq("id", content_id).execute()
        
        # 记录用户学习进度
        await record_learning_progress(current_user.id, content_id, "viewed")
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取内容详情失败: {str(e)}")

@router.get("/lesson/{lesson_code}/content", response_model=LessonContentResponse)
async def get_lesson_content(
    lesson_code: str,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取指定lesson的所有内容"""
    try:
        supabase = init_supabase()
        
        # 获取lesson的所有内容
        content_result = supabase.table("learning_content").select("*").eq("lesson_code", lesson_code).eq("is_active", True).execute()
        
        if not content_result.data:
            raise HTTPException(status_code=404, detail=f"未找到lesson {lesson_code} 的内容")
        
        # 按类型分组
        videos = []
        notes = []
        questions = []
        total_duration = 0
        
        for content in content_result.data:
            content_type = content["content_type"]
            estimated_duration = content.get("estimated_duration", 0) or 0
            total_duration += estimated_duration
            
            content_item = {
                "id": content["id"],
                "title": content["title"],
                "description": content["description"],
                "source_platform": content["source_platform"],
                "difficulty_level": content["difficulty_level"],
                "estimated_duration": estimated_duration,
                "quality_score": content["quality_score"],
                "is_verified": content["is_verified"],
                "tags": content["tags"],
                "content_data": content["content_data"]
            }
            
            if content_type == "video":
                videos.append(content_item)
            elif content_type == "note":
                notes.append(content_item)
            elif content_type == "question":
                questions.append(content_item)
        
        # 获取lesson信息
        lesson_info = content_scraper.apcsa_lessons.get(lesson_code, {})
        lesson_title = lesson_info.get("title", f"Lesson {lesson_code}")
        
        return LessonContentResponse(
            lesson_code=lesson_code,
            lesson_title=lesson_title,
            videos=videos,
            notes=notes,
            questions=questions,
            total_content=len(content_result.data),
            estimated_duration=total_duration
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取lesson内容失败: {str(e)}")

# ===============================
# 内容抓取API
# ===============================

@router.post("/scrape/lesson/{lesson_code}")
async def scrape_lesson_content(
    lesson_code: str,
    unit_id: int,
    topic_id: Optional[int] = None,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """抓取指定lesson的所有内容"""
    try:
        # 检查lesson_code是否有效
        if lesson_code not in content_scraper.apcsa_lessons:
            raise HTTPException(status_code=400, detail=f"无效的lesson代码: {lesson_code}")
        
        # 检查是否已经抓取过
        supabase = init_supabase()
        existing_content = supabase.table("learning_content").select("id").eq("lesson_code", lesson_code).execute()
        
        if existing_content.data:
            return {
                "success": False,
                "message": f"Lesson {lesson_code} 内容已存在，共 {len(existing_content.data)} 项",
                "existing_items": len(existing_content.data)
            }
        
        # 执行抓取
        print(f"🚀 开始抓取 lesson {lesson_code} 的内容")
        result = await content_scraper.scrape_all_content_for_lesson(
            lesson_code=lesson_code,
            unit_id=unit_id,
            topic_id=topic_id
        )
        
        if result["success"]:
            return {
                "success": True,
                "message": f"成功抓取 lesson {lesson_code} 的内容",
                "lesson_code": lesson_code,
                "lesson_title": result["lesson_title"],
                "summary": {
                    "videos": len(result["videos"]),
                    "notes": len(result["notes"]),
                    "questions": len(result["questions"]),
                    "total_items": result["total_items"]
                },
                "items": {
                    "videos": result["videos"],
                    "notes": result["notes"],
                    "questions": result["questions"]
                }
            }
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "抓取失败"))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"抓取内容失败: {str(e)}")

@router.post("/scrape/all-lessons")
async def scrape_all_lessons(
    current_user: UserResponse = Depends(get_current_active_user)
):
    """批量抓取所有APCSA课程内容"""
    try:
        print("🚀 开始批量抓取所有APCSA课程内容")
        result = await content_scraper.bulk_scrape_all_lessons()
        
        return {
            "success": result["success"],
            "message": "批量抓取完成" if result["success"] else "批量抓取部分失败",
            "summary": {
                "total_lessons": result["total_lessons"],
                "completed_lessons": result["completed_lessons"],
                "failed_lessons": len(result["failed_lessons"]),
                "content_summary": result["summary"]
            },
            "failed_lessons": result["failed_lessons"] if not result["success"] else []
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量抓取失败: {str(e)}")

@router.post("/scrape/unit/{unit_id}")
async def scrape_unit_content(
    unit_id: int,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """抓取指定单元的所有内容"""
    try:
        # 获取该单元的所有lessons
        unit_lessons = [lesson_code for lesson_code in content_scraper.apcsa_lessons.keys() 
                       if lesson_code.startswith(f"{unit_id}.")]
        
        if not unit_lessons:
            raise HTTPException(status_code=404, detail=f"单元 {unit_id} 没有找到相关lessons")
        
        results = {
            "success": True,
            "unit_id": unit_id,
            "total_lessons": len(unit_lessons),
            "completed_lessons": 0,
            "failed_lessons": [],
            "summary": {
                "total_videos": 0,
                "total_notes": 0,
                "total_questions": 0
            }
        }
        
        for lesson_code in unit_lessons:
            try:
                print(f"🔄 抓取单元 {unit_id} - lesson {lesson_code}")
                lesson_result = await content_scraper.scrape_all_content_for_lesson(
                    lesson_code=lesson_code,
                    unit_id=unit_id,
                    topic_id=None
                )
                
                if lesson_result["success"]:
                    results["completed_lessons"] += 1
                    results["summary"]["total_videos"] += len(lesson_result["videos"])
                    results["summary"]["total_notes"] += len(lesson_result["notes"])
                    results["summary"]["total_questions"] += len(lesson_result["questions"])
                else:
                    results["failed_lessons"].append({
                        "lesson_code": lesson_code,
                        "error": lesson_result.get("error", "Unknown error")
                    })
                    
            except Exception as e:
                results["failed_lessons"].append({
                    "lesson_code": lesson_code,
                    "error": str(e)
                })
        
        if results["failed_lessons"]:
            results["success"] = False
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"抓取单元内容失败: {str(e)}")

# ===============================
# 内容管理API
# ===============================

@router.put("/content/{content_id}/verify")
async def verify_content(
    content_id: int,
    verification_data: Dict[str, Any],
    current_user: UserResponse = Depends(get_current_active_user)
):
    """验证和审核内容"""
    try:
        # 检查用户权限 (简化处理，实际应检查是否为教师或管理员)
        if current_user.role not in ["teacher", "admin"]:
            raise HTTPException(status_code=403, detail="没有权限执行此操作")
        
        supabase = init_supabase()
        
        # 更新验证状态
        update_data = {
            "is_verified": verification_data.get("is_verified", True),
            "quality_score": verification_data.get("quality_score"),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        # 移除None值
        update_data = {k: v for k, v in update_data.items() if v is not None}
        
        result = supabase.table("learning_content").update(update_data).eq("id", content_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="内容不存在")
        
        return {
            "success": True,
            "message": "内容验证状态已更新",
            "content_id": content_id,
            "is_verified": update_data.get("is_verified")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新验证状态失败: {str(e)}")

@router.delete("/content/{content_id}")
async def delete_content(
    content_id: int,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """删除内容 (软删除)"""
    try:
        # 检查用户权限
        if current_user.role not in ["teacher", "admin"]:
            raise HTTPException(status_code=403, detail="没有权限执行此操作")
        
        supabase = init_supabase()
        
        # 软删除 - 设置为不活跃
        result = supabase.table("learning_content").update({
            "is_active": False,
            "updated_at": datetime.utcnow().isoformat()
        }).eq("id", content_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="内容不存在")
        
        return {
            "success": True,
            "message": "内容已删除",
            "content_id": content_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除内容失败: {str(e)}")

# ===============================
# 学习进度API
# ===============================

@router.post("/content/{content_id}/progress")
async def update_learning_progress(
    content_id: int,
    progress_data: Dict[str, Any],
    current_user: UserResponse = Depends(get_current_active_user)
):
    """更新学习进度"""
    try:
        await record_learning_progress(
            student_id=current_user.id,
            content_id=content_id,
            action=progress_data.get("action", "accessed"),
            progress_percentage=progress_data.get("progress_percentage"),
            time_spent_minutes=progress_data.get("time_spent_minutes"),
            score=progress_data.get("score"),
            notes=progress_data.get("notes")
        )
        
        return {
            "success": True,
            "message": "学习进度已更新",
            "content_id": content_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新学习进度失败: {str(e)}")

@router.get("/user/progress")
async def get_user_learning_progress(
    unit_id: Optional[int] = Query(None),
    lesson_code: Optional[str] = Query(None),
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取用户学习进度"""
    try:
        supabase = init_supabase()
        
        # 构建查询
        query = supabase.table("learning_progress").select("""
            *,
            learning_content:learning_content_id (
                id, title, content_type, lesson_code, unit_id,
                estimated_duration, difficulty_level
            )
        """).eq("student_id", current_user.id)
        
        if unit_id:
            # 需要通过learning_content表过滤
            content_query = supabase.table("learning_content").select("id").eq("unit_id", unit_id).execute()
            content_ids = [item["id"] for item in content_query.data]
            if content_ids:
                query = query.in_("learning_content_id", content_ids)
            else:
                return {"progress": [], "summary": {}}
        
        if lesson_code:
            content_query = supabase.table("learning_content").select("id").eq("lesson_code", lesson_code).execute()
            content_ids = [item["id"] for item in content_query.data]
            if content_ids:
                query = query.in_("learning_content_id", content_ids)
            else:
                return {"progress": [], "summary": {}}
        
        result = query.order("last_accessed_at", desc=True).execute()
        
        progress_data = result.data or []
        
        # 计算总结统计
        summary = {
            "total_items": len(progress_data),
            "completed_items": len([p for p in progress_data if p["status"] == "completed"]),
            "in_progress_items": len([p for p in progress_data if p["status"] == "in_progress"]),
            "total_time_spent": sum(p.get("time_spent_minutes", 0) for p in progress_data),
            "average_score": sum(p.get("score", 0) for p in progress_data if p.get("score")) / max(1, len([p for p in progress_data if p.get("score")]))
        }
        
        return {
            "progress": progress_data,
            "summary": summary
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取学习进度失败: {str(e)}")

# ===============================
# 统计和分析API
# ===============================

@router.get("/stats/content")
async def get_content_statistics(
    current_user: UserResponse = Depends(get_current_active_user)
):
    """获取内容统计信息"""
    try:
        supabase = init_supabase()
        
        # 基础统计
        total_content = supabase.table("learning_content").select("id", count="exact").eq("is_active", True).execute()
        
        # 按类型统计
        content_by_type = {}
        for content_type in ["video", "note", "question", "quiz"]:
            result = supabase.table("learning_content").select("id", count="exact").eq("content_type", content_type).eq("is_active", True).execute()
            content_by_type[content_type] = result.count
        
        # 按来源统计
        content_by_source = {}
        for source in ["khan_academy", "codehs", "youtube", "ai_generated", "manual"]:
            result = supabase.table("learning_content").select("id", count="exact").eq("source_platform", source).eq("is_active", True).execute()
            content_by_source[source] = result.count
        
        # 验证状态统计
        verified_content = supabase.table("learning_content").select("id", count="exact").eq("is_verified", True).eq("is_active", True).execute()
        
        return {
            "total_content": total_content.count,
            "content_by_type": content_by_type,
            "content_by_source": content_by_source,
            "verified_content": verified_content.count,
            "verification_rate": (verified_content.count / max(1, total_content.count)) * 100
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

# ===============================
# 辅助函数
# ===============================

async def record_learning_progress(
    student_id: str, 
    content_id: int, 
    action: str = "accessed",
    progress_percentage: Optional[int] = None,
    time_spent_minutes: Optional[int] = None,
    score: Optional[int] = None,
    notes: Optional[str] = None
) -> bool:
    """记录学习进度"""
    try:
        supabase = init_supabase()
        
        # 查找现有记录
        existing_progress = supabase.table("learning_progress").select("*").eq("student_id", student_id).eq("learning_content_id", content_id).execute()
        
        current_time = datetime.utcnow().isoformat()
        
        if existing_progress.data:
            # 更新现有记录
            existing = existing_progress.data[0]
            update_data = {
                "last_accessed_at": current_time,
                "updated_at": current_time
            }
            
            if action == "started" and not existing.get("started_at"):
                update_data["started_at"] = current_time
                update_data["status"] = "in_progress"
            
            elif action == "completed":
                update_data["status"] = "completed"
                update_data["completed_at"] = current_time
                if progress_percentage is None:
                    update_data["progress_percentage"] = 100
            
            if progress_percentage is not None:
                update_data["progress_percentage"] = progress_percentage
            
            if time_spent_minutes is not None:
                update_data["time_spent_minutes"] = existing.get("time_spent_minutes", 0) + time_spent_minutes
            
            if score is not None:
                update_data["score"] = score
                update_data["attempts"] = existing.get("attempts", 0) + 1
                update_data["best_score"] = max(existing.get("best_score", 0), score)
            
            if notes is not None:
                update_data["notes"] = notes
            
            supabase.table("learning_progress").update(update_data).eq("id", existing["id"]).execute()
            
        else:
            # 创建新记录
            new_progress = {
                "student_id": student_id,
                "learning_content_id": content_id,
                "status": "in_progress" if action in ["started", "accessed"] else "not_started",
                "progress_percentage": progress_percentage or 0,
                "time_spent_minutes": time_spent_minutes or 0,
                "started_at": current_time if action == "started" else None,
                "completed_at": current_time if action == "completed" else None,
                "last_accessed_at": current_time,
                "score": score,
                "attempts": 1 if score is not None else 0,
                "best_score": score or 0,
                "notes": notes
            }
            
            supabase.table("learning_progress").insert(new_progress).execute()
        
        return True
        
    except Exception as e:
        print(f"记录学习进度失败: {e}")
        return False 