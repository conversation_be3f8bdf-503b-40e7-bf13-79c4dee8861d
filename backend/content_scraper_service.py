"""
统一内容抓取服务
从Khan Academy、CodeHS、YouTube等平台抓取学习内容
并统一存储到数据库中
"""

import os
import json
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
from datetime import datetime
import re
from database import init_supabase
from ai_service import ai_service

class ContentScraperService:
    def __init__(self):
        self.supabase = init_supabase()
        self.khan_academy_base_url = "https://www.khanacademy.org"
        self.youtube_api_key = os.getenv("YOUTUBE_API_KEY")
        self.firecrawl_api_key = os.getenv("FIRECRAWL_API_KEY")
        
        # APCSA课程映射
        self.apcsa_lessons = {
            "1.1": {"title": "Why Programming? Why Java?", "keywords": ["java", "programming", "introduction"]},
            "1.2": {"title": "Variables and Data Types", "keywords": ["variables", "data types", "int", "double", "boolean", "string"]},
            "1.3": {"title": "Expressions and Assignment Statements", "keywords": ["expressions", "assignment", "operators"]},
            "1.4": {"title": "Compound Assignment Operators", "keywords": ["compound operators", "+=", "-=", "*=", "/="]},
            "1.5": {"title": "Casting and Ranges of Variables", "keywords": ["casting", "type conversion", "ranges"]},
            "2.1": {"title": "Objects: Instances of Classes", "keywords": ["objects", "classes", "instances"]},
            "2.2": {"title": "Creating and Storing Objects", "keywords": ["constructors", "new", "object creation"]},
            "2.3": {"title": "Calling a Void Method", "keywords": ["void methods", "method calls"]},
            "2.4": {"title": "Calling a Void Method with Parameters", "keywords": ["parameters", "arguments", "void methods"]},
            "2.5": {"title": "Calling a Non-void Method", "keywords": ["return values", "non-void methods"]},
            "2.6": {"title": "String Objects: Concatenation, Literals, and More", "keywords": ["string", "concatenation", "literals"]},
            "2.7": {"title": "String Methods", "keywords": ["string methods", "length", "substring", "indexOf"]},
            "2.8": {"title": "Wrapper Classes: Integer and Double", "keywords": ["wrapper classes", "Integer", "Double", "autoboxing"]},
            "2.9": {"title": "Using the Math Class", "keywords": ["Math class", "mathematical operations", "random"]},
            "3.1": {"title": "Boolean Expressions", "keywords": ["boolean", "true", "false", "logical expressions"]},
            "3.2": {"title": "if Statements and Control Flow", "keywords": ["if statements", "control flow", "conditionals"]},
            "3.3": {"title": "if-else Statements", "keywords": ["if-else", "alternative execution"]},
            "3.4": {"title": "else if Statements", "keywords": ["else if", "multiple conditions"]},
            "3.5": {"title": "Compound Boolean Expressions", "keywords": ["&&", "||", "!", "logical operators"]},
            "3.6": {"title": "Equivalent Boolean Expressions", "keywords": ["De Morgan's laws", "boolean equivalence"]},
            "3.7": {"title": "Comparing Objects", "keywords": ["equals", "object comparison", "==", "!="]},
            "4.1": {"title": "while Loops", "keywords": ["while loops", "iteration", "loop condition"]},
            "4.2": {"title": "for Loops", "keywords": ["for loops", "initialization", "condition", "increment"]},
            "4.3": {"title": "Developing Algorithms Using Strings", "keywords": ["string algorithms", "traversal", "search"]},
            "4.4": {"title": "Nested Iteration", "keywords": ["nested loops", "2D iteration"]},
            "4.5": {"title": "Informal Code Analysis", "keywords": ["algorithm analysis", "efficiency", "complexity"]},
            "5.1": {"title": "Anatomy of a Class", "keywords": ["class definition", "fields", "methods", "constructors"]},
            "5.2": {"title": "Constructors", "keywords": ["constructors", "initialization", "default constructor"]},
            "5.3": {"title": "Documentation with Comments", "keywords": ["comments", "javadoc", "documentation"]},
            "5.4": {"title": "Accessor Methods", "keywords": ["getter methods", "accessor", "encapsulation"]},
            "5.5": {"title": "Mutator Methods", "keywords": ["setter methods", "mutator", "modification"]},
            "5.6": {"title": "Writing Methods", "keywords": ["method definition", "parameters", "return values"]},
            "5.7": {"title": "Static Variables and Methods", "keywords": ["static", "class variables", "class methods"]},
            "5.8": {"title": "Scope and Access", "keywords": ["scope", "private", "public", "access modifiers"]},
            "5.9": {"title": "this Keyword", "keywords": ["this", "self-reference", "disambiguation"]},
            "6.1": {"title": "Array Creation and Access", "keywords": ["arrays", "creation", "indexing"]},
            "6.2": {"title": "Traversing Arrays", "keywords": ["array traversal", "for loop", "enhanced for"]},
            "6.3": {"title": "Enhanced for Loop for Arrays", "keywords": ["enhanced for", "for-each", "array iteration"]},
            "6.4": {"title": "Developing Algorithms Using Arrays", "keywords": ["array algorithms", "searching", "sorting"]},
            "7.1": {"title": "Introduction to ArrayList", "keywords": ["ArrayList", "dynamic arrays", "collections"]},
            "7.2": {"title": "ArrayList Methods", "keywords": ["add", "remove", "get", "set", "size"]},
            "7.3": {"title": "Traversing ArrayLists", "keywords": ["ArrayList traversal", "iteration"]},
            "7.4": {"title": "Developing Algorithms Using ArrayLists", "keywords": ["ArrayList algorithms", "manipulation"]},
            "7.5": {"title": "Searching", "keywords": ["linear search", "binary search", "searching algorithms"]},
            "7.6": {"title": "Sorting", "keywords": ["sorting algorithms", "selection sort", "insertion sort"]},
            "7.7": {"title": "Ethical Issues Around Data Collection", "keywords": ["ethics", "data privacy", "responsible computing"]},
            "8.1": {"title": "2D Arrays", "keywords": ["2D arrays", "matrix", "row", "column"]},
            "8.2": {"title": "Traversing 2D Arrays", "keywords": ["2D array traversal", "nested loops"]},
            "9.1": {"title": "Creating Superclasses and Subclasses", "keywords": ["inheritance", "superclass", "subclass", "extends"]},
            "9.2": {"title": "Writing Constructors for Subclasses", "keywords": ["super", "constructor chaining"]},
            "9.3": {"title": "Overriding Methods", "keywords": ["method overriding", "@Override"]},
            "9.4": {"title": "super Keyword", "keywords": ["super keyword", "parent class access"]},
            "9.5": {"title": "Creating References Using Inheritance Hierarchies", "keywords": ["polymorphism", "inheritance hierarchy"]},
            "9.6": {"title": "Method Calls and Dynamic Binding", "keywords": ["dynamic binding", "runtime polymorphism"]},
            "9.7": {"title": "Object Superclass", "keywords": ["Object class", "toString", "equals"]},
            "10.1": {"title": "Recursion", "keywords": ["recursion", "base case", "recursive case"]},
            "10.2": {"title": "Recursive Searching and Sorting", "keywords": ["recursive algorithms", "merge sort", "binary search"]}
        }

    async def scrape_all_content_for_lesson(self, lesson_code: str, unit_id: int, topic_id: int = None) -> Dict[str, Any]:
        """为指定lesson抓取所有类型的内容"""
        lesson_info = self.apcsa_lessons.get(lesson_code)
        if not lesson_info:
            return {"success": False, "message": f"Unknown lesson code: {lesson_code}"}
        
        results = {
            "lesson_code": lesson_code,
            "lesson_title": lesson_info["title"],
            "videos": [],
            "notes": [],
            "questions": [],
            "success": True,
            "total_items": 0
        }
        
        # 并行抓取不同类型的内容
        tasks = [
            self.scrape_khan_academy_videos(lesson_code, lesson_info, unit_id, topic_id),
            self.scrape_codehs_notes(lesson_code, lesson_info, unit_id, topic_id),
            self.scrape_youtube_videos(lesson_code, lesson_info, unit_id, topic_id),
            self.generate_ai_questions(lesson_code, lesson_info, unit_id, topic_id)
        ]
        
        try:
            video_results, note_results, youtube_results, question_results = await asyncio.gather(*tasks)
            
            results["videos"].extend(video_results.get("items", []))
            results["videos"].extend(youtube_results.get("items", []))
            results["notes"].extend(note_results.get("items", []))
            results["questions"].extend(question_results.get("items", []))
            results["total_items"] = len(results["videos"]) + len(results["notes"]) + len(results["questions"])
            
        except Exception as e:
            results["success"] = False
            results["error"] = str(e)
        
        return results

    async def scrape_khan_academy_videos(self, lesson_code: str, lesson_info: Dict, unit_id: int, topic_id: int = None) -> Dict[str, Any]:
        """抓取Khan Academy视频内容"""
        try:
            # Khan Academy APCSA视频映射 (模拟数据，实际应该从API获取)
            ka_videos = {
                "1.1": {
                    "title": "Intro to Programming with Java",
                    "url": "https://www.khanacademy.org/computing/ap-computer-science-principles/programming-101/programming-intro/v/programming-intro",
                    "embed_code": '<iframe width="560" height="315" src="https://www.youtube.com/embed/placeholder" frameborder="0" allowfullscreen></iframe>',
                    "duration": 420,  # 7 minutes
                    "description": "Introduction to programming concepts using Java"
                },
                "1.2": {
                    "title": "Variables and Data Types in Java",
                    "url": "https://www.khanacademy.org/computing/ap-computer-science-a/programming-intro/variables-data-types/v/variables-data-types",
                    "embed_code": '<iframe width="560" height="315" src="https://www.youtube.com/embed/placeholder2" frameborder="0" allowfullscreen></iframe>',
                    "duration": 480,  # 8 minutes
                    "description": "Learn about Java variables and primitive data types"
                }
            }
            
            video_data = ka_videos.get(lesson_code)
            if not video_data:
                return {"success": False, "items": []}
            
            # 保存到数据库
            content_id = await self.save_video_content(
                unit_id=unit_id,
                topic_id=topic_id,
                lesson_code=lesson_code,
                title=video_data["title"],
                description=video_data["description"],
                source_platform="khan_academy",
                source_url=video_data["url"],
                embed_code=video_data["embed_code"],
                duration_seconds=video_data["duration"],
                channel_name="Khan Academy",
                keywords=lesson_info["keywords"]
            )
            
            return {
                "success": True,
                "items": [{
                    "content_id": content_id,
                    "title": video_data["title"],
                    "type": "video",
                    "source": "khan_academy"
                }]
            }
            
        except Exception as e:
            print(f"Khan Academy scraping error: {e}")
            return {"success": False, "items": [], "error": str(e)}

    async def scrape_youtube_videos(self, lesson_code: str, lesson_info: Dict, unit_id: int, topic_id: int = None) -> Dict[str, Any]:
        """从YouTube抓取相关教学视频"""
        try:
            # 构建搜索查询
            search_query = f"APCSA {lesson_info['title']} Java tutorial"
            
            # YouTube教育频道列表
            educational_channels = [
                "UCeVMnSShP_Iviwkknt83cww",  # CS Dojo
                "UCWr0mx597DnSGLFk1WfvSkQ",  # CS50
                "UCXAHpX2xDhmjqtA-ANgsGmw",  # CodingBat
                "UCErssr3CrjV8DLMfI-TGnTQ"   # Computer Science
            ]
            
            # 模拟YouTube搜索结果 (实际应该使用YouTube API)
            youtube_videos = {
                "1.1": [
                    {
                        "video_id": "YT_java_intro_001",
                        "title": "Java Programming Introduction - Complete Beginner Tutorial",
                        "channel": "Programming with Alex",
                        "channel_id": "UCeducational123",
                        "duration": 1200,  # 20 minutes
                        "thumbnail": "https://img.youtube.com/vi/YT_java_intro_001/maxresdefault.jpg",
                        "description": "Complete introduction to Java programming for beginners"
                    }
                ],
                "1.2": [
                    {
                        "video_id": "YT_java_vars_001",
                        "title": "Java Variables and Data Types Explained",
                        "channel": "CodeAcademy",
                        "channel_id": "UCeducational456",
                        "duration": 900,  # 15 minutes
                        "thumbnail": "https://img.youtube.com/vi/YT_java_vars_001/maxresdefault.jpg",
                        "description": "Learn about Java variables, primitive data types, and memory allocation"
                    }
                ]
            }
            
            videos = youtube_videos.get(lesson_code, [])
            saved_videos = []
            
            for video in videos:
                content_id = await self.save_video_content(
                    unit_id=unit_id,
                    topic_id=topic_id,
                    lesson_code=lesson_code,
                    title=video["title"],
                    description=video["description"],
                    source_platform="youtube",
                    source_url=f"https://www.youtube.com/watch?v={video['video_id']}",
                    embed_code=f'<iframe width="560" height="315" src="https://www.youtube.com/embed/{video["video_id"]}" frameborder="0" allowfullscreen></iframe>',
                    duration_seconds=video["duration"],
                    channel_name=video["channel"],
                    channel_id=video["channel_id"],
                    thumbnail_url=video["thumbnail"],
                    keywords=lesson_info["keywords"]
                )
                
                saved_videos.append({
                    "content_id": content_id,
                    "title": video["title"],
                    "type": "video",
                    "source": "youtube"
                })
            
            return {"success": True, "items": saved_videos}
            
        except Exception as e:
            print(f"YouTube scraping error: {e}")
            return {"success": False, "items": [], "error": str(e)}

    async def scrape_codehs_notes(self, lesson_code: str, lesson_info: Dict, unit_id: int, topic_id: int = None) -> Dict[str, Any]:
        """抓取CodeHS笔记内容"""
        try:
            # CodeHS课程笔记映射
            codehs_notes = {
                "1.1": {
                    "title": "Introduction to Programming and Java",
                    "content": """# Introduction to Programming and Java

## What is Programming?
Programming is the process of creating instructions for computers to execute. These instructions are written in programming languages that computers can understand and process.

## Why Java?
Java is a popular programming language because:
- **Platform Independent**: "Write once, run anywhere"
- **Object-Oriented**: Organizes code into classes and objects
- **Robust**: Strong memory management and error handling
- **Secure**: Built-in security features

## Java Program Structure
Every Java program has a basic structure:

```java
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
```

## Key Components:
1. **Class Declaration**: `public class HelloWorld`
2. **Main Method**: Entry point of the program
3. **Print Statement**: Outputs text to console

## AP Computer Science A Focus
In APCSA, you'll learn:
- Programming fundamentals
- Object-oriented design
- Data structures and algorithms
- Problem-solving techniques
                    """,
                    "sections": [
                        {"title": "What is Programming?", "content": "Programming basics..."},
                        {"title": "Why Java?", "content": "Java advantages..."},
                        {"title": "Program Structure", "content": "Basic Java structure..."}
                    ],
                    "code_examples": [
                        {
                            "title": "Hello World Program",
                            "code": "public class HelloWorld {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}",
                            "explanation": "This is the simplest Java program that prints 'Hello, World!' to the console."
                        }
                    ]
                },
                "1.2": {
                    "title": "Variables and Data Types",
                    "content": """# Variables and Data Types in Java

## What are Variables?
Variables are containers that store data values. In Java, each variable has a specific type that determines what kind of data it can hold.

## Primitive Data Types
Java has eight primitive data types:

### Integer Types:
- **int**: 32-bit signed integer (-2,147,483,648 to 2,147,483,647)
- **long**: 64-bit signed integer
- **short**: 16-bit signed integer
- **byte**: 8-bit signed integer

### Floating-Point Types:
- **double**: 64-bit floating-point number
- **float**: 32-bit floating-point number

### Other Types:
- **boolean**: true or false
- **char**: single 16-bit Unicode character

## Variable Declaration and Initialization

```java
// Declaration
int age;
double price;
boolean isStudent;

// Initialization
age = 18;
price = 29.99;
isStudent = true;

// Declaration and initialization together
String name = "Alice";
int score = 95;
```

## Naming Conventions
- Use camelCase for variable names
- Start with lowercase letter
- Use descriptive names
- Cannot start with numbers or contain spaces
                    """,
                    "sections": [
                        {"title": "Introduction to Variables", "content": "Variable basics..."},
                        {"title": "Primitive Data Types", "content": "Types overview..."},
                        {"title": "Declaration and Initialization", "content": "How to create variables..."}
                    ],
                    "code_examples": [
                        {
                            "title": "Variable Examples",
                            "code": "int studentCount = 25;\ndouble averageScore = 87.5;\nboolean hasHomework = true;\nString courseName = \"AP Computer Science A\";",
                            "explanation": "Examples of different variable types commonly used in programming."
                        }
                    ]
                }
            }
            
            note_data = codehs_notes.get(lesson_code)
            if not note_data:
                return {"success": False, "items": []}
            
            # 保存笔记内容到数据库
            content_id = await self.save_note_content(
                unit_id=unit_id,
                topic_id=topic_id,
                lesson_code=lesson_code,
                title=note_data["title"],
                description=f"CodeHS notes for {lesson_info['title']}",
                markdown_content=note_data["content"],
                sections=note_data["sections"],
                code_examples=note_data["code_examples"],
                keywords=lesson_info["keywords"]
            )
            
            return {
                "success": True,
                "items": [{
                    "content_id": content_id,
                    "title": note_data["title"],
                    "type": "note",
                    "source": "codehs"
                }]
            }
            
        except Exception as e:
            print(f"CodeHS scraping error: {e}")
            return {"success": False, "items": [], "error": str(e)}

    async def generate_ai_questions(self, lesson_code: str, lesson_info: Dict, unit_id: int, topic_id: int = None) -> Dict[str, Any]:
        """使用AI生成相关练习题"""
        try:
            # 构建生成问题的提示
            prompt = f"""
            为APCSA课程lesson {lesson_code}: "{lesson_info['title']}" 生成3个练习题。

            主题关键词: {', '.join(lesson_info['keywords'])}

            请生成以下类型的题目：
            1. 一个多选题 (multiple_choice)
            2. 一个编程题 (coding)
            3. 一个简答题 (short_answer)

            每个题目请包含：
            - 题目内容
            - 选项 (多选题)
            - 正确答案
            - 解释
            - 提示

            请以JSON格式返回，格式如下：
            {{
                "questions": [
                    {{
                        "type": "multiple_choice",
                        "question": "题目内容",
                        "options": ["A选项", "B选项", "C选项", "D选项"],
                        "correct_answer": "A",
                        "explanation": "解释为什么这是正确答案",
                        "hints": ["提示1", "提示2"]
                    }}
                ]
            }}
            """
            
            # 使用AI服务生成题目
            ai_result = await ai_service.interactive_qa(prompt, f"APCSA Lesson {lesson_code}")
            
            if not ai_result.get("success"):
                return {"success": False, "items": [], "error": "AI service not working"}
            
            # 解析AI返回的题目
            try:
                ai_data = json.loads(ai_result.get("answer", "{}"))
                questions = ai_data.get("questions", [])
            except Exception as e:
                print(f"AI question parsing error: {e}")
                return {"success": False, "items": [], "error": "AI service not working"}

            if not questions:
                return {"success": False, "items": [], "error": "AI service not working"}
            
            saved_questions = []
            for question in questions:
                content_id = await self.save_question_content(
                    unit_id=unit_id,
                    topic_id=topic_id,
                    lesson_code=lesson_code,
                    question_data=question,
                    keywords=lesson_info["keywords"]
                )
                
                saved_questions.append({
                    "content_id": content_id,
                    "title": f"{lesson_info['title']} - {question['type'].replace('_', ' ').title()}",
                    "type": "question",
                    "source": "ai_generated"
                })
            
            return {"success": True, "items": saved_questions}
            
        except Exception as e:
            print(f"AI question generation error: {e}")
            return {"success": False, "items": [], "error": str(e)}

    async def save_video_content(self, unit_id: int, topic_id: int, lesson_code: str, title: str, 
                                description: str, source_platform: str, source_url: str, 
                                embed_code: str, duration_seconds: int, channel_name: str,
                                channel_id: str = None, thumbnail_url: str = None, 
                                keywords: List[str] = None) -> int:
        """保存视频内容到数据库"""
        try:
            # 准备内容数据
            content_data = {
                "embed_code": embed_code,
                "description": description,
                "duration": duration_seconds,
                "thumbnail": thumbnail_url,
                "channel": channel_name
            }
            
            # 插入主要内容记录
            content_result = self.supabase.table("learning_content").insert({
                "unit_id": unit_id,
                "topic_id": topic_id,
                "lesson_code": lesson_code,
                "title": title,
                "description": description,
                "content_type": "video",
                "format": "video_embed",
                "content_data": content_data,
                "source_platform": source_platform,
                "source_url": source_url,
                "channel_name": channel_name,
                "difficulty_level": "beginner",
                "estimated_duration": duration_seconds // 60,  # 转换为分钟
                "tags": keywords or [],
                "keywords": keywords or [],
                "quality_score": 80,
                "is_verified": source_platform in ["khan_academy", "codehs"],
                "is_active": True,
                "created_by": "content_scraper",
                "last_scraped_at": datetime.utcnow().isoformat()
            }).execute()
            
            if not content_result.data:
                raise Exception("Failed to insert learning content")
            
            content_id = content_result.data[0]["id"]
            
            # 插入视频特定数据
            video_result = self.supabase.table("video_content").insert({
                "learning_content_id": content_id,
                "video_url": source_url,
                "embed_code": embed_code,
                "thumbnail_url": thumbnail_url,
                "duration_seconds": duration_seconds,
                "channel_id": channel_id,
                "channel_name": channel_name,
                "channel_url": f"https://www.youtube.com/channel/{channel_id}" if channel_id else None
            }).execute()
            
            print(f"✅ Saved video: {title} (ID: {content_id})")
            return content_id
            
        except Exception as e:
            print(f"❌ Error saving video content: {e}")
            raise

    async def save_note_content(self, unit_id: int, topic_id: int, lesson_code: str, title: str,
                               description: str, markdown_content: str, sections: List[Dict] = None,
                               code_examples: List[Dict] = None, keywords: List[str] = None) -> int:
        """保存笔记内容到数据库"""
        try:
            # 准备内容数据
            content_data = {
                "markdown": markdown_content,
                "sections": sections or [],
                "code_examples": code_examples or [],
                "estimated_reading_time": len(markdown_content) // 200  # 大约每分钟200字
            }
            
            # 插入主要内容记录
            content_result = self.supabase.table("learning_content").insert({
                "unit_id": unit_id,
                "topic_id": topic_id,
                "lesson_code": lesson_code,
                "title": title,
                "description": description,
                "content_type": "note",
                "format": "markdown",
                "content_data": content_data,
                "source_platform": "codehs",
                "difficulty_level": "beginner",
                "estimated_duration": content_data["estimated_reading_time"],
                "tags": keywords or [],
                "keywords": keywords or [],
                "quality_score": 85,
                "is_verified": True,
                "is_active": True,
                "created_by": "content_scraper",
                "last_scraped_at": datetime.utcnow().isoformat()
            }).execute()
            
            if not content_result.data:
                raise Exception("Failed to insert learning content")
            
            content_id = content_result.data[0]["id"]
            
            # 插入笔记特定数据
            note_result = self.supabase.table("note_content").insert({
                "learning_content_id": content_id,
                "content_markdown": markdown_content,
                "sections": sections or [],
                "code_examples": code_examples or []
            }).execute()
            
            print(f"✅ Saved note: {title} (ID: {content_id})")
            return content_id
            
        except Exception as e:
            print(f"❌ Error saving note content: {e}")
            raise

    async def save_question_content(self, unit_id: int, topic_id: int, lesson_code: str,
                                   question_data: Dict, keywords: List[str] = None) -> int:
        """保存问题内容到数据库"""
        try:
            question_type = question_data.get("type", "multiple_choice")
            question_text = question_data.get("question", "")
            
            # 准备内容数据
            content_data = {
                "question_type": question_type,
                "question_text": question_text,
                "data": question_data
            }
            
            # 插入主要内容记录
            content_result = self.supabase.table("learning_content").insert({
                "unit_id": unit_id,
                "topic_id": topic_id,
                "lesson_code": lesson_code,
                "title": f"{question_type.replace('_', ' ').title()} Question",
                "description": question_text[:200] + "..." if len(question_text) > 200 else question_text,
                "content_type": "question",
                "format": "json",
                "content_data": content_data,
                "source_platform": "ai_generated",
                "difficulty_level": "beginner",
                "estimated_duration": 5,  # 5分钟答题时间
                "tags": keywords or [],
                "keywords": keywords or [],
                "quality_score": 75,
                "is_verified": False,  # AI生成需要人工审核
                "is_active": True,
                "created_by": "ai_generator",
                "last_scraped_at": datetime.utcnow().isoformat()
            }).execute()
            
            if not content_result.data:
                raise Exception("Failed to insert learning content")
            
            content_id = content_result.data[0]["id"]
            
            # 插入问题特定数据
            question_result = self.supabase.table("question_content").insert({
                "learning_content_id": content_id,
                "question_type": question_type,
                "question_text": question_text,
                "question_data": question_data,
                "starter_code": question_data.get("starter_code"),
                "solution_code": question_data.get("solution"),
                "test_cases": question_data.get("test_cases"),
                "points": 10,
                "explanation": question_data.get("explanation"),
                "hints": question_data.get("hints", [])
            }).execute()
            
            print(f"✅ Saved question: {question_type} (ID: {content_id})")
            return content_id
            
        except Exception as e:
            print(f"❌ Error saving question content: {e}")
            raise

    async def bulk_scrape_all_lessons(self) -> Dict[str, Any]:
        """批量抓取所有APCSA课程内容"""
        results = {
            "success": True,
            "total_lessons": len(self.apcsa_lessons),
            "completed_lessons": 0,
            "failed_lessons": [],
            "summary": {
                "total_videos": 0,
                "total_notes": 0,
                "total_questions": 0
            }
        }
        
        for lesson_code, lesson_info in self.apcsa_lessons.items():
            try:
                print(f"🔄 Scraping lesson {lesson_code}: {lesson_info['title']}")
                
                # 确定unit_id (从lesson_code解析)
                unit_num = int(lesson_code.split('.')[0])
                
                lesson_result = await self.scrape_all_content_for_lesson(
                    lesson_code=lesson_code,
                    unit_id=unit_num,
                    topic_id=None  # 可以后续添加topic映射
                )
                
                if lesson_result["success"]:
                    results["completed_lessons"] += 1
                    results["summary"]["total_videos"] += len(lesson_result["videos"])
                    results["summary"]["total_notes"] += len(lesson_result["notes"])
                    results["summary"]["total_questions"] += len(lesson_result["questions"])
                    print(f"✅ Completed lesson {lesson_code}")
                else:
                    results["failed_lessons"].append({
                        "lesson_code": lesson_code,
                        "error": lesson_result.get("error", "Unknown error")
                    })
                    print(f"❌ Failed lesson {lesson_code}")
                
                # 添加延迟避免API限制
                await asyncio.sleep(1)
                
            except Exception as e:
                results["failed_lessons"].append({
                    "lesson_code": lesson_code,
                    "error": str(e)
                })
                print(f"❌ Exception in lesson {lesson_code}: {e}")
        
        if results["failed_lessons"]:
            results["success"] = False
        
        return results

# 全局实例
content_scraper = ContentScraperService() 