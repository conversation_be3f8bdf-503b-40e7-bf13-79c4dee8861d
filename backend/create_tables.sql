-- =============================================
-- BoruiCourses 数据库表创建脚本
-- 在 Supabase Dashboard SQL Editor 中执行
-- =============================================


-- Chatbot 对话记录表
CREATE TABLE IF NOT EXISTS chatbot_conversations (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    conversation_type VARCHAR(50) DEFAULT 'general',
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    context_topic VARCHAR(200),
    context_unit VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_chatbot_user ON chatbot_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_type ON chatbot_conversations(conversation_type);


-- 学习内容主表
CREATE TABLE IF NOT EXISTS learning_content (
    id SERIAL PRIMARY KEY,
    unit_id VARCHAR(50) NOT NULL,
    topic_id UUID,
    lesson_code VARCHAR(50),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    content_type VARCHAR(50) NOT NULL,
    format VARCHAR(50),
    content_data JSONB NOT NULL,
    source_platform VARCHAR(100),
    source_url TEXT,
    external_id VARCHAR(200),
    channel_name VARCHAR(200),
    author_name VARCHAR(200),
    difficulty_level VARCHAR(20) DEFAULT 'beginner',
    estimated_duration INTEGER,
    learning_objectives TEXT[],
    prerequisites TEXT[],
    tags TEXT[],
    keywords TEXT[],
    quality_score INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    view_count INTEGER DEFAULT 0,
    completion_count INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_scraped_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT unique_external_content UNIQUE(source_platform, external_id)
);

CREATE INDEX IF NOT EXISTS idx_learning_content_unit_topic ON learning_content(unit_id, topic_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_learning_content_source ON learning_content(source_platform);
CREATE INDEX IF NOT EXISTS idx_learning_content_lesson ON learning_content(lesson_code);
CREATE INDEX IF NOT EXISTS idx_learning_content_active ON learning_content(is_active, is_verified);


-- 视频内容表
CREATE TABLE IF NOT EXISTS video_content (
    id SERIAL PRIMARY KEY,
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    video_url TEXT NOT NULL,
    embed_code TEXT,
    thumbnail_url TEXT,
    duration_seconds INTEGER,
    video_quality VARCHAR(20),
    channel_id VARCHAR(200),
    channel_name VARCHAR(200),
    channel_url TEXT,
    view_count_external INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    upload_date TIMESTAMP WITH TIME ZONE,
    has_subtitles BOOLEAN DEFAULT FALSE,
    subtitle_languages TEXT[],
    transcript TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_video_content_channel ON video_content(channel_id);
CREATE INDEX IF NOT EXISTS idx_video_content_duration ON video_content(duration_seconds);


-- 笔记内容表
CREATE TABLE IF NOT EXISTS note_content (
    id SERIAL PRIMARY KEY,
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    content_markdown TEXT NOT NULL,
    content_html TEXT,
    sections JSONB,
    code_examples JSONB,
    diagrams JSONB,
    version INTEGER DEFAULT 1,
    parent_version_id INTEGER REFERENCES note_content(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);


-- 问题内容表
CREATE TABLE IF NOT EXISTS question_content (
    id SERIAL PRIMARY KEY,
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    question_type VARCHAR(50) NOT NULL,
    question_text TEXT NOT NULL,
    question_data JSONB NOT NULL,
    starter_code TEXT,
    solution_code TEXT,
    test_cases JSONB,
    points INTEGER DEFAULT 1,
    time_limit_minutes INTEGER,
    explanation TEXT,
    hints TEXT[],
    common_mistakes JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_question_content_type ON question_content(question_type);


-- 学习进度表
CREATE TABLE IF NOT EXISTS learning_progress (
    id SERIAL PRIMARY KEY,
    student_id UUID NOT NULL,
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id),
    status VARCHAR(20) DEFAULT 'not_started',
    progress_percentage INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    score INTEGER,
    attempts INTEGER DEFAULT 0,
    best_score INTEGER DEFAULT 0,
    notes TEXT,
    bookmarked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, learning_content_id)
);

CREATE INDEX IF NOT EXISTS idx_learning_progress_student ON learning_progress(student_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_status ON learning_progress(status);
CREATE INDEX IF NOT EXISTS idx_learning_progress_completed ON learning_progress(completed_at);


-- 更新时间戳触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE TRIGGER IF NOT EXISTS update_learning_content_updated_at 
    BEFORE UPDATE ON learning_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_video_content_updated_at 
    BEFORE UPDATE ON video_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_note_content_updated_at 
    BEFORE UPDATE ON note_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_question_content_updated_at 
    BEFORE UPDATE ON question_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_learning_progress_updated_at 
    BEFORE UPDATE ON learning_progress 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
