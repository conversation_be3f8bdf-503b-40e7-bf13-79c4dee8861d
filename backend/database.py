import os
from supabase import create_client, Client
from dotenv import load_dotenv
import pytz
from datetime import datetime

load_dotenv()

# 时区配置
TIMEZONE = os.getenv("TIMEZONE", "America/Vancouver")
vancouver_tz = pytz.timezone(TIMEZONE)

# Supabase 配置
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")

print(f"🔧 Debug: SUPABASE_URL = {SUPABASE_URL}")
print(f"🔧 Debug: SUPABASE_KEY = {SUPABASE_KEY[:20]}..." if SUPABASE_KEY else "🔧 Debug: SUPABASE_KEY = None")
print(f"🌍 Debug: TIMEZONE = {TIMEZONE}")

def get_current_time():
    """获取当前温哥华时间"""
    return datetime.now(vancouver_tz)

def localize_datetime(dt):
    """将 UTC 时间转换为温哥华时间"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        # 假设是 UTC 时间
        utc_dt = pytz.utc.localize(dt)
    else:
        utc_dt = dt.astimezone(pytz.utc)
    return utc_dt.astimezone(vancouver_tz)

# 创建 Supabase 客户端
def get_supabase_client() -> Client:
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError(f"Missing Supabase credentials. URL: {bool(SUPABASE_URL)}, KEY: {bool(SUPABASE_KEY)}")
    
    if not SUPABASE_URL.startswith('https://'):
        raise ValueError(f"Invalid Supabase URL format: {SUPABASE_URL}")
    
    try:
        # 使用最简单的客户端初始化
        client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase client created successfully")
        return client
    except Exception as e:
        print(f"❌ Failed to create Supabase client: {e}")
        print(f"❌ Error type: {type(e)}")
        raise

# 延迟初始化客户端
supabase = None

def init_supabase():
    global supabase
    if supabase is None:
        supabase = get_supabase_client()
        print("✅ Supabase client initialized successfully")
        print(f"🌍 Current Vancouver time: {get_current_time().strftime('%Y-%m-%d %H:%M:%S %Z')}")
    return supabase 