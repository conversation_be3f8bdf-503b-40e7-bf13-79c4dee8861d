"""
文档库系统 - Document Library System
自动保存AI对话记录，分门别类管理学习内容
"""

import os
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import asyncio
from pathlib import Path

@dataclass
class DocumentEntry:
    """文档条目数据结构"""
    id: str
    type: str  # 'qa', 'concept', 'hint', 'quiz', 'exercise'
    category: str  # Unit, Topic等分类
    title: str
    content: Dict[str, Any]
    tags: List[str]
    created_at: str
    updated_at: str
    user_id: Optional[str] = None
    unit_id: Optional[str] = None
    topic_id: Optional[str] = None

class DocumentLibrary:
    """文档库管理类"""
    
    def __init__(self):
        self.base_path = Path("document_library")
        self.base_path.mkdir(exist_ok=True)
        
        # 创建子目录
        self.categories = {
            'qa': 'questions_answers',
            'concept': 'concept_explanations', 
            'hint': 'programming_hints',
            'quiz': 'quiz_questions',
            'exercise': 'exercise_solutions'
        }
        
        for category in self.categories.values():
            (self.base_path / category).mkdir(exist_ok=True)
            
        print("📚 文档库系统已初始化")

    async def save_document(self, doc_type: str, data: Dict[str, Any], 
                          unit_id: str = None, topic: str = None, 
                          user_id: str = None) -> str:
        """保存文档到库中"""
        try:
            # 生成文档ID
            doc_id = str(uuid.uuid4())
            current_time = datetime.now(timezone.utc).isoformat()
            
            # 根据类型确定标题和分类
            title, category, tags = self._generate_metadata(doc_type, data, topic)
            
            # 创建文档条目
            document = DocumentEntry(
                id=doc_id,
                type=doc_type,
                category=category,
                title=title,
                content=data,
                tags=tags,
                created_at=current_time,
                updated_at=current_time,
                user_id=user_id,
                unit_id=unit_id,
                topic_id=topic
            )
            
            # 保存到文件
            category_path = self.base_path / self.categories.get(doc_type, 'general')
            file_path = category_path / f"{doc_id}.json"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(document), f, ensure_ascii=False, indent=2)
            
            # 更新索引
            await self._update_index(document)
            
            print(f"📝 文档已保存: {title} ({doc_type})")
            return doc_id
            
        except Exception as e:
            print(f"❌ 保存文档失败: {e}")
            return None

    def _generate_metadata(self, doc_type: str, data: Dict[str, Any], 
                          topic: str = None) -> tuple:
        """生成文档元数据"""
        
        if doc_type == 'qa':
            question = data.get('question', 'Unknown Question')
            title = f"Q: {question[:50]}..." if len(question) > 50 else f"Q: {question}"
            category = f"Unit 1 - {topic}" if topic else "General QA"
            tags = ['question', 'answer', 'java', 'apcsa']
            
            # 根据问题内容添加特定标签
            question_lower = question.lower()
            if 'variable' in question_lower:
                tags.append('variables')
            if 'data type' in question_lower or 'datatype' in question_lower:
                tags.append('data-types')
            if 'method' in question_lower:
                tags.append('methods')
            if 'loop' in question_lower:
                tags.append('loops')
                
        elif doc_type == 'concept':
            concept = data.get('concept', 'Unknown Concept')
            title = f"概念解释: {concept}"
            category = f"Unit 1 - {topic}" if topic else "Concept Explanations"
            tags = ['concept', 'explanation', concept.lower(), 'java']
            
        elif doc_type == 'hint':
            exercise = data.get('exercise_description', 'Programming Hint')
            title = f"编程提示: {exercise[:30]}..." if len(exercise) > 30 else f"编程提示: {exercise}"
            category = f"Unit 1 - {topic}" if topic else "Programming Hints"
            tags = ['hint', 'programming', 'help', 'java']
            
        else:
            title = f"{doc_type.title()}: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            category = "General"
            tags = [doc_type]
            
        return title, category, tags

    async def _update_index(self, document: DocumentEntry):
        """更新文档索引"""
        try:
            index_file = self.base_path / "index.json"
            
            # 读取现有索引
            if index_file.exists():
                with open(index_file, 'r', encoding='utf-8') as f:
                    index = json.load(f)
            else:
                index = {
                    'total_documents': 0,
                    'categories': {},
                    'tags': {},
                    'recent_documents': []
                }
            
            # 更新统计
            index['total_documents'] += 1
            
            # 更新分类统计
            if document.category not in index['categories']:
                index['categories'][document.category] = 0
            index['categories'][document.category] += 1
            
            # 更新标签统计
            for tag in document.tags:
                if tag not in index['tags']:
                    index['tags'][tag] = 0
                index['tags'][tag] += 1
            
            # 更新最近文档列表
            recent_doc = {
                'id': document.id,
                'title': document.title,
                'type': document.type,
                'created_at': document.created_at
            }
            index['recent_documents'].insert(0, recent_doc)
            
            # 保持最近文档列表在50以内
            if len(index['recent_documents']) > 50:
                index['recent_documents'] = index['recent_documents'][:50]
            
            # 保存索引
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(index, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ 更新索引失败: {e}")

    async def search_documents(self, query: str = None, doc_type: str = None, 
                             category: str = None, tags: List[str] = None,
                             limit: int = 10) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            results = []
            
            # 遍历所有文档类型目录
            for category_dir in self.categories.values():
                category_path = self.base_path / category_dir
                if not category_path.exists():
                    continue
                    
                for json_file in category_path.glob("*.json"):
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            doc = json.load(f)
                        
                        # 应用过滤条件
                        if doc_type and doc.get('type') != doc_type:
                            continue
                        if category and category.lower() not in doc.get('category', '').lower():
                            continue
                        if tags and not any(tag in doc.get('tags', []) for tag in tags):
                            continue
                        if query and not self._match_query(doc, query):
                            continue
                            
                        results.append(doc)
                        
                    except Exception as e:
                        print(f"❌ 读取文档失败 {json_file}: {e}")
                        continue
            
            # 按创建时间排序
            results.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return results[:limit]
            
        except Exception as e:
            print(f"❌ 搜索文档失败: {e}")
            return []

    def _match_query(self, doc: Dict[str, Any], query: str) -> bool:
        """检查文档是否匹配查询"""
        query_lower = query.lower()
        
        # 搜索标题
        if query_lower in doc.get('title', '').lower():
            return True
            
        # 搜索标签
        for tag in doc.get('tags', []):
            if query_lower in tag.lower():
                return True
                
        # 搜索内容
        content = doc.get('content', {})
        for key, value in content.items():
            if isinstance(value, str) and query_lower in value.lower():
                return True
                
        return False

    async def get_document_stats(self) -> Dict[str, Any]:
        """获取文档库统计信息"""
        try:
            index_file = self.base_path / "index.json"
            
            if index_file.exists():
                with open(index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    'total_documents': 0,
                    'categories': {},
                    'tags': {},
                    'recent_documents': []
                }
        except Exception as e:
            print(f"❌ 获取统计失败: {e}")
            return {}

    async def generate_quiz_from_library(self, topic: str = None, 
                                       question_count: int = 5) -> List[Dict[str, Any]]:
        """从文档库生成测验题目"""
        try:
            # 搜索相关的QA文档
            qa_docs = await self.search_documents(
                doc_type='qa',
                query=topic,
                limit=question_count * 2  # 获取更多以便筛选
            )
            
            quiz_questions = []
            for doc in qa_docs[:question_count]:
                content = doc.get('content', {})
                question = content.get('question', '')
                answer = content.get('answer', '')
                
                if question and answer:
                    quiz_questions.append({
                        'question': question,
                        'correct_answer': self._extract_key_points(answer),
                        'options': self._generate_options(answer),
                        'source': 'document_library',
                        'difficulty': 'medium',
                        'tags': doc.get('tags', [])
                    })
            
            return quiz_questions
            
        except Exception as e:
            print(f"❌ 生成测验失败: {e}")
            return []

    def _extract_key_points(self, answer: str) -> str:
        """从答案中提取关键点"""
        # 简单的关键点提取逻辑
        sentences = answer.split('。')
        if sentences:
            return sentences[0] + '。'
        return answer[:100] + '...' if len(answer) > 100 else answer

    def _generate_options(self, correct_answer: str) -> List[str]:
        """生成选择题选项"""
        key_point = self._extract_key_points(correct_answer)
        
        # 这里可以集成AI来生成干扰项
        # 目前使用简单的模板
        options = [
            key_point,
            "这个概念与面向对象编程相关",
            "这是Java语言特有的特性",
            "这属于高级编程概念"
        ]
        
        return options

# 全局文档库实例
document_library = DocumentLibrary() 