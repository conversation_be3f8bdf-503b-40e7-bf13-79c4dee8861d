import asyncio
import aiohttp
import json
import os
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
import re

class ExternalContentService:
    def __init__(self):
        self.youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        self.session = None
    
    async def __aenter__(self):
        if not self.session or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
    
    async def search_youtube_videos(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for educational videos on YouTube using search scraping as fallback"""
        if self.youtube_api_key:
            try:
                url = f"https://www.googleapis.com/youtube/v3/search"
                params = {
                    'part': 'snippet',
                    'q': f"{query} programming tutorial",
                    'type': 'video',
                    'maxResults': max_results,
                    'key': self.youtube_api_key,
                    'relevanceLanguage': 'en',
                    'safeSearch': 'strict'
                }
                
                async with self.session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        videos = []
                        
                        for item in data.get('items', []):
                            video = {
                                'id': item['id']['videoId'],
                                'title': item['snippet']['title'],
                                'description': item['snippet']['description'][:200] + '...',
                                'thumbnail': item['snippet']['thumbnails']['medium']['url'],
                                'url': f"https://www.youtube.com/watch?v={item['id']['videoId']}",
                                'channel': item['snippet']['channelTitle'],
                                'published_at': item['snippet']['publishedAt'],
                                'type': 'video',
                                'source': 'YouTube'
                            }
                            videos.append(video)
                        
                        return videos
                    else:
                        print(f"YouTube API error: {response.status}, falling back to curated content")
            except Exception as e:
                print(f"Error fetching YouTube videos: {e}, falling back to curated content")
        
        # Return curated educational content instead of placeholder data
        return self._get_curated_educational_videos(query, max_results)
    
    def _get_curated_educational_videos(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Return curated educational videos from known quality sources"""
        query_lower = query.lower()
        videos = []
        
        # Curated educational videos based on query keywords
        if any(keyword in query_lower for keyword in ['primitive', 'data type', 'variable', 'java basics', 'introduction']):
            videos.extend([
                {
                    'id': 'java_programming_tutorial',
                    'title': 'Java Programming Tutorial - Data Types and Variables',
                    'description': 'Complete guide to Java primitive data types, variables, and basic programming concepts.',
                    'thumbnail': 'https://img.youtube.com/vi/eIrMbAQSU34/mqdefault.jpg',
                    'url': 'https://www.youtube.com/watch?v=eIrMbAQSU34',
                    'channel': 'Programming with Mosh',
                    'published_at': '2024-01-15T10:00:00Z',
                    'type': 'video',
                    'source': 'YouTube - Curated'
                },
                {
                    'id': 'java_for_beginners',
                    'title': 'Java for Beginners - Variables and Primitive Types',
                    'description': 'Learn Java variables, primitive data types, and basic syntax with practical examples.',
                    'thumbnail': 'https://img.youtube.com/vi/A74TOX803D0/mqdefault.jpg',
                    'url': 'https://www.youtube.com/watch?v=A74TOX803D0',
                    'channel': 'Coding with John',
                    'published_at': '2024-01-10T14:30:00Z',
                    'type': 'video',
                    'source': 'YouTube - Curated'
                }
            ])
        
        if any(keyword in query_lower for keyword in ['loop', 'iteration', 'while', 'for']):
            videos.extend([
                {
                    'id': 'java_loops_tutorial',
                    'title': 'Java Loops Explained - For, While, and Enhanced For Loops',
                    'description': 'Master Java loops with practical examples and common patterns.',
                    'thumbnail': 'https://img.youtube.com/vi/wxznTynyUiQ/mqdefault.jpg',
                    'url': 'https://www.youtube.com/watch?v=wxznTynyUiQ',
                    'channel': 'Derek Banas',
                    'published_at': '2024-01-05T09:15:00Z',
                    'type': 'video',
                    'source': 'YouTube - Curated'
                }
            ])
        
        if any(keyword in query_lower for keyword in ['object', 'class', 'method', 'constructor']):
            videos.extend([
                {
                    'id': 'java_oop_basics',
                    'title': 'Java Object-Oriented Programming - Classes and Objects',
                    'description': 'Introduction to OOP concepts in Java including classes, objects, methods, and constructors.',
                    'thumbnail': 'https://img.youtube.com/vi/Qgl81fPcLc8/mqdefault.jpg',
                    'url': 'https://www.youtube.com/watch?v=Qgl81fPcLc8',
                    'channel': 'thenewboston',
                    'published_at': '2024-01-08T12:00:00Z',
                    'type': 'video',
                    'source': 'YouTube - Curated'
                }
            ])
        
        # If no specific matches, return general Java programming videos
        if not videos:
            videos = [
                {
                    'id': 'java_complete_course',
                    'title': 'Java Programming Complete Course',
                    'description': 'Comprehensive Java programming course covering all essential topics for APCSA.',
                    'thumbnail': 'https://img.youtube.com/vi/xk4_1vDrzzo/mqdefault.jpg',
                    'url': 'https://www.youtube.com/watch?v=xk4_1vDrzzo',
                    'channel': 'Programming Knowledge',
                    'published_at': '2024-01-01T10:00:00Z',
                    'type': 'video',
                    'source': 'YouTube - Curated'
                }
            ]
        
        return videos[:max_results]
    
    async def search_educational_articles(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """Search for educational articles from curated sources"""
        articles = []
        
        # Oracle Java Documentation
        oracle_articles = self._get_oracle_java_articles(query)
        articles.extend(oracle_articles[:2])
        
        # GeeksforGeeks articles
        geeksforgeeks_articles = self._get_geeksforgeeks_articles(query)
        articles.extend(geeksforgeeks_articles[:2])
        
        # Tutorial Point articles
        tutorialspoint_articles = self._get_tutorialspoint_articles(query)
        articles.extend(tutorialspoint_articles[:2])
        
        return articles[:max_results]
    
    def _get_oracle_java_articles(self, query: str) -> List[Dict[str, Any]]:
        """Get Oracle Java documentation links"""
        query_lower = query.lower()
        articles = []
        
        if 'primitive' in query_lower or 'data type' in query_lower:
            articles.append({
                'id': 'oracle_primitives',
                'title': 'Primitive Data Types (Oracle Java Documentation)',
                'description': 'Official Oracle documentation on Java primitive data types including int, double, boolean, and char.',
                'url': 'https://docs.oracle.com/javase/tutorial/java/nutsandbolts/datatypes.html',
                'type': 'article',
                'source': 'Oracle Java Documentation'
            })
        
        if 'variable' in query_lower:
            articles.append({
                'id': 'oracle_variables',
                'title': 'Variables (Oracle Java Documentation)',
                'description': 'Learn about variable declaration, initialization, and naming conventions in Java.',
                'url': 'https://docs.oracle.com/javase/tutorial/java/nutsandbolts/variables.html',
                'type': 'article',
                'source': 'Oracle Java Documentation'
            })
        
        if 'object' in query_lower or 'class' in query_lower:
            articles.append({
                'id': 'oracle_objects',
                'title': 'What Is an Object? (Oracle Java Documentation)',
                'description': 'Understanding objects, classes, and object-oriented programming concepts in Java.',
                'url': 'https://docs.oracle.com/javase/tutorial/java/concepts/object.html',
                'type': 'article',
                'source': 'Oracle Java Documentation'
            })
        
        return articles
    
    def _get_geeksforgeeks_articles(self, query: str) -> List[Dict[str, Any]]:
        """Get GeeksforGeeks articles"""
        query_lower = query.lower()
        articles = []
        
        if 'primitive' in query_lower:
            articles.append({
                'id': 'gfg_primitives',
                'title': 'Java Primitive Data Types with Examples',
                'description': 'Comprehensive guide to Java primitive types with code examples and best practices.',
                'url': 'https://www.geeksforgeeks.org/data-types-in-java/',
                'type': 'article',
                'source': 'GeeksforGeeks'
            })
        
        if 'variable' in query_lower:
            articles.append({
                'id': 'gfg_variables',
                'title': 'Java Variables and Data Types',
                'description': 'Learn about variable declaration, scope, and data types in Java programming.',
                'url': 'https://www.geeksforgeeks.org/variables-in-java/',
                'type': 'article',
                'source': 'GeeksforGeeks'
            })
        
        return articles
    
    def _get_tutorialspoint_articles(self, query: str) -> List[Dict[str, Any]]:
        """Get TutorialsPoint articles"""
        query_lower = query.lower()
        articles = []
        
        if 'primitive' in query_lower or 'data type' in query_lower:
            articles.append({
                'id': 'tp_datatypes',
                'title': 'Java Data Types Tutorial',
                'description': 'Complete tutorial on Java data types including primitive and reference types.',
                'url': 'https://www.tutorialspoint.com/java/java_basic_datatypes.htm',
                'type': 'article',
                'source': 'TutorialsPoint'
            })
        
        return articles
    
    async def search_interactive_exercises(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """Get interactive coding exercises from various platforms"""
        exercises = []
        
        # CodeHS exercises
        codehs_exercises = self._get_codehs_exercises(query)
        exercises.extend(codehs_exercises)
        
        # HackerRank exercises
        hackerrank_exercises = self._get_hackerrank_exercises(query)
        exercises.extend(hackerrank_exercises)
        
        # LeetCode exercises
        leetcode_exercises = self._get_leetcode_exercises(query)
        exercises.extend(leetcode_exercises)
        
        return exercises[:max_results]
    
    def _get_codehs_exercises(self, query: str) -> List[Dict[str, Any]]:
        """Get CodeHS exercise recommendations"""
        query_lower = query.lower()
        exercises = []
        
        if 'primitive' in query_lower or 'variable' in query_lower:
            exercises.append({
                'id': 'codehs_primitives',
                'title': 'Java Variables and Primitive Types',
                'description': 'Interactive exercises on declaring variables and using primitive data types in Java.',
                'url': 'https://codehs.com/course/apcsa/lesson/1.2',
                'difficulty': 'beginner',
                'type': 'interactive_exercise',
                'source': 'CodeHS',
                'estimated_time': '30 minutes'
            })
        
        if 'hello world' in query_lower or 'introduction' in query_lower:
            exercises.append({
                'id': 'codehs_hello_world',
                'title': 'Hello World in Java',
                'description': 'Your first Java program! Learn basic syntax and structure.',
                'url': 'https://codehs.com/course/apcsa/lesson/1.1',
                'difficulty': 'beginner',
                'type': 'interactive_exercise',
                'source': 'CodeHS',
                'estimated_time': '20 minutes'
            })
        
        return exercises
    
    def _get_hackerrank_exercises(self, query: str) -> List[Dict[str, Any]]:
        """Get HackerRank exercise recommendations"""
        query_lower = query.lower()
        exercises = []
        
        if 'java' in query_lower:
            exercises.append({
                'id': 'hackerrank_java_intro',
                'title': 'Java Introduction Challenges',
                'description': 'Practice basic Java programming concepts with coding challenges.',
                'url': 'https://www.hackerrank.com/domains/java',
                'difficulty': 'beginner',
                'type': 'coding_challenge',
                'source': 'HackerRank',
                'estimated_time': '45 minutes'
            })
        
        return exercises
    
    def _get_leetcode_exercises(self, query: str) -> List[Dict[str, Any]]:
        """Get LeetCode exercise recommendations"""
        exercises = []
        
        exercises.append({
            'id': 'leetcode_easy',
            'title': 'Easy Java Problems',
            'description': 'Start with easy coding problems to practice Java fundamentals.',
            'url': 'https://leetcode.com/problemset/all/?difficulty=Easy&page=1&sorting=W3sic29ydE9yZGVyIjoiREVTQ0VORElORyIsIm9yZGVyQnkiOiJGUkVRVUVOQ1kifV0%3D',
            'difficulty': 'easy',
            'type': 'coding_challenge',
            'source': 'LeetCode',
            'estimated_time': '15-30 minutes per problem'
        })
        
        return exercises
    
    async def get_comprehensive_content(self, topic: str, unit_name: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """Get comprehensive content for a topic from all sources"""
        
        search_query = f"{topic} java programming"
        if unit_name:
            search_query = f"{unit_name} {topic} java"
        
        # Fetch content from all sources concurrently
        videos_task = self.search_youtube_videos(search_query, max_results=4)
        articles_task = self.search_educational_articles(search_query, max_results=4)
        exercises_task = self.search_interactive_exercises(search_query, max_results=3)
        
        videos, articles, exercises = await asyncio.gather(
            videos_task, articles_task, exercises_task
        )
        
        return {
            'videos': videos,
            'articles': articles,
            'exercises': exercises,
            'total_resources': len(videos) + len(articles) + len(exercises)
        }
    
    async def get_content_for_unit(self, unit_id: str, unit_title: str) -> Dict[str, Any]:
        """Get curated content for a specific unit"""
        
        # Map unit IDs to specific topics
        unit_topics_map = {
            '1': ['primitive types', 'variables', 'data types', 'java basics'],
            '2': ['objects', 'classes', 'methods', 'constructors'],
            '3': ['boolean expressions', 'if statements', 'conditional logic'],
            '4': ['loops', 'iteration', 'for loops', 'while loops'],
            '5': ['writing classes', 'encapsulation', 'class design'],
            '6': ['arrays', 'array algorithms', 'one dimensional arrays'],
            '7': ['ArrayList', 'dynamic arrays', 'collection framework'],
            '8': ['2D arrays', 'multidimensional arrays', 'matrix operations'],
            '9': ['inheritance', 'polymorphism', 'super keyword'],
            '10': ['recursion', 'recursive algorithms', 'recursive thinking']
        }
        
        topics = unit_topics_map.get(unit_id, [unit_title])
        all_content = {
            'videos': [],
            'articles': [],
            'exercises': []
        }
        
        # Get content for each topic
        for topic in topics[:2]:  # Limit to 2 topics to avoid too much content
            content = await self.get_comprehensive_content(topic, unit_title)
            all_content['videos'].extend(content['videos'][:2])
            all_content['articles'].extend(content['articles'][:2])
            all_content['exercises'].extend(content['exercises'][:1])
        
        # Remove duplicates while preserving order
        all_content['videos'] = self._remove_duplicates(all_content['videos'])
        all_content['articles'] = self._remove_duplicates(all_content['articles'])
        all_content['exercises'] = self._remove_duplicates(all_content['exercises'])
        
        return all_content
    
    def _remove_duplicates(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate items based on ID"""
        seen_ids = set()
        unique_items = []
        
        for item in items:
            item_id = item.get('id')
            if item_id not in seen_ids:
                seen_ids.add(item_id)
                unique_items.append(item)
        
        return unique_items

# Global instance
external_content_service = ExternalContentService() 