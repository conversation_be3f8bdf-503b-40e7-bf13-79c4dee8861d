#!/usr/bin/env python3
"""
Firecrawl Service for APCSA Learning Platform
Advanced web scraping and content collection service
"""

import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
import os
import json
import re
from datetime import datetime
from fastapi import HTTPException

try:
    from firecrawl import FirecrawlApp
    FIRECRAWL_AVAILABLE = True
except ImportError:
    print("⚠️ Firecrawl not installed. Run: pip install firecrawl-py")
    FIRECRAWL_AVAILABLE = False

class FirecrawlService:
    """Enhanced Firecrawl service for educational content collection"""
    
    def __init__(self):
        self.api_key = os.getenv("FIRECRAWL_API_KEY")
        self.base_url = os.getenv("FIRECRAWL_BASE_URL", "https://api.firecrawl.dev")
        
        if self.api_key and FIRECRAWL_AVAILABLE:
            try:
                self.app = FirecrawlApp(api_key=self.api_key)
                print("✅ Firecrawl initialized with API key")
            except Exception as e:
                print(f"⚠️ Firecrawl initialization error: {e}")
                self.app = None
        else:
            print("⚠️ Firecrawl API key not found or package not installed. Using demo mode.")
            self.app = None
    
    async def scrape_educational_resource(self, url: str, options: Dict = None) -> Dict[str, Any]:
        """Scrape a single educational resource"""
        if not self.app:
            raise HTTPException(status_code=503, detail="Firecrawl service not configured. Please set FIRECRAWL_API_KEY environment variable.")
        
        try:
            # Configure scraping options for educational content
            scrape_options = {
                'formats': ['markdown', 'html'],
                'onlyMainContent': True,
                'includeLinkTags': True,
                'includeImageTags': True
            }
            
            if options:
                scrape_options.update(options)
            
            print(f"🔍 Scraping educational resource: {url}")
            result = self.app.scrape_url(url, scrape_options)
            
            return {
                'success': True,
                'url': url,
                'title': result.get('metadata', {}).get('title', 'Untitled'),
                'description': result.get('metadata', {}).get('description', ''),
                'markdown': result.get('markdown', ''),
                'html': result.get('html', ''),
                'metadata': result.get('metadata', {}),
                'links': self._extract_links(result.get('markdown', '')),
                'images': self._extract_images(result.get('markdown', '')),
                'scraped_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Firecrawl scraping error for {url}: {e}")
            raise HTTPException(status_code=500, detail=f"Firecrawl scraping error: {str(e)}")
    
    async def crawl_educational_site(self, base_url: str, max_pages: int = 10) -> Dict[str, Any]:
        """Crawl an entire educational website"""
        if not self.app:
            raise HTTPException(status_code=503, detail="Firecrawl service not configured. Please set FIRECRAWL_API_KEY environment variable.")
        
        try:
            crawl_options = {
                'limit': max_pages,
                'scrapeOptions': {
                    'formats': ['markdown'],
                    'onlyMainContent': True
                },
                'excludePaths': ['/admin', '/login', '/cart', '/checkout', '/profile'],
                'includePaths': ['/tutorial', '/guide', '/lesson', '/course', '/documentation', '/docs']
            }
            
            print(f"🕷️ Crawling educational site: {base_url} (max {max_pages} pages)")
            result = self.app.crawl_url(base_url, **crawl_options)
            
            return {
                'success': True,
                'base_url': base_url,
                'total_pages': len(result.get('data', [])),
                'pages': self._process_crawled_pages(result.get('data', [])),
                'summary': self._generate_crawl_summary(result.get('data', [])),
                'crawled_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Firecrawl crawling error for {base_url}: {e}")
            raise HTTPException(status_code=500, detail=f"Firecrawl crawling error: {str(e)}")
    
    async def extract_structured_data(self, url: str, schema: Dict) -> Dict[str, Any]:
        """Extract structured data using AI"""
        if not self.app:
            raise HTTPException(status_code=503, detail="Firecrawl service not configured. Please set FIRECRAWL_API_KEY environment variable.")
        
        try:
            print(f"🤖 Extracting structured data from: {url}")
            
            # Simple extraction without complex schemas for now
            result = self.app.scrape_url(
                url,
                formats=["json"],
                jsonOptions={
                    'prompt': f"Extract the following information: {', '.join(schema.keys())}"
                }
            )
            
            return {
                'success': True,
                'url': url,
                'extracted_data': result.get('json', {}),
                'metadata': result.get('metadata', {}),
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Firecrawl extraction error for {url}: {e}")
            raise HTTPException(status_code=500, detail=f"Firecrawl extraction error: {str(e)}")
    
    async def search_and_collect_resources(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for educational resources and collect them"""
        print(f"🔍 Searching for educational resources: '{query}'")
        
        # Define educational domains to search
        educational_domains = [
            'docs.oracle.com',
            'geeksforgeeks.org',
            'tutorialspoint.com',
            'codecademy.com',
            'khanacademy.org'
        ]
        
        resources = []
        
        for domain in educational_domains[:3]:  # Limit to first 3 domains
            try:
                # Use site-specific search or scraping
                if domain == 'docs.oracle.com':
                    domain_resources = await self._search_oracle_docs(query)
                elif domain == 'geeksforgeeks.org':
                    domain_resources = await self._search_geeksforgeeks(query)
                elif domain == 'tutorialspoint.com':
                    domain_resources = await self._search_tutorialspoint(query)
                else:
                    continue
                
                resources.extend(domain_resources)
                
                if len(resources) >= max_results:
                    break
                    
            except Exception as e:
                print(f"❌ Error searching {domain}: {e}")
                continue
        
        return resources[:max_results]
    
    async def bulk_scrape_resources(self, urls: List[str]) -> List[Dict[str, Any]]:
        """Scrape multiple resources in parallel"""
        print(f"📚 Bulk scraping {len(urls)} resources...")
        
        tasks = []
        for url in urls:
            task = self.scrape_educational_resource(url)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"❌ Error scraping {urls[i]}: {result}")
            elif result.get('success'):
                successful_results.append(result)
        
        print(f"✅ Successfully scraped {len(successful_results)}/{len(urls)} resources")
        return successful_results
    
    # Helper methods
    def _extract_links(self, markdown: str) -> List[str]:
        """Extract links from markdown content"""
        links = re.findall(r'\[.*?\]\((https?://[^\)]+)\)', markdown)
        return list(set(links))  # Remove duplicates
    
    def _extract_images(self, markdown: str) -> List[str]:
        """Extract image URLs from markdown content"""
        images = re.findall(r'!\[.*?\]\((https?://[^\)]+)\)', markdown)
        return list(set(images))  # Remove duplicates
    
    def _process_crawled_pages(self, pages: List[Dict]) -> List[Dict]:
        """Process and clean crawled pages"""
        processed = []
        for page in pages:
            metadata = page.get('metadata', {})
            markdown_content = page.get('markdown', '')
            
            processed.append({
                'url': metadata.get('sourceURL', ''),
                'title': metadata.get('title', 'Untitled'),
                'description': metadata.get('description', ''),
                'content_length': len(markdown_content),
                'content_preview': markdown_content[:300] + '...' if len(markdown_content) > 300 else markdown_content,
                'links': self._extract_links(markdown_content),
                'images': self._extract_images(markdown_content),
                'metadata': metadata,
                'educational_indicators': self._assess_educational_content(markdown_content)
            })
        return processed
    
    def _generate_crawl_summary(self, pages: List[Dict]) -> Dict[str, Any]:
        """Generate summary of crawled content"""
        if not pages:
            return {'total_pages': 0, 'total_content_length': 0}
        
        total_content = sum(len(page.get('markdown', '')) for page in pages)
        
        # Count educational indicators
        educational_keywords = ['tutorial', 'example', 'guide', 'lesson', 'course', 'learning']
        educational_score = 0
        
        for page in pages:
            content = page.get('markdown', '').lower()
            educational_score += sum(content.count(keyword) for keyword in educational_keywords)
        
        domains = set()
        for page in pages:
            url = page.get('metadata', {}).get('sourceURL', '')
            if url:
                try:
                    domain = url.split('/')[2]
                    domains.add(domain)
                except IndexError:
                    pass
        
        return {
            'total_pages': len(pages),
            'total_content_length': total_content,
            'average_content_length': total_content // len(pages),
            'unique_domains': len(domains),
            'educational_score': educational_score,
            'domains': list(domains)
        }
    
    def _assess_educational_content(self, content: str) -> Dict[str, Any]:
        """Assess if content is educational"""
        educational_keywords = [
            'tutorial', 'example', 'guide', 'lesson', 'course', 
            'learning', 'practice', 'exercise', 'code', 'programming'
        ]
        
        content_lower = content.lower()
        keyword_count = sum(content_lower.count(keyword) for keyword in educational_keywords)
        
        # Check for code blocks
        code_blocks = len(re.findall(r'```[\s\S]*?```', content))
        
        # Check for Java-specific content
        java_keywords = ['java', 'class', 'public', 'private', 'method', 'variable']
        java_count = sum(content_lower.count(keyword) for keyword in java_keywords)
        
        return {
            'educational_keyword_count': keyword_count,
            'code_blocks': code_blocks,
            'java_specific_content': java_count,
            'is_likely_educational': keyword_count > 2 or code_blocks > 0,
            'is_java_related': java_count > 3
        }
    

    

    

    
    async def _search_oracle_docs(self, query: str) -> List[Dict[str, Any]]:
        """Search Oracle Java documentation"""
        return [
            {
                'title': f'Oracle Java Documentation: {query.title()}',
                'url': f'https://docs.oracle.com/javase/tutorial/java/nutsandbolts/{query.lower().replace(" ", "")}.html',
                'description': f'Official Oracle documentation covering {query} in Java programming',
                'source': 'Oracle Java Documentation',
                'type': 'official_documentation',
                'estimated_quality': 95
            }
        ]
    
    async def _search_geeksforgeeks(self, query: str) -> List[Dict[str, Any]]:
        """Search GeeksforGeeks"""
        return [
            {
                'title': f'{query.title()} in Java - GeeksforGeeks',
                'url': f'https://www.geeksforgeeks.org/{query.lower().replace(" ", "-")}-in-java/',
                'description': f'Comprehensive guide to {query} in Java with examples and explanations',
                'source': 'GeeksforGeeks',
                'type': 'tutorial',
                'estimated_quality': 85
            }
        ]
    
    async def _search_tutorialspoint(self, query: str) -> List[Dict[str, Any]]:
        """Search TutorialsPoint"""
        return [
            {
                'title': f'Java {query.title()} Tutorial - TutorialsPoint',
                'url': f'https://www.tutorialspoint.com/java/{query.lower().replace(" ", "_")}.htm',
                'description': f'Learn about {query} in Java with step-by-step tutorial and examples',
                'source': 'TutorialsPoint',
                'type': 'tutorial',
                'estimated_quality': 80
            }
        ]
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get Firecrawl service status"""
        return {
            'firecrawl_available': FIRECRAWL_AVAILABLE,
            'api_key_configured': bool(self.api_key),
            'service_ready': bool(self.app),
            'base_url': self.base_url,
            'service_mode': 'production' if self.app else 'demo'
        }

# Global instance
firecrawl_service = FirecrawlService()

# Quick test function
async def test_firecrawl_service():
    """Test the Firecrawl service"""
    print("🧪 Testing Firecrawl Service...")
    
    # Test service status
    status = firecrawl_service.get_service_status()
    print(f"📊 Service Status: {status}")
    
    # Test search
    resources = await firecrawl_service.search_and_collect_resources("java variables", max_results=2)
    print(f"🔍 Found {len(resources)} resources")
    
    # Test scraping
    if resources:
        scraped = await firecrawl_service.scrape_educational_resource(resources[0]['url'])
        print(f"📄 Scraped: {scraped.get('title', 'Unknown')}")
    
    print("✅ Firecrawl service test completed!")

if __name__ == "__main__":
    asyncio.run(test_firecrawl_service()) 