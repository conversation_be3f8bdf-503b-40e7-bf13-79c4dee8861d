"""
Lesson内容管理API
为每个lesson提供Khan Academy视频、CodeHS资源和quiz/assignment
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from auth import get_current_active_user
from models import UserResponse
from lesson_content_service import lesson_content_service

router = APIRouter()

# ===============================
# Lesson内容API
# ===============================

@router.get("/lessons/{lesson_code}/content")
async def get_lesson_complete_content(lesson_code: str):
    """获取特定lesson的完整内容 - Khan Academy + CodeHS + Quiz/Assignment"""
    try:
        result = await lesson_content_service.get_lesson_content(lesson_code)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取lesson内容失败: {str(e)}")

@router.get("/lessons/{lesson_code}/khan-academy")
async def get_lesson_khan_academy_videos(lesson_code: str):
    """获取特定lesson的Khan Academy视频"""
    try:
        result = await lesson_content_service.get_lesson_content(lesson_code)
        if result["success"]:
            return {
                "success": True,
                "lesson_code": lesson_code,
                "videos": result["content"]["khan_academy_videos"],
                "count": len(result["content"]["khan_academy_videos"]),
                "message": f"Khan Academy视频 for {lesson_code}"
            }
        else:
            return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Khan Academy视频失败: {str(e)}")

@router.get("/lessons/{lesson_code}/codehs")
async def get_lesson_codehs_resources(lesson_code: str):
    """获取特定lesson的CodeHS资源（notes + documents）"""
    try:
        result = await lesson_content_service.get_lesson_content(lesson_code)
        if result["success"]:
            return {
                "success": True,
                "lesson_code": lesson_code,
                "notes": result["content"]["codehs_notes"],
                "documents": result["content"]["codehs_documents"],
                "total_count": len(result["content"]["codehs_notes"]) + len(result["content"]["codehs_documents"]),
                "message": f"CodeHS资源 for {lesson_code}"
            }
        else:
            return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取CodeHS资源失败: {str(e)}")

@router.get("/lessons/{lesson_code}/assessments")
async def get_lesson_assessments(lesson_code: str):
    """获取特定lesson的assessments（quizzes + assignments）"""
    try:
        result = await lesson_content_service.get_lesson_content(lesson_code)
        if result["success"]:
            return {
                "success": True,
                "lesson_code": lesson_code,
                "quizzes": result["content"]["quizzes"],
                "assignments": result["content"]["assignments"],
                "total_count": len(result["content"]["quizzes"]) + len(result["content"]["assignments"]),
                "message": f"Quiz和Assignment for {lesson_code}"
            }
        else:
            return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取assessments失败: {str(e)}")

@router.get("/lessons/overview")
async def get_all_lessons_overview():
    """获取所有lesson的内容概览"""
    try:
        result = await lesson_content_service.get_all_lesson_content_overview()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览失败: {str(e)}")

# ===============================
# Unit级别的lesson内容API
# ===============================

@router.get("/units/{unit_code}/lessons/content")
async def get_unit_lessons_content(unit_code: str):
    """获取特定unit下所有lessons的内容"""
    try:
        overview_result = await lesson_content_service.get_all_lesson_content_overview()
        
        if not overview_result["success"]:
            return overview_result
        
        # 过滤出该unit的lessons
        unit_lessons = {}
        for lesson_code, content in overview_result["overview"].items():
            if lesson_code.startswith(f"{unit_code}-lesson-"):
                unit_lessons[lesson_code] = content
        
        return {
            "success": True,
            "unit_code": unit_code,
            "lessons": unit_lessons,
            "total_lessons": len(unit_lessons),
            "message": f"Unit {unit_code} 的所有lesson内容概览"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取unit lessons内容失败: {str(e)}")

@router.get("/units/{unit_code}/lessons/{lesson_number}/content")
async def get_unit_lesson_content(unit_code: str, lesson_number: int):
    """获取特定unit下特定lesson的完整内容"""
    try:
        lesson_code = f"{unit_code}-lesson-{lesson_number}"
        result = await lesson_content_service.get_lesson_content(lesson_code)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取unit lesson内容失败: {str(e)}")

# ===============================
# 资源类型过滤API
# ===============================

@router.get("/lessons/videos")
async def get_all_khan_academy_videos(unit_code: Optional[str] = None):
    """获取所有Khan Academy视频，可按unit过滤"""
    try:
        overview_result = await lesson_content_service.get_all_lesson_content_overview()
        
        if not overview_result["success"]:
            return overview_result
        
        all_videos = []
        
        for lesson_code in overview_result["overview"].keys():
            if unit_code and not lesson_code.startswith(f"{unit_code}-lesson-"):
                continue
                
            lesson_result = await lesson_content_service.get_lesson_content(lesson_code)
            if lesson_result["success"]:
                all_videos.extend(lesson_result["content"]["khan_academy_videos"])
        
        return {
            "success": True,
            "videos": all_videos,
            "count": len(all_videos),
            "filter": {"unit_code": unit_code},
            "message": f"所有Khan Academy视频{f' (Unit: {unit_code})' if unit_code else ''}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频失败: {str(e)}")

@router.get("/lessons/codehs-resources")
async def get_all_codehs_resources(unit_code: Optional[str] = None):
    """获取所有CodeHS资源，可按unit过滤"""
    try:
        overview_result = await lesson_content_service.get_all_lesson_content_overview()
        
        if not overview_result["success"]:
            return overview_result
        
        all_notes = []
        all_documents = []
        
        for lesson_code in overview_result["overview"].keys():
            if unit_code and not lesson_code.startswith(f"{unit_code}-lesson-"):
                continue
                
            lesson_result = await lesson_content_service.get_lesson_content(lesson_code)
            if lesson_result["success"]:
                all_notes.extend(lesson_result["content"]["codehs_notes"])
                all_documents.extend(lesson_result["content"]["codehs_documents"])
        
        return {
            "success": True,
            "notes": all_notes,
            "documents": all_documents,
            "notes_count": len(all_notes),
            "documents_count": len(all_documents),
            "total_count": len(all_notes) + len(all_documents),
            "filter": {"unit_code": unit_code},
            "message": f"所有CodeHS资源{f' (Unit: {unit_code})' if unit_code else ''}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取CodeHS资源失败: {str(e)}")

@router.get("/lessons/assessments")
async def get_all_assessments(unit_code: Optional[str] = None):
    """获取所有assessments（quizzes + assignments），可按unit过滤"""
    try:
        overview_result = await lesson_content_service.get_all_lesson_content_overview()
        
        if not overview_result["success"]:
            return overview_result
        
        all_quizzes = []
        all_assignments = []
        
        for lesson_code in overview_result["overview"].keys():
            if unit_code and not lesson_code.startswith(f"{unit_code}-lesson-"):
                continue
                
            lesson_result = await lesson_content_service.get_lesson_content(lesson_code)
            if lesson_result["success"]:
                all_quizzes.extend(lesson_result["content"]["quizzes"])
                all_assignments.extend(lesson_result["content"]["assignments"])
        
        return {
            "success": True,
            "quizzes": all_quizzes,
            "assignments": all_assignments,
            "quizzes_count": len(all_quizzes),
            "assignments_count": len(all_assignments),
            "total_count": len(all_quizzes) + len(all_assignments),
            "filter": {"unit_code": unit_code},
            "message": f"所有Quiz和Assignment{f' (Unit: {unit_code})' if unit_code else ''}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取assessments失败: {str(e)}")

# ===============================
# 统计和分析API
# ===============================

@router.get("/lessons/statistics")
async def get_lessons_statistics():
    """获取lesson内容统计信息"""
    try:
        overview_result = await lesson_content_service.get_all_lesson_content_overview()
        
        if not overview_result["success"]:
            return overview_result
        
        total_khan_videos = 0
        total_codehs_notes = 0
        total_codehs_docs = 0
        total_quizzes = 0
        total_assignments = 0
        
        units_stats = {}
        
        for lesson_code, content in overview_result["overview"].items():
            unit_code = lesson_code.split("-lesson-")[0]
            
            # 累计总数
            total_khan_videos += content["khan_academy_videos"]
            total_codehs_notes += content["codehs_notes"]
            total_codehs_docs += content["codehs_documents"]
            total_quizzes += content["quizzes"]
            total_assignments += content["assignments"]
            
            # 按unit统计
            if unit_code not in units_stats:
                units_stats[unit_code] = {
                    "lessons_count": 0,
                    "khan_academy_videos": 0,
                    "codehs_notes": 0,
                    "codehs_documents": 0,
                    "quizzes": 0,
                    "assignments": 0,
                    "total_resources": 0
                }
            
            units_stats[unit_code]["lessons_count"] += 1
            units_stats[unit_code]["khan_academy_videos"] += content["khan_academy_videos"]
            units_stats[unit_code]["codehs_notes"] += content["codehs_notes"]
            units_stats[unit_code]["codehs_documents"] += content["codehs_documents"]
            units_stats[unit_code]["quizzes"] += content["quizzes"]
            units_stats[unit_code]["assignments"] += content["assignments"]
            units_stats[unit_code]["total_resources"] += content["total_resources"]
        
        return {
            "success": True,
            "global_statistics": {
                "total_lessons": len(overview_result["overview"]),
                "total_khan_academy_videos": total_khan_videos,
                "total_codehs_notes": total_codehs_notes,
                "total_codehs_documents": total_codehs_docs,
                "total_quizzes": total_quizzes,
                "total_assignments": total_assignments,
                "total_resources": total_khan_videos + total_codehs_notes + total_codehs_docs + total_quizzes + total_assignments
            },
            "by_unit": units_stats,
            "message": "Lesson内容系统统计信息"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

# ===============================
# 导入到统一资源系统API
# ===============================

@router.post("/lessons/{lesson_code}/import-to-unified")
async def import_lesson_to_unified_system(
    lesson_code: str,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """将特定lesson的内容导入到统一资源管理系统"""
    try:
        # 获取lesson内容
        lesson_result = await lesson_content_service.get_lesson_content(lesson_code)
        
        if not lesson_result["success"]:
            return lesson_result
        
        # 导入到统一资源系统
        from unified_resource_manager import unified_resource_manager
        
        imported_count = 0
        results = []
        
        for resource in lesson_result["content"]["all_resources"]:
            # 转换为统一资源格式
            resource_data = {
                "title": resource["title"],
                "description": resource["description"],
                "category_code": resource["category_code"],
                "unit_code": resource["unit_code"],
                "lesson_code": resource["lesson_code"],
                "resource_type": resource["resource_type"],
                "format": resource["format"],
                "external_url": resource.get("external_url"),
                "thumbnail_url": resource.get("thumbnail_url"),
                "tags": resource["tags"],
                "keywords": resource["keywords"],
                "difficulty_level": resource["difficulty_level"],
                "estimated_time_minutes": resource["estimated_time_minutes"],
                "source_type": resource["source_type"],
                "source_platform": resource["source_platform"],
                "author_name": resource.get("author_name"),
                "quality_score": resource["quality_score"],
                "is_verified": resource["is_verified"],
                "is_featured": resource.get("is_featured", False),
                "metadata": resource["metadata"]
            }
            
            result = await unified_resource_manager.add_resource(resource_data, current_user.id)
            if result["success"]:
                imported_count += 1
            results.append(result)
        
        return {
            "success": True,
            "lesson_code": lesson_code,
            "imported_count": imported_count,
            "total_processed": len(results),
            "summary": lesson_result["summary"],
            "message": f"成功将 {lesson_code} 的 {imported_count} 个资源导入统一系统"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")

 