"""
Lesson内容服务 - 为每个lesson提供Khan Academy视频、CodeHS资源和quiz/assignment
专门针对APCSA课程的lesson级别内容管理
"""

import asyncio
import aiohttp
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

class LessonContentService:
    def __init__(self):
        self.youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        self.session = None
        # Khan Academy内容映射
        self.khan_academy_content = self._initialize_khan_academy_content()
        # CodeHS内容映射
        self.codehs_content = self._initialize_codehs_content()
        # Quiz和Assignment内容
        self.assessment_content = self._initialize_assessment_content()
    
    async def __aenter__(self):
        if not self.session or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    def _initialize_khan_academy_content(self) -> Dict[str, Dict]:
        """初始化Khan Academy视频内容映射"""
        return {
            # Unit 1: Primitive Types
            "unit-1-lesson-1": {
                "videos": [
                    {
                        "id": "khan_intro_programming",
                        "title": "What is Programming? | Khan Academy",
                        "description": "Learn the basics of computer programming and why Java is a great first language.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming/intro-to-programming/v/programming-intro",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_intro_programming.jpg",
                        "duration_minutes": 8,
                        "channel": "Khan Academy",
                        "topic": "Introduction to Programming",
                        "difficulty": "beginner"
                    },
                    {
                        "id": "khan_java_intro",
                        "title": "Intro to Java Programming | Khan Academy",
                        "description": "Introduction to Java programming language and its applications.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/intro-to-java/v/java-intro",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_java_intro.jpg",
                        "duration_minutes": 10,
                        "channel": "Khan Academy",
                        "topic": "Java Introduction",
                        "difficulty": "beginner"
                    }
                ]
            },
            "unit-1-lesson-2": {
                "videos": [
                    {
                        "id": "khan_variables_datatypes",
                        "title": "Variables and Data Types in Java | Khan Academy",
                        "description": "Learn about primitive data types: int, double, boolean, char and how to declare variables.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/variables-data-types/v/java-variables",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_variables.jpg",
                        "duration_minutes": 12,
                        "channel": "Khan Academy",
                        "topic": "Variables and Data Types",
                        "difficulty": "beginner"
                    },
                    {
                        "id": "khan_primitive_types",
                        "title": "Java Primitive Types Explained | Khan Academy",
                        "description": "Deep dive into Java primitive types with examples and best practices.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/primitive-types/v/primitive-types",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_primitives.jpg",
                        "duration_minutes": 15,
                        "channel": "Khan Academy",
                        "topic": "Primitive Types",
                        "difficulty": "beginner"
                    }
                ]
            },
            "unit-1-lesson-3": {
                "videos": [
                    {
                        "id": "khan_expressions",
                        "title": "Java Expressions and Assignment | Khan Academy",
                        "description": "Learn how to write expressions and assignment statements in Java.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/expressions/v/java-expressions",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_expressions.jpg",
                        "duration_minutes": 11,
                        "channel": "Khan Academy",
                        "topic": "Expressions and Assignment",
                        "difficulty": "beginner"
                    }
                ]
            },
            
            # Unit 2: Using Objects
            "unit-2-lesson-1": {
                "videos": [
                    {
                        "id": "khan_objects_classes",
                        "title": "Objects and Classes in Java | Khan Academy",
                        "description": "Understanding the relationship between objects and classes in Java programming.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/objects-classes/v/java-objects",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_objects.jpg",
                        "duration_minutes": 14,
                        "channel": "Khan Academy",
                        "topic": "Objects and Classes",
                        "difficulty": "beginner"
                    }
                ]
            },
            "unit-2-lesson-2": {
                "videos": [
                    {
                        "id": "khan_creating_objects",
                        "title": "Creating and Storing Objects | Khan Academy",
                        "description": "Learn how to create objects and store them in variables.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/creating-objects/v/creating-objects",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_creating_objects.jpg",
                        "duration_minutes": 13,
                        "channel": "Khan Academy",
                        "topic": "Creating Objects",
                        "difficulty": "beginner"
                    }
                ]
            },
            "unit-2-lesson-3": {
                "videos": [
                    {
                        "id": "khan_calling_methods",
                        "title": "Calling Methods in Java | Khan Academy",
                        "description": "Learn how to call methods on objects and understand return values.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/calling-methods/v/calling-methods",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_methods.jpg",
                        "duration_minutes": 16,
                        "channel": "Khan Academy",
                        "topic": "Calling Methods",
                        "difficulty": "intermediate"
                    }
                ]
            },
            
            # Unit 3: Boolean Expressions and if Statements
            "unit-3-lesson-1": {
                "videos": [
                    {
                        "id": "khan_boolean_expressions",
                        "title": "Boolean Expressions in Java | Khan Academy",
                        "description": "Learn about boolean values and how to create boolean expressions.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/boolean/v/boolean-expressions",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_boolean.jpg",
                        "duration_minutes": 12,
                        "channel": "Khan Academy",
                        "topic": "Boolean Expressions",
                        "difficulty": "beginner"
                    }
                ]
            },
            "unit-3-lesson-2": {
                "videos": [
                    {
                        "id": "khan_if_statements",
                        "title": "If Statements in Java | Khan Academy",
                        "description": "Learn how to use if statements for conditional logic in Java.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/if-statements/v/if-statements",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_if.jpg",
                        "duration_minutes": 14,
                        "channel": "Khan Academy",
                        "topic": "If Statements",
                        "difficulty": "beginner"
                    }
                ]
            },
            
            # Unit 4: Iteration
            "unit-4-lesson-1": {
                "videos": [
                    {
                        "id": "khan_while_loops",
                        "title": "While Loops in Java | Khan Academy",
                        "description": "Learn how to use while loops for repetitive tasks in Java.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/while-loops/v/while-loops",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_while.jpg",
                        "duration_minutes": 15,
                        "channel": "Khan Academy",
                        "topic": "While Loops",
                        "difficulty": "intermediate"
                    }
                ]
            },
            "unit-4-lesson-2": {
                "videos": [
                    {
                        "id": "khan_for_loops",
                        "title": "For Loops in Java | Khan Academy",
                        "description": "Master for loops and understand when to use them in Java programming.",
                        "url": "https://www.khanacademy.org/computing/computer-programming/programming-java/for-loops/v/for-loops",
                        "thumbnail": "https://cdn.kastatic.org/images/khan_for.jpg",
                        "duration_minutes": 16,
                        "channel": "Khan Academy",
                        "topic": "For Loops",
                        "difficulty": "intermediate"
                    }
                ]
            }
        }

    def _initialize_codehs_content(self) -> Dict[str, Dict]:
        """
        基于 CodeHS APCSA 教科书的实际内容结构初始化 CodeHS 资源
        从 https://codehs.com/textbook/apcsa_textbook/ 获取的结构
        """
        return {
            "unit-1-lesson-1": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_1_1_1",
                        "title": "1.1 Introduction to Java Programming",
                        "description": "Introduction to Java programming language and environment setup",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/1",
                        "estimated_time_minutes": 15,
                        "topics": ["Java basics", "Programming introduction"],
                        "difficulty_level": "beginner"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_1_1_1",
                        "title": "1.2 Why Programming? Why Java?",
                        "description": "Understanding the importance of programming and why Java is chosen for AP Computer Science",
                        "content_type": "interactive_lesson",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/2",
                        "estimated_time_minutes": 20,
                        "sections": [
                            "Environment Set Up",
                            "Writing Hello World", 
                            "Hello World",
                            "Println vs Print",
                            "Printing Multiple Lines",
                            "Check Your Understanding",
                            "Exercise: ASCII Art"
                        ],
                        "difficulty_level": "beginner"
                    }
                ]
            },
            
            "unit-1-lesson-2": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_1_2_1", 
                        "title": "1.3 Variables and Data Types",
                        "description": "Comprehensive guide to Java variables and primitive data types",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/3",
                        "estimated_time_minutes": 25,
                        "sections": [
                            "Variables and Types",
                            "Primitive Types", 
                            "Numeric Type",
                            "Char Type",
                            "Boolean Type",
                            "Reference Types",
                            "String Type",
                            "Final Keyword",
                            "Naming Variables",
                            "Variables Using Final",
                            "Swapping Two Values"
                        ],
                        "difficulty_level": "beginner"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_1_2_1",
                        "title": "Check Your Understanding: Variables",
                        "description": "Interactive exercises to test understanding of variables and data types",
                        "content_type": "interactive_exercise",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/3",
                        "estimated_time_minutes": 15,
                        "exercise_type": "multiple_choice_and_coding",
                        "difficulty_level": "beginner"
                    },
                    {
                        "id": "codehs_doc_1_2_2",
                        "title": "Exercise: Answering Questions",
                        "description": "Hands-on coding exercise practicing variable declaration and usage", 
                        "content_type": "coding_exercise",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/3",
                        "estimated_time_minutes": 20,
                        "exercise_type": "coding",
                        "difficulty_level": "beginner"
                    }
                ]
            },
            
            "unit-1-lesson-3": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_1_3_1",
                        "title": "1.4 Expressions and Assignment Statements", 
                        "description": "Understanding arithmetic expressions and assignment in Java",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/4",
                        "estimated_time_minutes": 30,
                        "sections": [
                            "Arithmetic Expressions",
                            "Addition Operator",
                            "Subtraction Operator", 
                            "Multiplication Operator",
                            "Division Operator",
                            "Types of Division",
                            "Arithmetic Exceptions",
                            "Modulus Operator",
                            "Operation Precedence",
                            "Calculator",
                            "Temperature Conversion",
                            "Tricky Java"
                        ],
                        "difficulty_level": "beginner"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_1_3_1",
                        "title": "Exercise: Fractions",
                        "description": "Practice with arithmetic operations and fractions in Java",
                        "content_type": "coding_exercise",
                        "source": "CodeHS APCSA Textbook", 
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/4",
                        "estimated_time_minutes": 25,
                        "exercise_type": "coding",
                        "difficulty_level": "intermediate"
                    }
                ]
            },
            
            "unit-1-lesson-4": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_1_4_1",
                        "title": "1.5 Compound Assignment Operators",
                        "description": "Learning about compound assignment operators and arithmetic shortcuts",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/5", 
                        "estimated_time_minutes": 20,
                        "sections": [
                            "Evaluating What is Stored in a Variable",
                            "Arithmetic Shortcuts",
                            "All Functions Calculator",
                            "Increase/Decrease by 1"
                        ],
                        "difficulty_level": "beginner"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_1_4_1",
                        "title": "Exercise: Work Shift",
                        "description": "Apply compound assignment operators in a practical scenario",
                        "content_type": "coding_exercise",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/5",
                        "estimated_time_minutes": 15,
                        "exercise_type": "coding",
                        "difficulty_level": "beginner"
                    }
                ]
            },
            
            "unit-1-lesson-5": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_1_5_1",
                        "title": "1.6 Casting and Range of Variables",
                        "description": "Understanding type casting and variable ranges in Java",
                        "content_type": "text", 
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/6",
                        "estimated_time_minutes": 25,
                        "sections": [
                            "How Casting Works",
                            "Casting an Integer to a Double",
                            "Casting a Double to an Integer", 
                            "Rounding Using Casting",
                            "Division with Casting",
                            "Numerical Ranges",
                            "Casting",
                            "Casting Order of Operations",
                            "Implicit Casting",
                            "Min and Max Values of Integers"
                        ],
                        "difficulty_level": "intermediate"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_1_5_1",
                        "title": "Exercise: Movie Ratings",
                        "description": "Practice casting and numerical ranges with a movie rating system",
                        "content_type": "coding_exercise",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/1/6",
                        "estimated_time_minutes": 20,
                        "exercise_type": "coding",
                        "difficulty_level": "intermediate"
                    }
                ]
            },
            
            # Unit 2 - Using Objects (从搜索结果获得的结构)
            "unit-2-lesson-1": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_2_1_1",
                        "title": "2.1 Objects: Instances of Classes",
                        "description": "Introduction to objects and classes in Java",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/1",
                        "estimated_time_minutes": 20,
                        "difficulty_level": "beginner"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_2_1_1", 
                        "title": "2.2 Creating and Storing Objects (Instantiation)",
                        "description": "Learn how to create and store objects in Java",
                        "content_type": "interactive_lesson",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/2",
                        "estimated_time_minutes": 25,
                        "difficulty_level": "beginner"
                    }
                ]
            },
            
            "unit-2-lesson-2": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_2_2_1",
                        "title": "2.3 Calling a Void Method",
                        "description": "Understanding void methods and how to call them",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook", 
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/3",
                        "estimated_time_minutes": 15,
                        "difficulty_level": "beginner"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_2_2_1",
                        "title": "2.4 Calling a Void Method with Parameters",
                        "description": "Learn to call void methods with parameters",
                        "content_type": "interactive_lesson",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/4",
                        "estimated_time_minutes": 20,
                        "difficulty_level": "beginner"
                    }
                ]
            },
            
            "unit-2-lesson-3": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_2_3_1",
                        "title": "2.5 Calling a Non-void Method",
                        "description": "Understanding methods that return values",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/5",
                        "estimated_time_minutes": 18,
                        "difficulty_level": "beginner"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_2_3_1",
                        "title": "2.6 String Objects: Concatenation, Literals & More",
                        "description": "Comprehensive guide to String objects in Java",
                        "content_type": "interactive_lesson", 
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/6",
                        "estimated_time_minutes": 25,
                        "difficulty_level": "beginner"
                    }
                ]
            },
            
            "unit-2-lesson-4": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_2_4_1",
                        "title": "2.7 String Methods",
                        "description": "Essential String methods for AP Computer Science A",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/7",
                        "estimated_time_minutes": 30,
                        "difficulty_level": "intermediate"
                    }
                ],
                "codehs_documents": [
                    {
                        "id": "codehs_doc_2_4_1",
                        "title": "2.8 Wrapper Classes: Integers and Doubles", 
                        "description": "Understanding wrapper classes for primitive types",
                        "content_type": "interactive_lesson",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/8",
                        "estimated_time_minutes": 20,
                        "difficulty_level": "intermediate"
                    }
                ]
            },
            
            "unit-2-lesson-5": {
                "codehs_notes": [
                    {
                        "id": "codehs_note_2_5_1",
                        "title": "2.9 Using the Math Class",
                        "description": "Comprehensive guide to the Math class and its methods",
                        "content_type": "text",
                        "source": "CodeHS APCSA Textbook",
                        "url": "https://codehs.com/textbook/apcsa_textbook/2/9",
                        "estimated_time_minutes": 25,
                        "difficulty_level": "intermediate"
                    }
                ],
                "codehs_documents": []
            }
        }

    def _initialize_assessment_content(self) -> Dict[str, Dict]:
        """初始化quiz和assignment内容"""
        return {
            "unit-1-lesson-1": {
                "quizzes": [
                    {
                        "id": "quiz_intro_programming",
                        "title": "Introduction to Programming - Quiz",
                        "description": "Test your understanding of basic programming concepts and Java introduction.",
                        "questions_count": 10,
                        "estimated_time_minutes": 15,
                        "difficulty": "beginner",
                        "type": "quiz",
                        "source": "custom",
                        "topics": ["programming basics", "Java overview"]
                    }
                ],
                "assignments": [
                    {
                        "id": "assignment_hello_world",
                        "title": "Hello World Assignment",
                        "description": "Create your first Java program that prints 'Hello, World!' to the console.",
                        "estimated_time_minutes": 30,
                        "difficulty": "beginner",
                        "type": "assignment",
                        "source": "custom",
                        "requirements": ["Create a Java class", "Use System.out.println", "Compile and run"]
                    }
                ]
            },
            "unit-1-lesson-2": {
                "quizzes": [
                    {
                        "id": "quiz_variables_datatypes",
                        "title": "Variables and Data Types - Quiz",
                        "description": "Quiz on Java primitive types and variable declaration.",
                        "questions_count": 12,
                        "estimated_time_minutes": 18,
                        "difficulty": "beginner",
                        "type": "quiz",
                        "source": "custom",
                        "topics": ["primitive types", "variables", "data types"]
                    }
                ],
                "assignments": [
                    {
                        "id": "assignment_variable_practice",
                        "title": "Variable Declaration Practice",
                        "description": "Practice declaring and initializing variables of different primitive types.",
                        "estimated_time_minutes": 45,
                        "difficulty": "beginner",
                        "type": "assignment",
                        "source": "custom",
                        "requirements": ["Declare int, double, boolean, char variables", "Initialize with appropriate values", "Print variable values"]
                    }
                ]
            },
            "unit-1-lesson-3": {
                "quizzes": [
                    {
                        "id": "quiz_expressions",
                        "title": "Expressions and Assignment - Quiz",
                        "description": "Test your knowledge of Java expressions and assignment statements.",
                        "questions_count": 8,
                        "estimated_time_minutes": 12,
                        "difficulty": "beginner",
                        "type": "quiz",
                        "source": "custom",
                        "topics": ["expressions", "operators", "assignment"]
                    }
                ],
                "assignments": [
                    {
                        "id": "assignment_calculator",
                        "title": "Simple Calculator Assignment",
                        "description": "Create a simple calculator that performs basic arithmetic operations.",
                        "estimated_time_minutes": 60,
                        "difficulty": "intermediate",
                        "type": "assignment",
                        "source": "custom",
                        "requirements": ["Use arithmetic operators", "Calculate and display results", "Handle different data types"]
                    }
                ]
            },
            
            # Unit 2 assessments
            "unit-2-lesson-1": {
                "quizzes": [
                    {
                        "id": "quiz_objects_classes",
                        "title": "Objects and Classes - Quiz",
                        "description": "Quiz on object-oriented programming concepts in Java.",
                        "questions_count": 10,
                        "estimated_time_minutes": 15,
                        "difficulty": "intermediate",
                        "type": "quiz",
                        "source": "custom",
                        "topics": ["objects", "classes", "OOP concepts"]
                    }
                ],
                "assignments": [
                    {
                        "id": "assignment_student_class",
                        "title": "Student Class Design",
                        "description": "Design a Student class with appropriate attributes and methods.",
                        "estimated_time_minutes": 90,
                        "difficulty": "intermediate",
                        "type": "assignment",
                        "source": "custom",
                        "requirements": ["Create Student class", "Add attributes and methods", "Create and test objects"]
                    }
                ]
            }
        }

    async def get_lesson_content(self, lesson_code: str) -> Dict[str, Any]:
        """获取特定lesson的完整内容 - Khan Academy视频 + CodeHS资源 + Quiz/Assignment"""
        
        # 获取Khan Academy视频
        khan_videos = self.khan_academy_content.get(lesson_code, {}).get("videos", [])
        
        # 获取CodeHS notes和documents
        codehs_content = self.codehs_content.get(lesson_code, {})
        codehs_notes = codehs_content.get("codehs_notes", [])
        codehs_documents = codehs_content.get("codehs_documents", [])
        
        # 获取quizzes和assignments
        assessment_content = self.assessment_content.get(lesson_code, {})
        quizzes = assessment_content.get("quizzes", [])
        assignments = assessment_content.get("assignments", [])
        
        # 转换为统一资源格式
        unified_resources = []
        
        # 添加Khan Academy视频
        for video in khan_videos:
            unified_resource = {
                "id": str(uuid.uuid4()),
                "title": video["title"],
                "description": video["description"],
                "category_code": lesson_code,
                "unit_code": lesson_code.split("-lesson-")[0],
                "lesson_code": lesson_code,
                "resource_type": "video",
                "format": "youtube",
                "external_url": video["url"],
                "thumbnail_url": video["thumbnail"],
                "estimated_time_minutes": video["duration_minutes"],
                "difficulty_level": video["difficulty"],
                "source_type": "external",
                "source_platform": "Khan Academy",
                "author_name": video["channel"],
                "tags": ["khan academy", "video", "tutorial", video["topic"].lower()],
                "keywords": [lesson_code, video["topic"], "khan academy"],
                "quality_score": 95.0,
                "is_verified": True,
                "is_featured": True,
                "metadata": {
                    "duration_minutes": video["duration_minutes"],
                    "topic": video["topic"],
                    "original_source": "Khan Academy"
                }
            }
            unified_resources.append(unified_resource)
        
        # 添加CodeHS notes
        for note in codehs_notes:
            unified_resource = {
                "id": str(uuid.uuid4()),
                "title": note["title"],
                "description": note["description"],
                "category_code": lesson_code,
                "unit_code": lesson_code.split("-lesson-")[0],
                "lesson_code": lesson_code,
                "resource_type": "article",
                "format": "text",
                "external_url": note["url"],
                "estimated_time_minutes": note["estimated_time_minutes"],
                "difficulty_level": note["difficulty_level"],
                "source_type": "external",
                "source_platform": "CodeHS",
                "tags": ["codehs", "notes", "textbook"] + note.get("topics", []),
                "keywords": [lesson_code] + note.get("topics", []),
                "quality_score": 88.0,
                "is_verified": True,
                "metadata": {
                    "content_type": "notes",
                    "topics": note.get("topics", []),
                    "original_source": "CodeHS"
                }
            }
            unified_resources.append(unified_resource)
        
        # 添加CodeHS documents
        for doc in codehs_documents:
            unified_resource = {
                "id": str(uuid.uuid4()),
                "title": doc["title"],
                "description": doc["description"],
                "category_code": lesson_code,
                "unit_code": lesson_code.split("-lesson-")[0],
                "lesson_code": lesson_code,
                "resource_type": "document",
                "format": "interactive_lesson",
                "external_url": doc["url"],
                "estimated_time_minutes": doc["estimated_time_minutes"],
                "difficulty_level": doc["difficulty_level"],
                "source_type": "external",
                "source_platform": "CodeHS",
                "tags": ["codehs", "document", "reference"],
                "keywords": [lesson_code, "reference", "guide"],
                "quality_score": 85.0,
                "is_verified": True,
                "metadata": {
                    "content_type": doc["content_type"],
                    "original_source": "CodeHS"
                }
            }
            unified_resources.append(unified_resource)
        
        # 添加quizzes
        for quiz in quizzes:
            unified_resource = {
                "id": str(uuid.uuid4()),
                "title": quiz["title"],
                "description": quiz["description"],
                "category_code": lesson_code,
                "unit_code": lesson_code.split("-lesson-")[0],
                "lesson_code": lesson_code,
                "resource_type": "quiz",
                "format": "interactive",
                "estimated_time_minutes": quiz["estimated_time_minutes"],
                "difficulty_level": quiz["difficulty"],
                "source_type": "internal",
                "source_platform": "custom",
                "tags": ["quiz", "assessment", "practice"] + quiz.get("topics", []),
                "keywords": [lesson_code] + quiz.get("topics", []),
                "quality_score": 90.0,
                "is_verified": True,
                "metadata": {
                    "questions_count": quiz["questions_count"],
                    "content_type": "quiz",
                    "topics": quiz.get("topics", [])
                }
            }
            unified_resources.append(unified_resource)
        
        # 添加assignments
        for assignment in assignments:
            unified_resource = {
                "id": str(uuid.uuid4()),
                "title": assignment["title"],
                "description": assignment["description"],
                "category_code": lesson_code,
                "unit_code": lesson_code.split("-lesson-")[0],
                "lesson_code": lesson_code,
                "resource_type": "exercise",
                "format": "interactive",
                "estimated_time_minutes": assignment["estimated_time_minutes"],
                "difficulty_level": assignment["difficulty"],
                "source_type": "internal",
                "source_platform": "custom",
                "tags": ["assignment", "exercise", "practice"],
                "keywords": [lesson_code, "assignment", "practice"],
                "quality_score": 92.0,
                "is_verified": True,
                "metadata": {
                    "content_type": "assignment",
                    "requirements": assignment.get("requirements", [])
                }
            }
            unified_resources.append(unified_resource)
        
        # 按类型分组
        categorized_content = {
            "khan_academy_videos": [r for r in unified_resources if r["source_platform"] == "Khan Academy"],
            "codehs_notes": [r for r in unified_resources if r["source_platform"] == "CodeHS" and r["resource_type"] == "article"],
            "codehs_documents": [r for r in unified_resources if r["source_platform"] == "CodeHS" and r["resource_type"] == "document"],
            "quizzes": [r for r in unified_resources if r["resource_type"] == "quiz"],
            "assignments": [r for r in unified_resources if r["resource_type"] == "exercise"],
            "all_resources": unified_resources
        }
        
        return {
            "success": True,
            "lesson_code": lesson_code,
            "content": categorized_content,
            "summary": {
                "khan_academy_videos_count": len(categorized_content["khan_academy_videos"]),
                "codehs_notes_count": len(categorized_content["codehs_notes"]),
                "codehs_documents_count": len(categorized_content["codehs_documents"]),
                "quizzes_count": len(categorized_content["quizzes"]),
                "assignments_count": len(categorized_content["assignments"]),
                "total_resources": len(unified_resources)
            },
            "message": f"为 {lesson_code} 提供完整的学习资源：Khan Academy视频、CodeHS资料、Quiz和Assignment"
        }

    async def get_all_lesson_content_overview(self) -> Dict[str, Any]:
        """获取所有lesson的内容概览"""
        overview = {}
        
        # 获取所有已定义的lesson
        all_lessons = set(
            list(self.khan_academy_content.keys()) + 
            list(self.codehs_content.keys()) + 
            list(self.assessment_content.keys())
        )
        
        for lesson_code in sorted(all_lessons):
            khan_videos = len(self.khan_academy_content.get(lesson_code, {}).get("videos", []))
            codehs_notes = len(self.codehs_content.get(lesson_code, {}).get("codehs_notes", []))
            codehs_docs = len(self.codehs_content.get(lesson_code, {}).get("codehs_documents", []))
            quizzes = len(self.assessment_content.get(lesson_code, {}).get("quizzes", []))
            assignments = len(self.assessment_content.get(lesson_code, {}).get("assignments", []))
            
            overview[lesson_code] = {
                "khan_academy_videos": khan_videos,
                "codehs_notes": codehs_notes,
                "codehs_documents": codehs_docs,
                "quizzes": quizzes,
                "assignments": assignments,
                "total_resources": khan_videos + codehs_notes + codehs_docs + quizzes + assignments
            }
        
        return {
            "success": True,
            "overview": overview,
            "total_lessons": len(overview),
            "message": "所有lesson的内容概览 - Khan Academy视频、CodeHS资料、Quiz和Assignment"
        }

# 全局实例
lesson_content_service = LessonContentService() 