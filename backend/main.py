from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from api import router as api_router

# 加载环境变量
load_dotenv()

app = FastAPI(
    title="APCSA AI Learning Platform API",
    description="AI-powered learning platform for AP Computer Science A",
    version="2.0.0"
)

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # 前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含主要 API 路由
app.include_router(api_router, prefix="/api", tags=["APCSA Core"])

# 引入新的统一内容管理API
try:
    from content_api import router as content_api_router
    app.include_router(content_api_router, prefix="/api/content", tags=["Learning Content Management"])
    print("✅ 统一内容管理API已集成")
except ImportError as e:
    print(f"⚠️ 统一内容管理API集成失败: {e}")
except Exception as e:
    print(f"⚠️ 统一内容管理API初始化失败: {e}")

# 引入统一资源管理API
try:
    from unified_resource_api import router as unified_resource_router
    app.include_router(unified_resource_router, prefix="/api/unified", tags=["Unified Resources"])
    print("✅ 统一资源管理API已集成")
except ImportError as e:
    print(f"⚠️ 统一资源管理API集成失败: {e}")
except Exception as e:
    print(f"⚠️ 统一资源管理API初始化失败: {e}")

# 引入Lesson内容管理API (Khan Academy + CodeHS + Quiz/Assignment)
try:
    from lesson_content_api import router as lesson_content_router
    app.include_router(lesson_content_router, prefix="/api/lessons", tags=["Lesson Content - Khan Academy & CodeHS"])
    print("✅ Lesson内容管理API已集成 (Khan Academy + CodeHS + Quiz/Assignment)")
except ImportError as e:
    print(f"⚠️ Lesson内容管理API集成失败: {e}")
except Exception as e:
    print(f"⚠️ Lesson内容管理API初始化失败: {e}")

@app.get("/")
async def root():
    return {"message": "APCSA AI Learning Platform API v2.0", "status": "running", "features": ["AI", "Content Management", "Progress Tracking"]}

@app.get("/direct-test")
async def direct_test():
    return {"message": "Direct endpoint working", "no_auth": True}

@app.get("/api/units-direct")
async def get_units_direct():
    """直接的单元列表端点,无需认证"""
    return [
        {
            "id": "00000000-0000-0000-0000-000000000001",
            "title": "Primitive Types",
            "description": "Explore Java's fundamental data types including int, double, boolean, and char. Learn variable declaration, initialization, and basic operations.",
            "order_index": 1,
            "estimated_hours": 8,
            "prerequisites": None,
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000002", 
            "title": "Using Objects",
            "description": "Learn to create and manipulate objects, call methods, and understand the relationship between classes and objects.",
            "order_index": 2,
            "estimated_hours": 10,
            "prerequisites": ["Primitive Types"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000003",
            "title": "Boolean Expressions and if Statements", 
            "description": "Master conditional logic with boolean expressions, if statements, and logical operators.",
            "order_index": 3,
            "estimated_hours": 6,
            "prerequisites": ["Using Objects"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000004",
            "title": "Iteration",
            "description": "Learn different types of loops including for loops, while loops, and enhanced for loops.",
            "order_index": 4,
            "estimated_hours": 8,
            "prerequisites": ["Boolean Expressions and if Statements"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000005",
            "title": "Writing Classes",
            "description": "Understand class design, constructors, methods, and encapsulation principles.",
            "order_index": 5,
            "estimated_hours": 12,
            "prerequisites": ["Iteration"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000006",
            "title": "Array",
            "description": "Work with one-dimensional arrays, array algorithms, and array processing.",
            "order_index": 6,
            "estimated_hours": 10,
            "prerequisites": ["Writing Classes"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000007",
            "title": "ArrayList",
            "description": "Learn dynamic data structures with ArrayList and collection processing.",
            "order_index": 7,
            "estimated_hours": 8,
            "prerequisites": ["Array"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000008",
            "title": "2D Array",
            "description": "Master two-dimensional arrays and matrix operations.",
            "order_index": 8,
            "estimated_hours": 6,
            "prerequisites": ["ArrayList"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000009",
            "title": "Inheritance",
            "description": "Understand inheritance, polymorphism, and the super keyword.",
            "order_index": 9,
            "estimated_hours": 10,
            "prerequisites": ["2D Array"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "00000000-0000-0000-0000-000000000010",
            "title": "Recursion",
            "description": "Learn recursive thinking and implement recursive algorithms.",
            "order_index": 10,
            "estimated_hours": 8,
            "prerequisites": ["Inheritance"],
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "2.0.0"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 