from pydantic import BaseModel, Field
from typing import List, Optional, Any
from datetime import datetime
from enum import Enum

# 用户相关模型
class UserRole(str, Enum):
    STUDENT = "student"
    TEACHER = "teacher"
    ADMIN = "admin"

class User(BaseModel):
    id: Optional[str] = None
    email: str
    role: UserRole
    full_name: str
    avatar_url: Optional[str] = None
    school: Optional[str] = None
    grade_level: Optional[str] = None
    settings: Optional[dict] = {}
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class UserResponse(BaseModel):
    id: str
    email: str
    role: UserRole
    full_name: str
    avatar_url: Optional[str] = None
    school: Optional[str] = None
    grade_level: Optional[str] = None
    settings: Optional[dict] = {}
    created_at: Optional[datetime] = None

class TokenData(BaseModel):
    user_id: Optional[str] = None
    email: Optional[str] = None

# 课程结构模型
class Unit(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    order_index: int
    estimated_hours: int = 10
    prerequisites: Optional[List[str]] = Field(default_factory=list)
    is_active: bool = True
    created_at: Optional[datetime] = None

class Topic(BaseModel):
    id: Optional[str] = None
    unit_id: str
    title: str
    description: Optional[str] = None
    order_index: int
    estimated_hours: int = 2
    learning_objectives: Optional[List[str]] = Field(default_factory=list)
    is_active: bool = True
    created_at: Optional[datetime] = None

# 题目模型
class QuestionType(str, Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    CODING = "coding"
    SHORT_ANSWER = "short_answer"
    ESSAY = "essay"

class Question(BaseModel):
    id: Optional[str] = None
    topic_id: str
    question_type: QuestionType
    title: str
    content: dict
    correct_answer: Optional[dict] = None
    explanation: Optional[str] = None
    rubric: Optional[dict] = None
    difficulty: int = 1
    points: int = 1
    tags: List[str] = []
    created_by: Optional[str] = None
    is_ai_generated: bool = False
    is_active: bool = True
    usage_count: int = 0
    average_score: float = 0.0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# 响应模型
class UnitWithTopics(BaseModel):
    unit: Unit
    topics: List[Topic]

class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None 