# FastAPI & Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database & Auth
supabase==2.0.0
pydantic==2.5.0
python-multipart==0.0.6

# AI & External Services
google-generativeai==0.3.2
firecrawl-py==0.0.16

# HTTP & Async
aiohttp==3.9.1
requests==2.31.0

# Utilities  
python-dotenv==1.0.0

# Optional: Remove pandas for now since it's not essential for content scraping
# pandas==2.1.4

# Security and Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Date and Time
python-dateutil==2.8.2

# Async Support
asyncio-mqtt==0.16.1

# Performance and Monitoring
psutil==5.9.6

# File Operations
pathlib

# Testing (minimal for production debugging)
pytest==7.4.3
pytest-asyncio==0.21.1 