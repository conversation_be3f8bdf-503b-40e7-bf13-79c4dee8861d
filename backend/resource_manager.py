import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
import re
from urllib.parse import urlparse, parse_qs

from external_content_service import external_content_service
from database import init_supabase

class ResourceManager:
    def __init__(self):
        self.supabase = init_supabase()
    
    async def collect_resources_for_unit(self, unit_id: str, unit_title: str, created_by: str = "system") -> Dict[str, Any]:
        """为指定单元收集并保存外部资源"""
        
        # 创建收集任务记录
        task_id = str(uuid.uuid4())
        task_data = {
            "id": task_id,
            "task_type": "unit_scan",
            "target_unit_id": unit_id,
            "search_query": f"{unit_title} java programming tutorial",
            "status": "running",
            "started_at": datetime.utcnow().isoformat(),
            "created_by": created_by
        }
        
        try:
            # 插入任务记录
            self.supabase.table("resource_collection_tasks").insert(task_data).execute()
            
            # 获取外部资源
            async with external_content_service:
                content = await external_content_service.get_content_for_unit(unit_id, unit_title)
            
            resources_found = 0
            resources_saved = 0
            resources_skipped = 0
            
            # 处理视频资源
            for video in content.get('videos', []):
                resources_found += 1
                if await self._save_resource(video, 'video', unit_id):
                    resources_saved += 1
                else:
                    resources_skipped += 1
            
            # 处理文章资源
            for article in content.get('articles', []):
                resources_found += 1
                if await self._save_resource(article, 'article', unit_id):
                    resources_saved += 1
                else:
                    resources_skipped += 1
            
            # 处理练习资源
            for exercise in content.get('exercises', []):
                resources_found += 1
                if await self._save_resource(exercise, 'interactive_exercise', unit_id):
                    resources_saved += 1
                else:
                    resources_skipped += 1
            
            # 更新任务状态
            update_data = {
                "status": "completed",
                "progress": 100,
                "resources_found": resources_found,
                "resources_saved": resources_saved,
                "resources_skipped": resources_skipped,
                "completed_at": datetime.utcnow().isoformat()
            }
            
            self.supabase.table("resource_collection_tasks").update(update_data).eq("id", task_id).execute()
            
            return {
                "success": True,
                "task_id": task_id,
                "resources_found": resources_found,
                "resources_saved": resources_saved,
                "resources_skipped": resources_skipped,
                "message": f"Successfully collected {resources_saved} resources for {unit_title}"
            }
            
        except Exception as e:
            # 更新任务状态为失败
            error_data = {
                "status": "failed",
                "error_message": str(e),
                "completed_at": datetime.utcnow().isoformat()
            }
            self.supabase.table("resource_collection_tasks").update(error_data).eq("id", task_id).execute()
            
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e),
                "message": f"Failed to collect resources for {unit_title}"
            }
    
    async def _save_resource(self, resource_data: Dict[str, Any], resource_type: str, unit_id: str) -> bool:
        """保存单个资源到数据库"""
        try:
            # 检查URL是否已存在
            existing = self.supabase.table("external_resources").select("id").eq("url", resource_data.get('url')).execute()
            if existing.data:
                print(f"Resource already exists: {resource_data.get('url')}")
                return False
            
            # 提取外部ID（对于YouTube视频）
            external_id = self._extract_external_id(resource_data.get('url', ''), resource_data.get('source', ''))
            
            # 计算初始质量评分
            quality_score = self._calculate_quality_score(resource_data, resource_type)
            
            # 准备资源数据
            db_resource = {
                "title": resource_data.get('title', '')[:500],  # 限制长度
                "description": resource_data.get('description', ''),
                "url": resource_data.get('url', ''),
                "resource_type": resource_type,
                "source_platform": resource_data.get('source', ''),
                "external_id": external_id,
                "thumbnail_url": resource_data.get('thumbnail'),
                "channel_name": resource_data.get('channel'),
                "difficulty_level": resource_data.get('difficulty', 'beginner'),
                "estimated_time": resource_data.get('estimated_time'),
                "programming_language": "Java",
                "unit_ids": [unit_id],
                "topic_keywords": self._extract_keywords(resource_data.get('title', '') + ' ' + resource_data.get('description', '')),
                "tags": self._generate_tags(resource_data, resource_type),
                "category": self._categorize_resource(resource_data, resource_type),
                "quality_score": quality_score,
                "is_verified": False,  # 需要人工验证
                "is_active": True,
                "external_metadata": json.dumps(resource_data)
            }
            
            # 插入数据库
            result = self.supabase.table("external_resources").insert(db_resource).execute()
            
            if result.data:
                print(f"✅ Saved resource: {resource_data.get('title', '')[:50]}...")
                return True
            else:
                print(f"❌ Failed to save resource: {resource_data.get('title', '')}")
                return False
                
        except Exception as e:
            print(f"Error saving resource: {e}")
            return False
    
    def _extract_external_id(self, url: str, source: str) -> Optional[str]:
        """从URL中提取外部平台的ID"""
        if not url:
            return None
            
        if source == "YouTube":
            # 提取YouTube视频ID
            if "youtube.com/watch" in url:
                parsed = urlparse(url)
                query_params = parse_qs(parsed.query)
                return query_params.get('v', [None])[0]
            elif "youtu.be/" in url:
                return url.split('/')[-1].split('?')[0]
        
        # 对于其他平台，可以添加相应的ID提取逻辑
        return None
    
    def _calculate_quality_score(self, resource_data: Dict[str, Any], resource_type: str) -> int:
        """计算资源的质量评分 (0-100)"""
        score = 50  # 基础分数
        
        # 根据来源平台加分
        source = resource_data.get('source', '').lower()
        if 'oracle' in source:
            score += 30  # 官方文档高质量
        elif 'youtube' in source:
            score += 15  # YouTube视频质量中等
        elif 'geeksforgeeks' in source:
            score += 20  # GeeksforGeeks质量较高
        elif 'codehs' in source:
            score += 25  # CodeHS交互练习质量高
        
        # 根据标题和描述质量加分
        title = resource_data.get('title', '')
        description = resource_data.get('description', '')
        
        if len(title) > 20 and len(description) > 50:
            score += 10  # 有详细标题和描述
        
        # 检查关键词匹配
        content = (title + ' ' + description).lower()
        java_keywords = ['java', 'apcsa', 'programming', 'tutorial', 'example']
        keyword_matches = sum(1 for keyword in java_keywords if keyword in content)
        score += keyword_matches * 2
        
        # 确保分数在0-100范围内
        return max(0, min(100, score))
    
    def _extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        # 清理文本
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # 常见的Java和编程关键词
        programming_keywords = {
            'java', 'apcsa', 'programming', 'variables', 'data', 'types', 'primitive',
            'objects', 'classes', 'methods', 'loops', 'iteration', 'arrays', 'arraylist',
            'inheritance', 'recursion', 'boolean', 'conditionals', 'if', 'statements',
            'tutorial', 'examples', 'practice', 'exercises', 'beginner', 'intermediate',
            'advanced', 'coding', 'syntax', 'algorithms'
        }
        
        # 提取匹配的关键词
        keywords = [word for word in words if word in programming_keywords]
        
        # 去重并限制数量
        return list(set(keywords))[:10]
    
    def _generate_tags(self, resource_data: Dict[str, Any], resource_type: str) -> List[str]:
        """为资源生成标签"""
        tags = []
        
        # 基于资源类型的标签
        if resource_type == 'video':
            tags.extend(['video', 'tutorial', 'visual-learning'])
        elif resource_type == 'article':
            tags.extend(['article', 'reading', 'reference'])
        elif resource_type == 'interactive_exercise':
            tags.extend(['practice', 'interactive', 'hands-on'])
        elif resource_type == 'coding_challenge':
            tags.extend(['challenge', 'problem-solving', 'coding'])
        
        # 基于难度的标签
        difficulty = resource_data.get('difficulty', '').lower()
        if difficulty in ['easy', 'beginner']:
            tags.append('beginner-friendly')
        elif difficulty in ['medium', 'intermediate']:
            tags.append('intermediate')
        elif difficulty in ['hard', 'advanced']:
            tags.append('advanced')
        
        # 基于来源的标签
        source = resource_data.get('source', '').lower()
        if 'oracle' in source:
            tags.extend(['official', 'authoritative'])
        elif 'youtube' in source:
            tags.extend(['video-tutorial', 'multimedia'])
        elif 'codehs' in source:
            tags.extend(['structured-learning', 'curriculum'])
        
        return tags[:8]  # 限制标签数量
    
    def _categorize_resource(self, resource_data: Dict[str, Any], resource_type: str) -> str:
        """为资源分类"""
        source = resource_data.get('source', '').lower()
        
        if 'oracle' in source:
            return 'Official Documentation'
        elif resource_type == 'video':
            return 'Video Tutorial'
        elif resource_type == 'interactive_exercise':
            return 'Interactive Exercise'
        elif resource_type == 'coding_challenge':
            return 'Coding Challenge'
        elif resource_type == 'article':
            return 'Tutorial Article'
        else:
            return 'Educational Resource'
    
    async def get_stored_resources(self, unit_id: str = None, resource_type: str = None, 
                                   limit: int = 20, offset: int = 0) -> Dict[str, Any]:
        """获取已存储的外部资源"""
        try:
            query = self.supabase.table("external_resources").select("*")
            
            # 添加过滤条件
            if unit_id:
                query = query.contains("unit_ids", [unit_id])
            if resource_type:
                query = query.eq("resource_type", resource_type)
            
            # 只返回活跃的资源，按质量评分排序
            query = query.eq("is_active", True).order("quality_score", desc=True)
            
            # 分页
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            
            result = query.execute()
            
            return {
                "success": True,
                "resources": result.data,
                "count": len(result.data)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "resources": []
            }
    
    async def update_resource_stats(self, resource_id: str, action: str = "view") -> bool:
        """更新资源的访问统计"""
        try:
            if action == "view":
                # 增加内部访问次数
                result = self.supabase.table("external_resources").update({
                    "view_count_internal": "view_count_internal + 1"
                }).eq("id", resource_id).execute()
            elif action == "click":
                # 增加点击次数
                result = self.supabase.table("external_resources").update({
                    "click_count": "click_count + 1"
                }).eq("id", resource_id).execute()
            
            return True
        except Exception as e:
            print(f"Error updating resource stats: {e}")
            return False
    
    async def get_collection_tasks(self, limit: int = 10) -> Dict[str, Any]:
        """获取资源收集任务列表"""
        try:
            result = self.supabase.table("resource_collection_tasks").select("*").order("created_at", desc=True).limit(limit).execute()
            
            return {
                "success": True,
                "tasks": result.data,
                "count": len(result.data)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tasks": []
            }
    
    async def verify_resource(self, resource_id: str, is_verified: bool, admin_notes: str = None, quality_score: int = None) -> bool:
        """人工验证资源质量"""
        try:
            update_data = {
                "is_verified": is_verified,
                "last_verified_at": datetime.utcnow().isoformat()
            }
            
            if admin_notes:
                update_data["admin_notes"] = admin_notes
            if quality_score is not None:
                update_data["quality_score"] = max(0, min(100, quality_score))
            
            result = self.supabase.table("external_resources").update(update_data).eq("id", resource_id).execute()
            
            return len(result.data) > 0
        except Exception as e:
            print(f"Error verifying resource: {e}")
            return False
    
    async def cleanup_old_resources(self, days_old: int = 90) -> Dict[str, Any]:
        """清理过期的资源（可选功能）"""
        try:
            cutoff_date = (datetime.utcnow() - timedelta(days=days_old)).isoformat()
            
            # 查找低质量且长时间未验证的资源
            result = self.supabase.table("external_resources").select("id").lt("created_at", cutoff_date).lt("quality_score", 50).eq("is_verified", False).execute()
            
            old_resources = result.data
            cleanup_count = len(old_resources)
            
            if cleanup_count > 0:
                # 标记为非活跃而不是删除
                for resource in old_resources:
                    self.supabase.table("external_resources").update({
                        "is_active": False,
                        "admin_notes": f"Auto-deactivated: low quality, unverified for {days_old} days"
                    }).eq("id", resource["id"]).execute()
            
            return {
                "success": True,
                "cleaned_up": cleanup_count,
                "message": f"Deactivated {cleanup_count} old, low-quality resources"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "cleaned_up": 0
            }

# 全局实例
resource_manager = ResourceManager() 