
-- 学习内容主表
CREATE TABLE IF NOT EXISTS learning_content (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    unit_id VARCHAR(50),
    lesson_code VARCHAR(50),
    content_type VARCHAR(50) NOT NULL, -- 'video', 'article', 'document', 'quiz', 'exercise'
    source_platform VARCHAR(50), -- 'Khan Academy', 'CodeHS', 'custom'
    external_url TEXT,
    difficulty_level VARCHAR(20) DEFAULT 'beginner',
    estimated_time_minutes INTEGER DEFAULT 0,
    quality_score DECIMAL(3,1) DEFAULT 0.0,
    is_verified BOOLEAN DEFAULT false,
    tags TEXT[], -- Array of tags
    keywords TEXT[], -- Array of keywords
    metadata JSONB, -- Additional metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_learning_content_unit ON learning_content(unit_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_lesson ON learning_content(lesson_code);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_learning_content_platform ON learning_content(source_platform);

-- 聊天机器人对话表
CREATE TABLE IF NOT EXISTS chatbot_conversations (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255),
    conversation_type VARCHAR(50) DEFAULT 'general',
    user_message TEXT,
    ai_response TEXT,
    context_topic VARCHAR(100),
    context_unit VARCHAR(50),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_chatbot_user ON chatbot_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_type ON chatbot_conversations(conversation_type);

-- 学习进度表
CREATE TABLE IF NOT EXISTS learning_progress (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    content_id BIGINT REFERENCES learning_content(id),
    status VARCHAR(20) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'completed'
    progress_percentage INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    score INTEGER,
    notes TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_progress_user ON learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_progress_content ON learning_progress(content_id);
CREATE INDEX IF NOT EXISTS idx_progress_status ON learning_progress(status);
