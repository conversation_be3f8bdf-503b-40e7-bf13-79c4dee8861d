-- =============================================
-- 统一学习内容数据库架构
-- BoruiCourses - APCSA Learning Platform
-- =============================================

-- 课程单元表 (已存在，参考用)
-- CREATE TABLE units (
--     id SERIAL PRIMARY KEY,
--     title VARCHAR(200) NOT NULL,
--     description TEXT,
--     order_index INTEGER NOT NULL,
--     estimated_hours INTEGER DEFAULT 10,
--     prerequisites TEXT[],
--     is_active BOOLEAN DEFAULT TRUE,
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
-- );

-- 主题表 (已存在，参考用) 
-- CREATE TABLE topics (
--     id SERIAL PRIMARY KEY,
--     unit_id INTEGER REFERENCES units(id),
--     title VARCHAR(200) NOT NULL,
--     description TEXT,
--     order_index INTEGER NOT NULL,
--     estimated_hours INTEGER DEFAULT 2,
--     learning_objectives TEXT[],
--     is_active BOOLEAN DEFAULT TRUE,
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
-- );

-- =============================================
-- 统一学习资源表 - 所有类型的学习内容
-- =============================================
CREATE TABLE IF NOT EXISTS learning_content (
    id SERIAL PRIMARY KEY,
    
    -- 关联信息
    unit_id VARCHAR(50) NOT NULL REFERENCES units(id),
    topic_id UUID REFERENCES topics(id),
    lesson_code VARCHAR(50), -- 如 "1.1", "2.3" 等
    
    -- 内容基本信息
    title VARCHAR(500) NOT NULL,
    description TEXT,
    content_type VARCHAR(50) NOT NULL, -- 'note', 'video', 'question', 'quiz', 'assignment', 'interactive'
    format VARCHAR(50), -- 'markdown', 'html', 'json', 'video_embed', 'pdf'
    
    -- 内容数据 (JSON格式存储)
    content_data JSONB NOT NULL,
    
    -- 来源信息
    source_platform VARCHAR(100), -- 'khan_academy', 'codehs', 'youtube', 'manual', 'ai_generated'
    source_url TEXT,
    external_id VARCHAR(200), -- 外部平台的ID
    channel_name VARCHAR(200), -- YouTube频道名等
    author_name VARCHAR(200),
    
    -- 教学属性
    difficulty_level VARCHAR(20) DEFAULT 'beginner', -- 'beginner', 'intermediate', 'advanced'
    estimated_duration INTEGER, -- 分钟
    learning_objectives TEXT[],
    prerequisites TEXT[],
    tags TEXT[],
    keywords TEXT[],
    
    -- 质量控制
    quality_score INTEGER DEFAULT 0, -- 0-100
    is_verified BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 使用统计
    view_count INTEGER DEFAULT 0,
    completion_count INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    
    -- 系统字段
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_scraped_at TIMESTAMP WITH TIME ZONE,
    
    -- 索引
    CONSTRAINT unique_external_content UNIQUE(source_platform, external_id)
);

-- =============================================
-- 视频内容表 - 专门存储视频相关数据
-- =============================================
CREATE TABLE IF NOT EXISTS video_content (
    id SERIAL PRIMARY KEY,
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    
    -- 视频特定信息
    video_url TEXT NOT NULL,
    embed_code TEXT, -- YouTube/Khan Academy 嵌入代码
    thumbnail_url TEXT,
    duration_seconds INTEGER,
    video_quality VARCHAR(20), -- '720p', '1080p', etc.
    
    -- 频道信息
    channel_id VARCHAR(200),
    channel_name VARCHAR(200),
    channel_url TEXT,
    
    -- 视频统计
    view_count_external INTEGER DEFAULT 0, -- 外部平台观看数
    like_count INTEGER DEFAULT 0,
    upload_date TIMESTAMP WITH TIME ZONE,
    
    -- 字幕和转录
    has_subtitles BOOLEAN DEFAULT FALSE,
    subtitle_languages TEXT[],
    transcript TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- Notes内容表 - 存储笔记和文档
-- =============================================
CREATE TABLE IF NOT EXISTS note_content (
    id SERIAL PRIMARY KEY,
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    
    -- 笔记内容
    content_markdown TEXT NOT NULL,
    content_html TEXT,
    
    -- 结构化数据
    sections JSONB, -- 章节结构
    code_examples JSONB, -- 代码示例
    diagrams JSONB, -- 图表数据
    
    -- 版本控制
    version INTEGER DEFAULT 1,
    parent_version_id INTEGER REFERENCES note_content(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 问题和测验表
-- =============================================
CREATE TABLE IF NOT EXISTS question_content (
    id SERIAL PRIMARY KEY,
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    
    -- 问题信息
    question_type VARCHAR(50) NOT NULL, -- 'multiple_choice', 'coding', 'short_answer', 'true_false', 'fill_blank'
    question_text TEXT NOT NULL,
    question_data JSONB NOT NULL, -- 包含选项、正确答案等
    
    -- 编程题特定
    starter_code TEXT,
    solution_code TEXT,
    test_cases JSONB,
    
    -- 评分
    points INTEGER DEFAULT 1,
    time_limit_minutes INTEGER,
    
    -- 解释和提示
    explanation TEXT,
    hints TEXT[],
    common_mistakes JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 学生进度和完成记录
-- =============================================
CREATE TABLE IF NOT EXISTS learning_progress (
    id SERIAL PRIMARY KEY,
    
    student_id UUID NOT NULL, -- 用户ID
    learning_content_id INTEGER NOT NULL REFERENCES learning_content(id),
    
    -- 进度信息
    status VARCHAR(20) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'completed', 'mastered'
    progress_percentage INTEGER DEFAULT 0,
    
    -- 时间记录
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 成绩记录
    score INTEGER, -- 百分制
    attempts INTEGER DEFAULT 0,
    best_score INTEGER DEFAULT 0,
    
    -- 学习数据
    notes TEXT, -- 学生笔记
    bookmarked BOOLEAN DEFAULT FALSE,
    rating INTEGER, -- 1-5星评分
    feedback TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 确保每个学生每个内容只有一条记录
    CONSTRAINT unique_student_content UNIQUE(student_id, learning_content_id)
);

-- =============================================
-- AI生成内容记录表
-- =============================================
CREATE TABLE IF NOT EXISTS ai_generated_content (
    id SERIAL PRIMARY KEY,
    learning_content_id INTEGER REFERENCES learning_content(id),
    
    -- AI信息
    ai_model VARCHAR(100), -- 'gemini-pro', 'gpt-4', etc.
    generation_type VARCHAR(50), -- 'explanation', 'quiz', 'hint', 'summary'
    
    -- 生成参数
    prompt_used TEXT,
    generation_params JSONB,
    
    -- 质量控制
    human_reviewed BOOLEAN DEFAULT FALSE,
    quality_score INTEGER, -- 0-100
    review_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 创建索引
-- =============================================

-- learning_content 表索引
CREATE INDEX IF NOT EXISTS idx_learning_content_unit_topic ON learning_content(unit_id, topic_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_learning_content_source ON learning_content(source_platform);
CREATE INDEX IF NOT EXISTS idx_learning_content_lesson ON learning_content(lesson_code);
CREATE INDEX IF NOT EXISTS idx_learning_content_active ON learning_content(is_active, is_verified);
CREATE INDEX IF NOT EXISTS idx_learning_content_tags ON learning_content USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_learning_content_keywords ON learning_content USING GIN(keywords);

-- video_content 表索引
CREATE INDEX IF NOT EXISTS idx_video_content_channel ON video_content(channel_id);
CREATE INDEX IF NOT EXISTS idx_video_content_duration ON video_content(duration_seconds);

-- learning_progress 表索引
CREATE INDEX IF NOT EXISTS idx_learning_progress_student ON learning_progress(student_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_status ON learning_progress(status);
CREATE INDEX IF NOT EXISTS idx_learning_progress_completed ON learning_progress(completed_at);

-- question_content 表索引
CREATE INDEX IF NOT EXISTS idx_question_content_type ON question_content(question_type);

-- =============================================
-- 触发器：自动更新 updated_at
-- =============================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
CREATE TRIGGER update_learning_content_updated_at 
    BEFORE UPDATE ON learning_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_video_content_updated_at 
    BEFORE UPDATE ON video_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_note_content_updated_at 
    BEFORE UPDATE ON note_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_question_content_updated_at 
    BEFORE UPDATE ON question_content 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_learning_progress_updated_at 
    BEFORE UPDATE ON learning_progress 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- =============================================
-- 示例数据格式 (注释)
-- =============================================

/*
-- Video内容示例 (Khan Academy)
INSERT INTO learning_content (
    unit_id, topic_id, lesson_code, title, description, content_type, format,
    content_data, source_platform, source_url, external_id, channel_name,
    difficulty_level, estimated_duration, tags, created_by
) VALUES (
    1, 1, '1.1', 'Java基础语法介绍', 'Khan Academy Java基础视频', 'video', 'video_embed',
    '{"embed_code": "<iframe src=...>", "description": "...", "key_points": [...]}',
    'khan_academy', 'https://www.khanacademy.org/...', 'ka_java_basics_001', 'Khan Academy',
    'beginner', 15, '["java", "syntax", "basics"]', 'system'
);

-- Note内容示例 (CodeHS)
INSERT INTO learning_content (
    unit_id, topic_id, lesson_code, title, description, content_type, format,
    content_data, source_platform, source_url, external_id,
    difficulty_level, estimated_duration, tags, created_by
) VALUES (
    1, 1, '1.1', 'Java变量和数据类型', 'CodeHS关于Java变量的详细笔记', 'note', 'markdown',
    '{"markdown": "# Java变量...", "sections": [...], "code_examples": [...]}',
    'codehs', 'https://codehs.com/...', 'codehs_java_variables',
    'beginner', 20, '["java", "variables", "data types"]', 'system'
);

-- Question内容示例
INSERT INTO learning_content (
    unit_id, topic_id, lesson_code, title, description, content_type, format,
    content_data, source_platform, difficulty_level, tags, created_by
) VALUES (
    1, 1, '1.1', 'Java语法测试题', '关于Java基础语法的选择题', 'question', 'json',
    '{"question": "...", "options": [...], "correct_answer": "...", "explanation": "..."}',
    'manual', 'beginner', '["java", "syntax", "quiz"]', 'teacher'
);
*/ 