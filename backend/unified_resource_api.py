"""
统一资源管理API
支持分门别类的资源管理和用户上传
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from typing import List, Optional
from pydantic import BaseModel
import json

from auth import get_current_active_user
from models import UserResponse
from unified_resource_manager import unified_resource_manager

router = APIRouter()

# ===============================
# 数据模型
# ===============================

class ResourceCreateRequest(BaseModel):
    title: str
    description: Optional[str] = None
    content: Optional[str] = None
    category_code: str
    unit_code: str
    lesson_code: Optional[str] = None
    resource_type: str  # 'video', 'article', 'document', 'exercise', 'quiz', 'interactive', 'image', 'audio'
    format: Optional[str] = None
    external_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    tags: Optional[List[str]] = []
    keywords: Optional[List[str]] = []
    difficulty_level: Optional[str] = "beginner"
    estimated_time_minutes: Optional[int] = None
    source_platform: Optional[str] = None
    author_name: Optional[str] = None
    author_email: Optional[str] = None
    quality_score: Optional[float] = 0.0
    is_featured: Optional[bool] = False
    is_public: Optional[bool] = True
    order_index: Optional[int] = 0

class ResourceUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    tags: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    difficulty_level: Optional[str] = None
    estimated_time_minutes: Optional[int] = None
    quality_score: Optional[float] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    order_index: Optional[int] = None
    admin_notes: Optional[str] = None

class CategoryCreateRequest(BaseModel):
    code: str
    title: str
    parent_code: Optional[str] = None
    level: int = 1
    description: Optional[str] = None

class ResourceRatingRequest(BaseModel):
    rating: int  # 1-5
    review_text: Optional[str] = None

# ===============================
# 分类管理API
# ===============================

@router.get("/categories")
async def get_categories(
    level: Optional[int] = None,
    parent_code: Optional[str] = None
):
    """获取资源分类列表"""
    try:
        result = await unified_resource_manager.get_categories(level=level, parent_code=parent_code)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.post("/categories")
async def create_category(
    request: CategoryCreateRequest,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """创建新的资源分类（需要管理员权限）"""
    try:
        # 在实际应用中，这里应该检查用户是否有管理员权限
        # if current_user.role != "admin":
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        result = await unified_resource_manager.create_category(
            code=request.code,
            title=request.title,
            parent_code=request.parent_code,
            level=request.level,
            description=request.description
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建分类失败: {str(e)}")

# ===============================
# 资源管理API
# ===============================

@router.post("/resources")
async def create_resource(
    request: ResourceCreateRequest,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """创建新资源"""
    try:
        resource_data = {
            "title": request.title,
            "description": request.description,
            "content": request.content,
            "category_code": request.category_code,
            "unit_code": request.unit_code,
            "lesson_code": request.lesson_code,
            "resource_type": request.resource_type,
            "format": request.format,
            "external_url": request.external_url,
            "thumbnail_url": request.thumbnail_url,
            "tags": request.tags,
            "keywords": request.keywords,
            "difficulty_level": request.difficulty_level,
            "estimated_time_minutes": request.estimated_time_minutes,
            "source_type": "internal",  # 内部创建
            "source_platform": request.source_platform,
            "author_name": request.author_name,
            "author_email": request.author_email,
            "quality_score": request.quality_score,
            "is_featured": request.is_featured,
            "is_public": request.is_public,
            "order_index": request.order_index
        }
        
        result = await unified_resource_manager.add_resource(resource_data, current_user.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建资源失败: {str(e)}")

@router.post("/resources/upload")
async def upload_file_resource(
    file: UploadFile = File(...),
    resource_data: str = Form(...),  # JSON字符串
    current_user: UserResponse = Depends(get_current_active_user)
):
    """上传文件资源"""
    try:
        # 解析资源数据
        try:
            parsed_data = json.loads(resource_data)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="资源数据格式错误")
        
        # 验证必填字段
        required_fields = ["title", "category_code", "unit_code", "resource_type"]
        for field in required_fields:
            if field not in parsed_data:
                raise HTTPException(status_code=400, detail=f"缺少必填字段: {field}")
        
        # 读取文件数据
        file_data = await file.read()
        
        # 上传文件
        result = await unified_resource_manager.upload_file_resource(
            file_data=file_data,
            filename=file.filename,
            resource_data=parsed_data,
            user_id=current_user.id
        )
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@router.get("/resources")
async def get_resources(
    unit_code: Optional[str] = None,
    lesson_code: Optional[str] = None,
    resource_type: Optional[str] = None,
    difficulty_level: Optional[str] = None,
    source_type: Optional[str] = None,
    tags: Optional[str] = None,  # 逗号分隔的标签
    is_featured: Optional[bool] = None,
    limit: int = 20,
    offset: int = 0
):
    """获取资源列表"""
    try:
        # 解析标签
        tag_list = tags.split(',') if tags else None
        
        result = await unified_resource_manager.get_resources(
            unit_code=unit_code,
            lesson_code=lesson_code,
            resource_type=resource_type,
            difficulty_level=difficulty_level,
            source_type=source_type,
            tags=tag_list,
            is_featured=is_featured,
            limit=limit,
            offset=offset
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取资源失败: {str(e)}")

@router.get("/resources/{resource_id}")
async def get_resource(resource_id: str):
    """获取单个资源详情"""
    try:
        result = await unified_resource_manager.get_resource_by_id(resource_id)
        if not result["success"]:
            raise HTTPException(status_code=404, detail="资源不存在")
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取资源失败: {str(e)}")

@router.put("/resources/{resource_id}")
async def update_resource(
    resource_id: str,
    request: ResourceUpdateRequest,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """更新资源信息"""
    try:
        # 准备更新数据（只包含非None的字段）
        update_data = {}
        for field, value in request.dict().items():
            if value is not None:
                update_data[field] = value
        
        result = await unified_resource_manager.update_resource(
            resource_id=resource_id,
            update_data=update_data,
            user_id=current_user.id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新资源失败: {str(e)}")

@router.delete("/resources/{resource_id}")
async def delete_resource(
    resource_id: str,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """删除资源"""
    try:
        result = await unified_resource_manager.delete_resource(
            resource_id=resource_id,
            user_id=current_user.id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除资源失败: {str(e)}")

# ===============================
# 资源交互API
# ===============================

@router.post("/resources/{resource_id}/view")
async def track_resource_view(
    resource_id: str,
    duration_seconds: Optional[int] = None,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """跟踪资源查看"""
    try:
        result = await unified_resource_manager.track_resource_access(
            resource_id=resource_id,
            access_type="view",
            user_id=current_user.id,
            duration_seconds=duration_seconds
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"跟踪失败: {str(e)}")

@router.post("/resources/{resource_id}/download")
async def track_resource_download(
    resource_id: str,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """跟踪资源下载"""
    try:
        result = await unified_resource_manager.track_resource_access(
            resource_id=resource_id,
            access_type="download",
            user_id=current_user.id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"跟踪失败: {str(e)}")

@router.post("/resources/{resource_id}/rate")
async def rate_resource(
    resource_id: str,
    request: ResourceRatingRequest,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """为资源评分"""
    try:
        result = await unified_resource_manager.rate_resource(
            resource_id=resource_id,
            user_id=current_user.id,
            rating=request.rating,
            review_text=request.review_text
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"评分失败: {str(e)}")

# ===============================
# 外部资源导入API
# ===============================

@router.post("/resources/import")
async def import_external_resources(
    unit_code: str,
    lesson_code: Optional[str] = None,
    current_user: UserResponse = Depends(get_current_active_user)
):
    """导入外部资源到统一格式"""
    try:
        from external_content_service import external_content_service
        
        # 确定分类代码
        category_code = lesson_code if lesson_code else unit_code
        
        # 获取外部内容
        async with external_content_service:
            content = await external_content_service.get_content_for_unit(
                unit_code.replace('unit-', ''), 
                f"Unit {unit_code.replace('unit-', '')}"
            )
        
        imported_count = 0
        results = []
        
        # 导入视频
        for video in content.get('videos', []):
            result = await unified_resource_manager.import_external_resource(
                external_resource=video,
                category_code=category_code,
                unit_code=unit_code,
                lesson_code=lesson_code
            )
            if result["success"]:
                imported_count += 1
            results.append(result)
        
        # 导入文章
        for article in content.get('articles', []):
            result = await unified_resource_manager.import_external_resource(
                external_resource=article,
                category_code=category_code,
                unit_code=unit_code,
                lesson_code=lesson_code
            )
            if result["success"]:
                imported_count += 1
            results.append(result)
        
        # 导入练习
        for exercise in content.get('exercises', []):
            result = await unified_resource_manager.import_external_resource(
                external_resource=exercise,
                category_code=category_code,
                unit_code=unit_code,
                lesson_code=lesson_code
            )
            if result["success"]:
                imported_count += 1
            results.append(result)
        
        return {
            "success": True,
            "message": f"成功导入 {imported_count} 个外部资源",
            "imported_count": imported_count,
            "total_processed": len(results),
            "details": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")

# ===============================
# 统计和管理API
# ===============================

@router.get("/statistics")
async def get_resource_statistics():
    """获取资源统计信息"""
    try:
        result = await unified_resource_manager.get_resource_statistics()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

 