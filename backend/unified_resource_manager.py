"""
统一资源管理器
支持统一格式和分门别类的资源管理，包括用户上传功能
"""

import asyncio
import uuid
import json
import os
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import mimetypes
import hashlib

from database import init_supabase

class UnifiedResourceManager:
    def __init__(self):
        self.supabase = init_supabase()
        self.upload_base_path = Path("uploads/resources")
        self.upload_base_path.mkdir(parents=True, exist_ok=True)
        
    async def create_category(self, code: str, title: str, parent_code: str = None, 
                            level: int = 1, description: str = None) -> Dict[str, Any]:
        """创建新的资源分类"""
        try:
            category_data = {
                "code": code,
                "parent_code": parent_code,
                "level": level,
                "title": title,
                "description": description,
                "order_index": 0,
                "is_active": True
            }
            
            result = self.supabase.table("resource_categories").insert(category_data).execute()
            
            return {
                "success": True,
                "message": f"分类 {code} 创建成功",
                "category": result.data[0] if result.data else None
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"创建分类失败: {str(e)}"
            }
    
    async def get_categories(self, level: int = None, parent_code: str = None) -> Dict[str, Any]:
        """获取资源分类列表"""
        try:
            query = self.supabase.table("resource_categories").select("*").eq("is_active", True)
            
            if level is not None:
                query = query.eq("level", level)
            if parent_code is not None:
                query = query.eq("parent_code", parent_code)
                
            result = query.order("order_index", desc=False).execute()
            
            return {
                "success": True,
                "categories": result.data,
                "count": len(result.data)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "categories": []
            }
    
    async def add_resource(self, resource_data: Dict[str, Any], user_id: str = None) -> Dict[str, Any]:
        """添加统一格式的资源"""
        try:
            # 验证必填字段
            required_fields = ["title", "category_code", "unit_code", "resource_type", "source_type"]
            for field in required_fields:
                if field not in resource_data:
                    return {
                        "success": False,
                        "error": f"缺少必填字段: {field}"
                    }
            
            # 生成资源ID
            resource_id = str(uuid.uuid4())
            
            # 准备资源数据
            unified_resource = {
                "id": resource_id,
                "title": resource_data["title"],
                "description": resource_data.get("description", ""),
                "content": resource_data.get("content", ""),
                
                # 分类信息
                "category_code": resource_data["category_code"],
                "unit_code": resource_data["unit_code"],
                "lesson_code": resource_data.get("lesson_code"),
                
                # 资源类型和格式
                "resource_type": resource_data["resource_type"],
                "format": resource_data.get("format"),
                "file_extension": resource_data.get("file_extension"),
                
                # 文件和链接
                "file_path": resource_data.get("file_path"),
                "external_url": resource_data.get("external_url"),
                "thumbnail_url": resource_data.get("thumbnail_url"),
                "file_size": resource_data.get("file_size"),
                
                # 元数据
                "metadata": resource_data.get("metadata", {}),
                "tags": resource_data.get("tags", []),
                "keywords": resource_data.get("keywords", []),
                
                # 难度和时长
                "difficulty_level": resource_data.get("difficulty_level", "beginner"),
                "estimated_time_minutes": resource_data.get("estimated_time_minutes"),
                
                # 来源信息
                "source_type": resource_data["source_type"],
                "source_platform": resource_data.get("source_platform"),
                "author_name": resource_data.get("author_name"),
                "author_email": resource_data.get("author_email"),
                "created_by_user_id": user_id,
                
                # 质量和验证
                "quality_score": resource_data.get("quality_score", 0.0),
                "is_verified": resource_data.get("is_verified", False),
                "is_featured": resource_data.get("is_featured", False),
                "is_public": resource_data.get("is_public", True),
                "admin_notes": resource_data.get("admin_notes"),
                
                # 排序
                "order_index": resource_data.get("order_index", 0),
                "is_active": True,
                "published_at": datetime.utcnow().isoformat() if resource_data.get("is_public", True) else None
            }
            
            # 插入到数据库
            result = self.supabase.table("unified_resources").insert(unified_resource).execute()
            
            return {
                "success": True,
                "message": "资源添加成功",
                "resource_id": resource_id,
                "resource": result.data[0] if result.data else None
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"添加资源失败: {str(e)}"
            }
    
    async def upload_file_resource(self, file_data: bytes, filename: str, 
                                 resource_data: Dict[str, Any], user_id: str = None) -> Dict[str, Any]:
        """上传文件资源"""
        try:
            # 验证文件大小 (最大100MB)
            max_size = 100 * 1024 * 1024  # 100MB
            if len(file_data) > max_size:
                return {
                    "success": False,
                    "error": "文件大小超过100MB限制"
                }
            
            # 获取文件信息
            file_extension = Path(filename).suffix.lower()
            mime_type, _ = mimetypes.guess_type(filename)
            
            # 生成唯一文件名
            file_hash = hashlib.md5(file_data).hexdigest()
            safe_filename = f"{file_hash}_{filename}"
            
            # 根据分类创建目录
            unit_code = resource_data["unit_code"]
            lesson_code = resource_data.get("lesson_code", "general")
            resource_type = resource_data["resource_type"]
            
            file_dir = self.upload_base_path / unit_code / lesson_code / resource_type
            file_dir.mkdir(parents=True, exist_ok=True)
            
            file_path = file_dir / safe_filename
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(file_data)
            
            # 更新资源数据
            resource_data.update({
                "file_path": str(file_path.relative_to(Path.cwd())),
                "file_extension": file_extension,
                "file_size": len(file_data),
                "format": self._get_format_from_extension(file_extension),
                "source_type": "user_upload",
                "metadata": {
                    **resource_data.get("metadata", {}),
                    "original_filename": filename,
                    "mime_type": mime_type,
                    "upload_timestamp": datetime.utcnow().isoformat()
                }
            })
            
            # 添加资源到数据库
            result = await self.add_resource(resource_data, user_id)
            
            if result["success"]:
                return {
                    **result,
                    "file_path": str(file_path.relative_to(Path.cwd())),
                    "file_size": len(file_data)
                }
            else:
                # 如果数据库插入失败，删除文件
                if file_path.exists():
                    file_path.unlink()
                return result
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"文件上传失败: {str(e)}"
            }
    
    def _get_format_from_extension(self, extension: str) -> str:
        """根据文件扩展名确定格式"""
        format_mapping = {
            '.pdf': 'pdf',
            '.docx': 'docx',
            '.doc': 'doc',
            '.pptx': 'pptx',
            '.ppt': 'ppt',
            '.html': 'html',
            '.md': 'markdown',
            '.txt': 'text',
            '.mp4': 'mp4',
            '.mp3': 'mp3',
            '.jpg': 'image',
            '.jpeg': 'image',
            '.png': 'image',
            '.gif': 'image',
            '.zip': 'archive',
            '.json': 'json',
            '.xml': 'xml'
        }
        return format_mapping.get(extension.lower(), 'unknown')
    
    async def get_resources(self, 
                          unit_code: str = None,
                          lesson_code: str = None,
                          resource_type: str = None,
                          difficulty_level: str = None,
                          source_type: str = None,
                          tags: List[str] = None,
                          is_featured: bool = None,
                          limit: int = 20,
                          offset: int = 0) -> Dict[str, Any]:
        """获取资源列表"""
        try:
            query = self.supabase.table("unified_resources").select("*").eq("is_active", True).eq("is_public", True)
            
            # 应用过滤条件
            if unit_code:
                query = query.eq("unit_code", unit_code)
            if lesson_code:
                query = query.eq("lesson_code", lesson_code)
            if resource_type:
                query = query.eq("resource_type", resource_type)
            if difficulty_level:
                query = query.eq("difficulty_level", difficulty_level)
            if source_type:
                query = query.eq("source_type", source_type)
            if is_featured is not None:
                query = query.eq("is_featured", is_featured)
            if tags:
                # PostgreSQL数组包含查询
                for tag in tags:
                    query = query.contains("tags", [tag])
            
            # 排序和分页
            result = query.order("order_index", desc=False).order("created_at", desc=True).range(offset, offset + limit - 1).execute()
            
            return {
                "success": True,
                "resources": result.data,
                "count": len(result.data),
                "filters_applied": {
                    "unit_code": unit_code,
                    "lesson_code": lesson_code,
                    "resource_type": resource_type,
                    "difficulty_level": difficulty_level,
                    "source_type": source_type,
                    "tags": tags,
                    "is_featured": is_featured
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "resources": []
            }
    
    async def get_resource_by_id(self, resource_id: str) -> Dict[str, Any]:
        """根据ID获取单个资源"""
        try:
            result = self.supabase.table("unified_resources").select("*").eq("id", resource_id).eq("is_active", True).single().execute()
            
            if result.data:
                return {
                    "success": True,
                    "resource": result.data
                }
            else:
                return {
                    "success": False,
                    "error": "资源不存在"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def update_resource(self, resource_id: str, update_data: Dict[str, Any], user_id: str = None) -> Dict[str, Any]:
        """更新资源信息"""
        try:
            # 添加更新时间
            update_data["updated_at"] = datetime.utcnow().isoformat()
            
            result = self.supabase.table("unified_resources").update(update_data).eq("id", resource_id).execute()
            
            return {
                "success": True,
                "message": "资源更新成功",
                "resource": result.data[0] if result.data else None
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"更新资源失败: {str(e)}"
            }
    
    async def delete_resource(self, resource_id: str, user_id: str = None) -> Dict[str, Any]:
        """删除资源（软删除）"""
        try:
            # 获取资源信息
            resource_result = await self.get_resource_by_id(resource_id)
            if not resource_result["success"]:
                return resource_result
            
            resource = resource_result["resource"]
            
            # 软删除（设置为非活跃）
            result = self.supabase.table("unified_resources").update({
                "is_active": False,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", resource_id).execute()
            
            # 如果是用户上传的文件，删除本地文件
            if resource.get("source_type") == "user_upload" and resource.get("file_path"):
                file_path = Path(resource["file_path"])
                if file_path.exists():
                    file_path.unlink()
            
            return {
                "success": True,
                "message": "资源删除成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"删除资源失败: {str(e)}"
            }
    
    async def track_resource_access(self, resource_id: str, access_type: str = "view", 
                                  user_id: str = None, duration_seconds: int = None) -> Dict[str, Any]:
        """跟踪资源访问"""
        try:
            # 记录访问日志
            access_log = {
                "resource_id": resource_id,
                "user_id": user_id,
                "access_type": access_type,
                "duration_seconds": duration_seconds
            }
            
            self.supabase.table("resource_access_logs").insert(access_log).execute()
            
            # 更新资源统计
            if access_type == "view":
                self.supabase.rpc("increment_resource_view_count", {"resource_id": resource_id}).execute()
            elif access_type == "download":
                self.supabase.rpc("increment_resource_download_count", {"resource_id": resource_id}).execute()
            elif access_type == "like":
                self.supabase.rpc("increment_resource_like_count", {"resource_id": resource_id}).execute()
            
            return {"success": True}
            
        except Exception as e:
            print(f"Error tracking resource access: {e}")
            return {"success": False, "error": str(e)}
    
    async def rate_resource(self, resource_id: str, user_id: str, rating: int, review_text: str = None) -> Dict[str, Any]:
        """为资源评分"""
        try:
            if not (1 <= rating <= 5):
                return {
                    "success": False,
                    "error": "评分必须在1-5之间"
                }
            
            # 插入或更新评分
            rating_data = {
                "resource_id": resource_id,
                "user_id": user_id,
                "rating": rating,
                "review_text": review_text
            }
            
            result = self.supabase.table("resource_ratings").upsert(rating_data).execute()
            
            # 重新计算平均评分
            ratings_result = self.supabase.table("resource_ratings").select("rating").eq("resource_id", resource_id).execute()
            
            if ratings_result.data:
                ratings = [r["rating"] for r in ratings_result.data]
                avg_rating = sum(ratings) / len(ratings)
                rating_count = len(ratings)
                
                # 更新资源的平均评分
                self.supabase.table("unified_resources").update({
                    "rating_average": round(avg_rating, 2),
                    "rating_count": rating_count
                }).eq("id", resource_id).execute()
            
            return {
                "success": True,
                "message": "评分提交成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"评分失败: {str(e)}"
            }
    
    async def import_external_resource(self, external_resource: Dict[str, Any], 
                                     category_code: str, unit_code: str, lesson_code: str = None) -> Dict[str, Any]:
        """将外部资源导入统一格式"""
        try:
            # 转换外部资源为统一格式
            unified_data = {
                "title": external_resource.get("title", "Untitled"),
                "description": external_resource.get("description", ""),
                "category_code": category_code,
                "unit_code": unit_code,
                "lesson_code": lesson_code,
                "resource_type": external_resource.get("type", "video"),
                "format": "url" if external_resource.get("url") else "unknown",
                "external_url": external_resource.get("url"),
                "thumbnail_url": external_resource.get("thumbnail"),
                "source_type": "external",
                "source_platform": external_resource.get("source", "unknown"),
                "author_name": external_resource.get("channel") or external_resource.get("author"),
                "difficulty_level": external_resource.get("difficulty", "beginner"),
                "estimated_time_minutes": self._parse_time_to_minutes(external_resource.get("estimated_time")),
                "tags": ["external", "imported"],
                "keywords": [unit_code, lesson_code] if lesson_code else [unit_code],
                "quality_score": 70.0,  # 默认质量分数
                "is_verified": False,
                "metadata": {
                    "original_data": external_resource,
                    "import_timestamp": datetime.utcnow().isoformat()
                }
            }
            
            return await self.add_resource(unified_data)
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"导入外部资源失败: {str(e)}"
            }
    
    def _parse_time_to_minutes(self, time_str: str) -> Optional[int]:
        """解析时间字符串为分钟数"""
        if not time_str:
            return None
        
        try:
            time_str = time_str.lower()
            if "hour" in time_str:
                hours = float(time_str.split()[0])
                return int(hours * 60)
            elif "minute" in time_str:
                minutes = float(time_str.split()[0])
                return int(minutes)
            else:
                return None
        except:
            return None
    
    async def get_resource_statistics(self) -> Dict[str, Any]:
        """获取资源统计信息"""
        try:
            # 总资源数
            total_result = self.supabase.table("unified_resources").select("id", count="exact").eq("is_active", True).execute()
            total_resources = total_result.count
            
            # 按类型统计
            type_stats = {}
            types_result = self.supabase.table("unified_resources").select("resource_type").eq("is_active", True).execute()
            for resource in types_result.data:
                res_type = resource["resource_type"]
                type_stats[res_type] = type_stats.get(res_type, 0) + 1
            
            # 按来源统计
            source_stats = {}
            sources_result = self.supabase.table("unified_resources").select("source_type").eq("is_active", True).execute()
            for resource in sources_result.data:
                source_type = resource["source_type"]
                source_stats[source_type] = source_stats.get(source_type, 0) + 1
            
            # 按单元统计
            unit_stats = {}
            units_result = self.supabase.table("unified_resources").select("unit_code").eq("is_active", True).execute()
            for resource in units_result.data:
                unit_code = resource["unit_code"]
                unit_stats[unit_code] = unit_stats.get(unit_code, 0) + 1
            
            return {
                "success": True,
                "statistics": {
                    "total_resources": total_resources,
                    "by_type": type_stats,
                    "by_source": source_stats,
                    "by_unit": unit_stats
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "statistics": {}
            }

# 全局实例
unified_resource_manager = UnifiedResourceManager() 