# =============================================
# PostgreSQL Configuration for APCSA Platform
# =============================================

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 100
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

# Write Ahead Logging
wal_level = replica
max_wal_senders = 3
max_replication_slots = 3
wal_keep_size = 1GB

# Query Tuning
work_mem = 4MB
min_wal_size = 80MB
max_wal_size = 1GB

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_file_mode = 0600
log_truncate_on_rotation = on
log_rotation_age = 1d
log_rotation_size = 10MB
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0
log_autovacuum_min_duration = 0
log_error_verbosity = default

# Runtime Statistics
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# Autovacuum
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1
autovacuum_freeze_max_age = 200000000
autovacuum_multixact_freeze_max_age = 400000000
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = 200

# Client Connection Defaults
timezone = 'America/Vancouver'
datestyle = 'iso, mdy'
default_text_search_config = 'pg_catalog.english'

# Lock Management
deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64

# Error Reporting and Logging
client_min_messages = notice
log_min_messages = warning
log_min_error_statement = error

# Locale and Formatting
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# Shared Library Preloading
shared_preload_libraries = 'pg_stat_statements'

# Additional Settings for Development
log_statement = 'none'
log_duration = off
debug_print_parse = off
debug_print_rewritten = off
debug_print_plan = off
debug_pretty_print = on

# Security Settings
ssl = off
password_encryption = scram-sha-256

# Performance Settings for Development
fsync = on
synchronous_commit = on
full_page_writes = on
checkpoint_timeout = 5min
archive_mode = off

# Custom Settings for APCSA Platform
# These can be adjusted based on usage patterns
statement_timeout = 0
lock_timeout = 0
idle_in_transaction_session_timeout = 0
tcp_keepalives_idle = 0
tcp_keepalives_interval = 0
tcp_keepalives_count = 0
