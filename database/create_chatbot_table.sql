-- 删除现有表（如果存在）
DROP TABLE IF EXISTS chatbot_conversations;

-- 创建chatbot对话表（无外键约束）
CREATE TABLE chatbot_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    context_topic VARCHAR(255),
    context_unit_id VARCHAR(100),
    context_exercise_id VARCHAR(100),
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    conversation_type VARCHAR(50) DEFAULT 'general',
    session_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_chatbot_conversations_user_id ON chatbot_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversations_created_at ON chatbot_conversations(created_at);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversations_context_topic ON chatbot_conversations(context_topic);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversations_session_id ON chatbot_conversations(session_id);

-- 添加注释
COMMENT ON TABLE chatbot_conversations IS 'AI助手对话记录表';
COMMENT ON COLUMN chatbot_conversations.user_id IS '用户ID';
COMMENT ON COLUMN chatbot_conversations.context_topic IS '对话上下文主题';
COMMENT ON COLUMN chatbot_conversations.context_unit_id IS '相关单元ID';
COMMENT ON COLUMN chatbot_conversations.context_exercise_id IS '相关练习ID';
COMMENT ON COLUMN chatbot_conversations.user_message IS '用户消息';
COMMENT ON COLUMN chatbot_conversations.ai_response IS 'AI回复';
COMMENT ON COLUMN chatbot_conversations.conversation_type IS '对话类型：general, help, explanation等';
COMMENT ON COLUMN chatbot_conversations.session_id IS '会话ID，用于关联同一次对话'; 