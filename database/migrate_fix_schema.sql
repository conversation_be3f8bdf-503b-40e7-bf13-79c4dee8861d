-- 数据库架构修复脚本
-- 修复缺失的表和列

-- 1. 创建 exercises 表（如果不存在）
CREATE TABLE IF NOT EXISTS exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- 'coding', 'multiple_choice', 'fill_blank', 'true_false'
    difficulty VARCHAR(20) NOT NULL, -- 'easy', 'medium', 'hard'
    points INTEGER DEFAULT 10,
    content TEXT, -- 题目内容/代码模板
    solution TEXT, -- 参考答案
    test_cases TEXT[], -- 测试用例（JSON 格式）
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 2. 检查并添加 order_index 列到 learning_resources 表
DO $$
BEGIN
    -- 检查 order_index 列是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'learning_resources' 
        AND column_name = 'order_index'
    ) THEN
        -- 添加 order_index 列
        ALTER TABLE learning_resources ADD COLUMN order_index INTEGER DEFAULT 0;
        RAISE NOTICE 'Added order_index column to learning_resources table';
    ELSE
        RAISE NOTICE 'order_index column already exists in learning_resources table';
    END IF;
END $$;

-- 3. 创建 external_resources 表（用于外部资源管理）
CREATE TABLE IF NOT EXISTS external_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id VARCHAR(50) REFERENCES units(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    source VARCHAR(100) NOT NULL, -- 'youtube', 'oracle', 'geeksforgeeks', etc.
    resource_type VARCHAR(50) NOT NULL, -- 'video', 'article', 'tutorial', 'documentation'
    description TEXT,
    tags TEXT[],
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
    quality_score FLOAT DEFAULT 0.0,
    access_count INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建资源收集任务表
CREATE TABLE IF NOT EXISTS resource_collection_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id VARCHAR(50) REFERENCES units(id) ON DELETE CASCADE,
    search_query VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    total_found INTEGER DEFAULT 0,
    processed_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    error_message TEXT,
    task_data JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建资源评分表
CREATE TABLE IF NOT EXISTS resource_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_id UUID REFERENCES external_resources(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    review TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(resource_id, user_id)
);

-- 6. 创建资源访问日志表
CREATE TABLE IF NOT EXISTS resource_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_id UUID REFERENCES external_resources(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    access_type VARCHAR(50) DEFAULT 'view', -- 'view', 'download', 'share'
    user_agent TEXT,
    ip_address INET,
    session_id VARCHAR(255),
    access_duration INTEGER, -- in seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_exercises_topic_id ON exercises(topic_id);
CREATE INDEX IF NOT EXISTS idx_exercises_difficulty ON exercises(difficulty);
CREATE INDEX IF NOT EXISTS idx_exercises_type ON exercises(type);

CREATE INDEX IF NOT EXISTS idx_learning_resources_topic_id ON learning_resources(topic_id);
CREATE INDEX IF NOT EXISTS idx_learning_resources_order ON learning_resources(order_index);

CREATE INDEX IF NOT EXISTS idx_external_resources_unit ON external_resources(unit_id);
CREATE INDEX IF NOT EXISTS idx_external_resources_source ON external_resources(source);
CREATE INDEX IF NOT EXISTS idx_external_resources_type ON external_resources(resource_type);
CREATE INDEX IF NOT EXISTS idx_external_resources_quality ON external_resources(quality_score);
CREATE INDEX IF NOT EXISTS idx_external_resources_tags ON external_resources USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_resource_tasks_unit ON resource_collection_tasks(unit_id);
CREATE INDEX IF NOT EXISTS idx_resource_tasks_status ON resource_collection_tasks(status);

CREATE INDEX IF NOT EXISTS idx_resource_ratings_resource ON resource_ratings(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_ratings_user ON resource_ratings(user_id);

CREATE INDEX IF NOT EXISTS idx_resource_logs_resource ON resource_access_logs(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_logs_user ON resource_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_resource_logs_date ON resource_access_logs(created_at);

-- 8. 插入一些基本练习数据（如果 exercises 表为空）
INSERT INTO exercises (id, topic_id, title, description, type, difficulty, points, content, solution, test_cases)
SELECT 
    gen_random_uuid(),
    t.id,
    'Hello World 程序',
    '编写你的第一个 Java 程序',
    'coding',
    'easy',
    10,
    '// 请编写一个 Hello World 程序
public class HelloWorld {
    public static void main(String[] args) {
        // 在这里写下你的代码
        
    }
}',
    'public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello World!");
    }
}',
    ARRAY['{"input": "", "expected_output": "Hello World!"}']
FROM topics t 
WHERE t.unit_id = 'unit-1' 
AND t.title LIKE '%1.1%'
AND NOT EXISTS (SELECT 1 FROM exercises WHERE topic_id = t.id)
LIMIT 1;

-- 9. 验证修复结果
DO $$
DECLARE
    exercises_count INTEGER;
    resources_count INTEGER;
    order_column_exists BOOLEAN;
BEGIN
    -- 检查表是否存在并有数据
    SELECT COUNT(*) INTO exercises_count FROM exercises;
    SELECT COUNT(*) INTO resources_count FROM learning_resources;
    
    -- 检查 order_index 列是否存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'learning_resources' 
        AND column_name = 'order_index'
    ) INTO order_column_exists;
    
    RAISE NOTICE '=== 数据库修复验证 ===';
    RAISE NOTICE 'Exercises 表记录数: %', exercises_count;
    RAISE NOTICE 'Learning Resources 表记录数: %', resources_count;
    RAISE NOTICE 'order_index 列存在: %', order_column_exists;
    RAISE NOTICE 'External Resources 表已创建';
    RAISE NOTICE 'Resource Collection Tasks 表已创建';
    RAISE NOTICE 'Resource Ratings 表已创建';
    RAISE NOTICE 'Resource Access Logs 表已创建';
    RAISE NOTICE '所有索引已创建';
    RAISE NOTICE '======================';
END $$; 