-- APCSA AI Learning Platform Database Schema
-- PostgreSQL + Supabase

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- 用户系统
-- =============================================

-- 用户表 (继承Supabase auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255), -- 添加密码字段用于直接认证
    role VARCHAR(50) NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'teacher', 'admin')),
    full_name VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    school VARCHAR(255),
    grade_level VARCHAR(100),
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 班级表
CREATE TABLE classes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    teacher_id UUID REFERENCES users(id),
    class_code VARCHAR(10) UNIQUE NOT NULL, -- 用于学生加入班级
    max_students INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 班级成员表
CREATE TABLE class_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
    student_id UUID REFERENCES users(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    UNIQUE(class_id, student_id)
);

-- =============================================
-- 课程内容结构
-- =============================================

-- APCSA单元表
CREATE TABLE units (
    id VARCHAR(50) PRIMARY KEY, -- 'unit-1', 'unit-2', ..., 'unit-10'
    title VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    estimated_hours INTEGER DEFAULT 10,
    prerequisites VARCHAR(50)[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 知识点/主题表
CREATE TABLE topics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id VARCHAR(50) REFERENCES units(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    estimated_hours INTEGER DEFAULT 2,
    learning_objectives TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 题目系统
-- =============================================

-- 题目表
CREATE TABLE questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id UUID REFERENCES topics(id),
    question_type VARCHAR(50) NOT NULL CHECK (question_type IN ('multiple_choice', 'coding', 'short_answer', 'essay')),
    title VARCHAR(255) NOT NULL,
    content JSONB NOT NULL, -- 题目内容、选项、测试用例等
    correct_answer JSONB,
    explanation TEXT, -- 解题解析
    rubric JSONB, -- 评分标准
    difficulty INTEGER CHECK (difficulty BETWEEN 1 AND 5),
    points INTEGER DEFAULT 1, -- 题目分值
    tags TEXT[],
    created_by UUID REFERENCES users(id),
    is_ai_generated BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    average_score FLOAT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 测验表
CREATE TABLE quizzes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    unit_id VARCHAR(50) REFERENCES units(id),
    topic_id UUID REFERENCES topics(id),
    created_by UUID REFERENCES users(id),
    time_limit INTEGER, -- 时间限制(分钟)
    max_attempts INTEGER DEFAULT 3,
    show_results_immediately BOOLEAN DEFAULT true,
    shuffle_questions BOOLEAN DEFAULT false,
    is_published BOOLEAN DEFAULT false,
    due_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 测验题目关联表
CREATE TABLE quiz_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    quiz_id UUID REFERENCES quizzes(id) ON DELETE CASCADE,
    question_id UUID REFERENCES questions(id),
    order_index INTEGER NOT NULL,
    points INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 学生答题记录
CREATE TABLE quiz_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    quiz_id UUID REFERENCES quizzes(id),
    student_id UUID REFERENCES users(id),
    attempt_number INTEGER DEFAULT 1,
    answers JSONB NOT NULL, -- 学生答案
    score FLOAT DEFAULT 0,
    max_score FLOAT NOT NULL,
    time_spent INTEGER, -- 花费时间(秒)
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    submitted_at TIMESTAMP WITH TIME ZONE,
    is_completed BOOLEAN DEFAULT false,
    ai_feedback TEXT,
    teacher_feedback TEXT
);

-- =============================================
-- 作业系统
-- =============================================

-- 作业表
CREATE TABLE assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    unit_id VARCHAR(50) REFERENCES units(id),
    topic_id UUID REFERENCES topics(id),
    created_by UUID REFERENCES users(id),
    due_date TIMESTAMP WITH TIME ZONE,
    max_score INTEGER DEFAULT 100,
    rubric JSONB,
    file_requirements JSONB, -- 文件类型、大小限制等
    allow_late_submission BOOLEAN DEFAULT true,
    late_penalty_percent FLOAT DEFAULT 10,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 作业提交
CREATE TABLE submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID REFERENCES assignments(id),
    student_id UUID REFERENCES users(id),
    content JSONB, -- 代码、文本内容
    files JSONB[], -- 上传的文件信息
    score FLOAT,
    ai_feedback TEXT,
    teacher_feedback TEXT,
    status VARCHAR(50) DEFAULT 'submitted' 
        CHECK (status IN ('draft', 'submitted', 'graded', 'returned')),
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    graded_at TIMESTAMP WITH TIME ZONE,
    version INTEGER DEFAULT 1,
    is_late BOOLEAN DEFAULT false,
    UNIQUE(assignment_id, student_id)
);

-- 提交历史版本
CREATE TABLE submission_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    submission_id UUID REFERENCES submissions(id) ON DELETE CASCADE,
    content JSONB,
    files JSONB[],
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 学习进度系统
-- =============================================

-- 学生学习进度
CREATE TABLE student_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES users(id),
    unit_id VARCHAR(50) REFERENCES units(id),
    topic_id UUID REFERENCES topics(id),
    completion_rate FLOAT DEFAULT 0 CHECK (completion_rate BETWEEN 0 AND 100),
    mastery_level VARCHAR(50) DEFAULT 'not_started' 
        CHECK (mastery_level IN ('not_started', 'learning', 'practicing', 'mastered')),
    time_spent INTEGER DEFAULT 0, -- 总学习时间(分钟)
    attempts_count INTEGER DEFAULT 0,
    best_score FLOAT DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, topic_id)
);

-- 学习活动记录
CREATE TABLE learning_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES users(id),
    activity_type VARCHAR(50) NOT NULL 
        CHECK (activity_type IN ('quiz', 'assignment', 'video', 'practice', 'review', 'ide')),
    content_id UUID, -- 关联的内容ID
    content_type VARCHAR(50), -- 内容类型
    score FLOAT,
    time_spent INTEGER, -- 分钟
    submission_data JSONB,
    ai_feedback TEXT,
    teacher_feedback TEXT,
    metadata JSONB DEFAULT '{}', -- 额外数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AI内容管理
-- =============================================

-- AI生成内容
CREATE TABLE ai_generated_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(50) NOT NULL 
        CHECK (content_type IN ('question', 'explanation', 'example', 'suggestion', 'feedback')),
    prompt_used TEXT,
    raw_response JSONB,
    processed_content JSONB,
    quality_score FLOAT DEFAULT 0 CHECK (quality_score BETWEEN 0 AND 5),
    usage_count INTEGER DEFAULT 0,
    teacher_reviewed BOOLEAN DEFAULT false,
    teacher_rating INTEGER CHECK (teacher_rating BETWEEN 1 AND 5),
    is_active BOOLEAN DEFAULT true,
    tags TEXT[],
    related_topic_id UUID REFERENCES topics(id),
    created_by_ai_model VARCHAR(50), -- 'gpt-4', 'gemini-pro'等
    generation_cost DECIMAL(10,6), -- AI调用成本
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 内容使用统计
CREATE TABLE content_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID, -- 可以是question_id, ai_content_id等
    content_type VARCHAR(50),
    student_interactions INTEGER DEFAULT 0,
    total_attempts INTEGER DEFAULT 0,
    correct_attempts INTEGER DEFAULT 0,
    average_score FLOAT DEFAULT 0,
    average_time_spent FLOAT DEFAULT 0, -- 平均花费时间
    difficulty_rating FLOAT DEFAULT 0, -- 实际难度评级
    effectiveness_score FLOAT DEFAULT 0, -- 教学效果评分
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 复习系统
-- =============================================

-- 复习计划
CREATE TABLE review_schedule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES users(id),
    content_id UUID, -- 可以是question_id或其他内容
    content_type VARCHAR(50) NOT NULL,
    scheduled_date DATE NOT NULL,
    interval_days INTEGER NOT NULL, -- 复习间隔
    difficulty_factor FLOAT DEFAULT 2.5, -- SM-2算法参数
    repetition_count INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT false,
    completion_quality INTEGER CHECK (completion_quality BETWEEN 0 AND 5), -- 复习质量评分
    completed_at TIMESTAMP WITH TIME ZONE,
    next_review_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 错题本
CREATE TABLE mistake_bank (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES users(id),
    question_id UUID REFERENCES questions(id),
    incorrect_answer JSONB,
    correct_answer JSONB,
    mistake_type VARCHAR(100), -- 错误类型分类
    ai_analysis TEXT, -- AI分析错因
    review_count INTEGER DEFAULT 0,
    last_reviewed TIMESTAMP WITH TIME ZONE,
    mastered BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, question_id)
);

-- =============================================
-- 视频和资源系统
-- =============================================

-- 视频资源
CREATE TABLE videos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    url TEXT NOT NULL, -- 可以是本地文件路径或外部链接
    thumbnail_url TEXT,
    duration INTEGER, -- 视频时长(秒)
    file_size BIGINT, -- 文件大小(字节)
    resolution VARCHAR(20), -- 分辨率
    unit_id VARCHAR(50) REFERENCES units(id),
    topic_id UUID REFERENCES topics(id),
    tags TEXT[],
    transcript TEXT, -- 字幕/转录文本
    is_external BOOLEAN DEFAULT false,
    external_platform VARCHAR(50), -- 'youtube', 'khan_academy'等
    upload_status VARCHAR(50) DEFAULT 'pending' 
        CHECK (upload_status IN ('pending', 'processing', 'completed', 'failed')),
    view_count INTEGER DEFAULT 0,
    average_watch_percentage FLOAT DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频观看记录
CREATE TABLE video_watch_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    video_id UUID REFERENCES videos(id),
    student_id UUID REFERENCES users(id),
    watch_percentage FLOAT DEFAULT 0 CHECK (watch_percentage BETWEEN 0 AND 100),
    watch_time INTEGER DEFAULT 0, -- 观看时长(秒)
    completed BOOLEAN DEFAULT false,
    last_position INTEGER DEFAULT 0, -- 最后观看位置(秒)
    watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(video_id, student_id)
);

-- 学习资源
CREATE TABLE learning_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    resource_type VARCHAR(50) NOT NULL 
        CHECK (resource_type IN ('article', 'tutorial', 'reference', 'example', 'tool')),
    url TEXT,
    content TEXT, -- 如果是内部资源
    unit_id VARCHAR(50) REFERENCES units(id),
    topic_id UUID REFERENCES topics(id),
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    estimated_read_time INTEGER, -- 预估阅读时间(分钟)
    tags TEXT[],
    is_external BOOLEAN DEFAULT false,
    access_count INTEGER DEFAULT 0,
    rating FLOAT DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 系统配置和管理
-- =============================================

-- 系统配置
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- 是否对前端公开
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI提示词模板
CREATE TABLE prompt_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_type VARCHAR(50) NOT NULL 
        CHECK (template_type IN ('question_generation', 'grading', 'feedback', 'explanation')),
    prompt_template TEXT NOT NULL,
    variables JSONB DEFAULT '[]', -- 模板变量定义
    ai_model VARCHAR(50), -- 适用的AI模型
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    success_rate FLOAT DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 通知系统
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    notification_type VARCHAR(50) NOT NULL 
        CHECK (notification_type IN ('assignment_due', 'quiz_available', 'grade_posted', 'review_reminder', 'system')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}', -- 额外数据
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- 索引优化
-- =============================================

-- 用户相关索引
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_class_members_student ON class_members(student_id);
CREATE INDEX idx_class_members_class ON class_members(class_id);

-- 内容相关索引
CREATE INDEX idx_questions_topic ON questions(topic_id);
CREATE INDEX idx_questions_type ON questions(question_type);
CREATE INDEX idx_questions_difficulty ON questions(difficulty);
CREATE INDEX idx_questions_tags ON questions USING GIN(tags);

-- 学习进度索引
CREATE INDEX idx_progress_student ON student_progress(student_id);
CREATE INDEX idx_progress_unit ON student_progress(unit_id);
CREATE INDEX idx_progress_topic ON student_progress(topic_id);
CREATE INDEX idx_activities_student ON learning_activities(student_id);
CREATE INDEX idx_activities_type ON learning_activities(activity_type);
CREATE INDEX idx_activities_date ON learning_activities(created_at);

-- 复习系统索引
CREATE INDEX idx_review_student_date ON review_schedule(student_id, scheduled_date);
CREATE INDEX idx_review_content ON review_schedule(content_id, content_type);
CREATE INDEX idx_mistake_bank_student ON mistake_bank(student_id);

-- AI内容索引
CREATE INDEX idx_ai_content_type ON ai_generated_content(content_type);
CREATE INDEX idx_ai_content_topic ON ai_generated_content(related_topic_id);
CREATE INDEX idx_ai_content_active ON ai_generated_content(is_active);

-- 视频和资源索引
CREATE INDEX idx_videos_unit ON videos(unit_id);
CREATE INDEX idx_videos_topic ON videos(topic_id);
CREATE INDEX idx_watch_history_student ON video_watch_history(student_id);
CREATE INDEX idx_resources_type ON learning_resources(resource_type);

-- 通知索引
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = false; 