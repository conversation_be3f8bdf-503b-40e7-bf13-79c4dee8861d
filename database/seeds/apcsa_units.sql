-- APCSA Units Seed Data
-- 插入 AP Computer Science A 课程的 10 个单元

INSERT INTO units (id, title, description, order_index, estimated_hours, prerequisites, is_active) VALUES
('unit-1', 'Primitive Types', 'Introduction to Java basics, primitive data types, variables, and basic operations', 1, 12, '{}', true),
('unit-2', 'Using Objects', 'Working with objects, methods, and the String class', 2, 15, '{"unit-1"}', true),
('unit-3', 'Boolean Expressions and if Statements', 'Conditional logic, Boolean expressions, and control flow', 3, 12, '{"unit-1", "unit-2"}', true),
('unit-4', 'Iteration', 'Loops including for, while, and enhanced for loops', 4, 18, '{"unit-1", "unit-2", "unit-3"}', true),
('unit-5', 'Writing Classes', 'Creating classes, constructors, methods, and encapsulation', 5, 20, '{"unit-1", "unit-2", "unit-3", "unit-4"}', true),
('unit-6', 'Array', 'One-dimensional arrays, traversing arrays, and array algorithms', 6, 16, '{"unit-1", "unit-2", "unit-3", "unit-4"}', true),
('unit-7', 'ArrayList', 'Dynamic arrays using ArrayList, methods, and traversing ArrayLists', 7, 14, '{"unit-1", "unit-2", "unit-3", "unit-4", "unit-5", "unit-6"}', true),
('unit-8', '2D Array', 'Two-dimensional arrays, nested loops, and 2D array algorithms', 8, 16, '{"unit-1", "unit-2", "unit-3", "unit-4", "unit-6"}', true),
('unit-9', 'Inheritance', 'Class hierarchies, inheritance, polymorphism, and the Object class', 9, 18, '{"unit-1", "unit-2", "unit-3", "unit-4", "unit-5"}', true),
('unit-10', 'Recursion', 'Recursive methods, base cases, and recursive algorithms', 10, 14, '{"unit-1", "unit-2", "unit-3", "unit-4", "unit-5"}', true);

-- 提交更改
COMMIT; 