-- External Resources Database Schema
-- 用于存储从外部平台抓取并保存的学习资源

-- 创建外部资源表
CREATE TABLE IF NOT EXISTS external_resources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    url VARCHAR(1000) NOT NULL,
    resource_type VARCHAR(50) NOT NULL, -- video, article, interactive_exercise, coding_challenge
    source_platform VARCHAR(100) NOT NULL, -- YouTube, Oracle, GeeksforGeeks, CodeHS, etc.
    
    -- Video 相关字段
    thumbnail_url VARCHAR(1000),
    video_duration VARCHAR(20),
    channel_name VARCHAR(200),
    view_count INTEGER,
    
    -- 文章相关字段
    author VARCHAR(200),
    publish_date DATE,
    reading_time VARCHAR(20),
    
    -- 练习相关字段
    difficulty_level VARCHAR(20), -- easy, medium, hard, beginner, intermediate, advanced
    estimated_time VARCHAR(50),
    programming_language VARCHAR(50) DEFAULT 'Java',
    
    -- 分类和标签
    unit_ids TEXT[], -- 关联的单元ID数组
    topic_keywords TEXT[], -- 相关的主题关键词
    tags TEXT[], -- 自定义标签
    category VARCHAR(100), -- 主要分类
    
    -- 质量控制
    quality_score INTEGER DEFAULT 0, -- 0-100的质量评分
    is_verified BOOLEAN DEFAULT FALSE, -- 是否经过人工验证
    is_active BOOLEAN DEFAULT TRUE, -- 是否启用
    admin_notes TEXT, -- 管理员备注
    
    -- 访问统计
    view_count_internal INTEGER DEFAULT 0, -- 内部访问次数
    click_count INTEGER DEFAULT 0, -- 点击次数
    rating DECIMAL(3,2), -- 用户评分 (1.00-5.00)
    
    -- 外部ID和元数据
    external_id VARCHAR(200), -- 在外部平台的ID
    external_metadata JSONB, -- 存储额外的外部数据
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_verified_at TIMESTAMP WITH TIME ZONE,
    
    -- 索引
    UNIQUE(url) -- URL唯一性约束
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_external_resources_type ON external_resources(resource_type);
CREATE INDEX IF NOT EXISTS idx_external_resources_source ON external_resources(source_platform);
CREATE INDEX IF NOT EXISTS idx_external_resources_difficulty ON external_resources(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_external_resources_quality ON external_resources(quality_score);
CREATE INDEX IF NOT EXISTS idx_external_resources_active ON external_resources(is_active);
CREATE INDEX IF NOT EXISTS idx_external_resources_unit_ids ON external_resources USING GIN(unit_ids);
CREATE INDEX IF NOT EXISTS idx_external_resources_keywords ON external_resources USING GIN(topic_keywords);
CREATE INDEX IF NOT EXISTS idx_external_resources_tags ON external_resources USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_external_resources_created ON external_resources(created_at);

-- 创建资源收集任务表 (用于跟踪抓取任务)
CREATE TABLE IF NOT EXISTS resource_collection_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_type VARCHAR(50) NOT NULL, -- unit_scan, topic_scan, manual_add
    target_unit_id VARCHAR(100),
    target_topic VARCHAR(200),
    search_query VARCHAR(500),
    
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    progress INTEGER DEFAULT 0, -- 0-100
    
    resources_found INTEGER DEFAULT 0,
    resources_saved INTEGER DEFAULT 0,
    resources_skipped INTEGER DEFAULT 0,
    
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    created_by VARCHAR(100), -- 创建任务的用户/系统
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建资源评分表 (用户评分)
CREATE TABLE IF NOT EXISTS resource_ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_id UUID REFERENCES external_resources(id) ON DELETE CASCADE,
    user_id UUID, -- 关联用户ID (如果有用户系统)
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_helpful BOOLEAN,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(resource_id, user_id) -- 每个用户对每个资源只能评分一次
);

-- 创建资源访问日志表
CREATE TABLE IF NOT EXISTS resource_access_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_id UUID REFERENCES external_resources(id) ON DELETE CASCADE,
    user_id UUID, -- 关联用户ID (如果有用户系统)
    access_type VARCHAR(20) NOT NULL, -- view, click, download
    user_agent TEXT,
    ip_address INET,
    referrer_url VARCHAR(1000),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_resource_ratings_resource ON resource_ratings(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_ratings_rating ON resource_ratings(rating);
CREATE INDEX IF NOT EXISTS idx_resource_access_logs_resource ON resource_access_logs(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_access_logs_type ON resource_access_logs(access_type);
CREATE INDEX IF NOT EXISTS idx_resource_access_logs_created ON resource_access_logs(created_at);

-- 创建触发器自动更新 updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_external_resources_updated_at 
    BEFORE UPDATE ON external_resources 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_resource_collection_tasks_updated_at 
    BEFORE UPDATE ON resource_collection_tasks 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_resource_ratings_updated_at 
    BEFORE UPDATE ON resource_ratings 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- 插入一些示例外部资源数据
INSERT INTO external_resources (
    title, description, url, resource_type, source_platform,
    thumbnail_url, channel_name, difficulty_level, estimated_time,
    unit_ids, topic_keywords, tags, category, quality_score, is_verified
) VALUES 
-- YouTube 视频资源
(
    'Java Programming Tutorial - Variables and Data Types',
    'Comprehensive tutorial covering all primitive data types in Java including int, double, boolean, and char. Perfect for APCSA students learning Unit 1.',
    'https://www.youtube.com/watch?v=eIrMbAQSU34',
    'video',
    'YouTube',
    'https://img.youtube.com/vi/eIrMbAQSU34/mqdefault.jpg',
    'Programming with Mosh',
    'beginner',
    '45 minutes',
    ARRAY['unit-1'],
    ARRAY['variables', 'data types', 'primitive types', 'java basics'],
    ARRAY['tutorial', 'beginner-friendly', 'comprehensive'],
    'Programming Tutorial',
    85,
    true
),
(
    'Java Loops Explained - For, While, and Do-While',
    'Complete guide to all types of loops in Java with practical examples and common patterns.',
    'https://www.youtube.com/watch?v=A74TOX803D0',
    'video',
    'YouTube',
    'https://img.youtube.com/vi/A74TOX803D0/mqdefault.jpg',
    'Coding with John',
    'intermediate',
    '35 minutes',
    ARRAY['unit-4'],
    ARRAY['loops', 'iteration', 'for loop', 'while loop'],
    ARRAY['loops', 'control structures', 'algorithms'],
    'Programming Tutorial',
    90,
    true
),

-- Oracle 文档资源
(
    'Primitive Data Types (Oracle Java Documentation)',
    'Official Oracle documentation explaining all primitive data types, their ranges, and usage in Java programming.',
    'https://docs.oracle.com/javase/tutorial/java/nutsandbolts/datatypes.html',
    'article',
    'Oracle Java Documentation',
    null,
    null,
    'beginner',
    '15 minutes',
    ARRAY['unit-1'],
    ARRAY['primitive types', 'data types', 'official documentation'],
    ARRAY['official', 'reference', 'authoritative'],
    'Official Documentation',
    95,
    true
),
(
    'The if-then and if-then-else Statements',
    'Official Oracle tutorial on conditional statements in Java, covering if, if-else, and nested conditionals.',
    'https://docs.oracle.com/javase/tutorial/java/nutsandbolts/if.html',
    'article',
    'Oracle Java Documentation',
    null,
    null,
    'beginner',
    '20 minutes',
    ARRAY['unit-3'],
    ARRAY['if statements', 'conditionals', 'boolean expressions'],
    ARRAY['official', 'conditionals', 'control flow'],
    'Official Documentation',
    95,
    true
),

-- GeeksforGeeks 资源
(
    'Java Data Types with Examples - GeeksforGeeks',
    'Comprehensive article covering all Java data types with practical examples and common use cases.',
    'https://www.geeksforgeeks.org/data-types-in-java/',
    'article',
    'GeeksforGeeks',
    null,
    null,
    'beginner',
    '25 minutes',
    ARRAY['unit-1'],
    ARRAY['data types', 'primitive types', 'examples', 'java'],
    ARRAY['examples', 'comprehensive', 'beginner'],
    'Tutorial Article',
    80,
    true
),

-- CodeHS 交互式练习
(
    'Variables and Primitive Types - Interactive Exercises',
    'Hands-on coding exercises for practicing variable declaration and primitive type usage in Java.',
    'https://codehs.com/course/apcsa/lesson/1.2',
    'interactive_exercise',
    'CodeHS',
    null,
    null,
    'beginner',
    '30 minutes',
    ARRAY['unit-1'],
    ARRAY['variables', 'primitive types', 'practice', 'interactive'],
    ARRAY['practice', 'interactive', 'hands-on'],
    'Interactive Exercise',
    85,
    true
),
(
    'Boolean Expressions and Conditionals Practice',
    'Interactive coding problems focused on boolean logic and conditional statements.',
    'https://codehs.com/course/apcsa/lesson/3.1',
    'interactive_exercise',
    'CodeHS',
    null,
    null,
    'intermediate',
    '45 minutes',
    ARRAY['unit-3'],
    ARRAY['boolean expressions', 'conditionals', 'if statements'],
    ARRAY['practice', 'logic', 'conditionals'],
    'Interactive Exercise',
    85,
    true
),

-- HackerRank 编程挑战
(
    'Java Introduction Challenges',
    'Collection of beginner-friendly Java programming challenges covering basic syntax and concepts.',
    'https://www.hackerrank.com/domains/java',
    'coding_challenge',
    'HackerRank',
    null,
    null,
    'beginner',
    '1-2 hours',
    ARRAY['unit-1', 'unit-2'],
    ARRAY['java basics', 'syntax', 'problem solving'],
    ARRAY['challenges', 'problem-solving', 'practice'],
    'Coding Challenge',
    80,
    true
);

-- 插入一个示例收集任务
INSERT INTO resource_collection_tasks (
    task_type, target_unit_id, search_query, status, progress,
    resources_found, resources_saved, created_by
) VALUES 
(
    'unit_scan',
    'unit-1',
    'Java primitive types variables programming tutorial',
    'completed',
    100,
    15,
    8,
    'system'
); 