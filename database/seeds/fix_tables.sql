-- 修复表结构脚本

-- 删除并重新创建 learning_resources 表
DROP TABLE IF EXISTS learning_resources CASCADE;
CREATE TABLE learning_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'video', 'article', 'external_link', 'pdf'
    url TEXT,
    description TEXT,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 重新插入学习资源数据
INSERT INTO learning_resources (id, topic_id, title, type, url, description, order_index) VALUES
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.1 Why Programming? Why Java?' LIMIT 1),
  'Java 官方历史文档',
  'external_link',
  'https://www.oracle.com/java/technologies/java-se-support-roadmap.html',
  'Oracle 官方提供的 Java 发展历史和支持路线图',
  1
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.2 Variables and Data Types' LIMIT 1),
  'Java 数据类型详解',
  'video',
  'https://www.youtube.com/watch?v=example',
  '详细解释 Java 中四种基本数据类型的特点和使用方法',
  1
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.2 Variables and Data Types' LIMIT 1),
  'Oracle Java 教程 - 原始数据类型',
  'external_link',
  'https://docs.oracle.com/javase/tutorial/java/nutsandbolts/datatypes.html',
  'Oracle 官方教程中关于原始数据类型的详细说明',
  2
);

-- 重新插入练习题目
INSERT INTO exercises (id, topic_id, title, description, type, difficulty, points, content, solution, test_cases) VALUES
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.2 Variables and Data Types' LIMIT 1),
  '变量声明练习',
  '练习声明和初始化不同类型的变量',
  'coding',
  'easy',
  10,
  '// 请声明以下变量并赋予适当的初值：
// 1. 一个整数变量 age，值为 18
// 2. 一个双精度浮点数 price，值为 99.99
// 3. 一个布尔变量 isStudent，值为 true
// 4. 一个字符变量 grade，值为 ''A''

public class VariableDeclaration {
    public static void main(String[] args) {
        // 在这里写下你的代码
        
        
        
        
        // 打印所有变量
        System.out.println("Age: " + age);
        System.out.println("Price: " + price);
        System.out.println("Is Student: " + isStudent);
        System.out.println("Grade: " + grade);
    }
}',
  'public class VariableDeclaration {
    public static void main(String[] args) {
        int age = 18;
        double price = 99.99;
        boolean isStudent = true;
        char grade = ''A'';
        
        System.out.println("Age: " + age);
        System.out.println("Price: " + price);
        System.out.println("Is Student: " + isStudent);
        System.out.println("Grade: " + grade);
    }
}',
  ARRAY[
    '{"input": "", "expected_output": "Age: 18\nPrice: 99.99\nIs Student: true\nGrade: A"}'
  ]
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.3 Expressions and Assignment Statements' LIMIT 1),
  '算术运算练习',
  '使用算术运算符计算数学表达式',
  'coding',
  'medium',
  15,
  '// 给定两个整数，计算它们的和、差、积、商和余数
// 注意：除法结果应该是整数除法

public class ArithmeticOperations {
    public static void main(String[] args) {
        int a = 17;
        int b = 5;
        
        // 计算并打印结果
        // 格式：a + b = result
        
        
        
        
        
    }
}',
  'public class ArithmeticOperations {
    public static void main(String[] args) {
        int a = 17;
        int b = 5;
        
        int sum = a + b;
        int difference = a - b;
        int product = a * b;
        int quotient = a / b;
        int remainder = a % b;
        
        System.out.println(a + " + " + b + " = " + sum);
        System.out.println(a + " - " + b + " = " + difference);
        System.out.println(a + " * " + b + " = " + product);
        System.out.println(a + " / " + b + " = " + quotient);
        System.out.println(a + " % " + b + " = " + remainder);
    }
}',
  ARRAY[
    '{"input": "", "expected_output": "17 + 5 = 22\n17 - 5 = 12\n17 * 5 = 85\n17 / 5 = 3\n17 % 5 = 2"}'
  ]
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.5 Casting and Ranges of Variables' LIMIT 1),
  '类型转换练习',
  '练习不同数据类型之间的转换',
  'multiple_choice',
  'medium',
  10,
  '以下代码的输出是什么？

```java
int x = 10;
double y = 3.7;
int result = x + (int) y;
System.out.println(result);
```

A) 13.7
B) 13
C) 14
D) 编译错误',
  'B) 13

解释：
1. x = 10 (整数)
2. y = 3.7 (双精度浮点数)
3. (int) y 将 3.7 强制转换为整数 3（截断小数部分）
4. x + (int) y = 10 + 3 = 13
5. result 被赋值为 13',
  NULL
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_exercises_topic_id ON exercises(topic_id);
CREATE INDEX IF NOT EXISTS idx_exercises_difficulty ON exercises(difficulty);
CREATE INDEX IF NOT EXISTS idx_learning_resources_topic_id ON learning_resources(topic_id);

-- 验证数据
SELECT 'Topics added:' as info, count(*) as count FROM topics WHERE title LIKE '1.%';
SELECT 'Exercises added:' as info, count(*) as count FROM exercises;
SELECT 'Learning resources added:' as info, count(*) as count FROM learning_resources; 