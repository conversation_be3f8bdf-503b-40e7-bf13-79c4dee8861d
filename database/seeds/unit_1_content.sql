-- Unit 1: Primitive Types 详细内容
-- 这将为 APCSA 第一单元提供完整的学习材料

-- 首先插入详细的主题内容
INSERT INTO topics (id, unit_id, title, description, order_index, estimated_hours, learning_objectives, is_active) VALUES
(
  gen_random_uuid(),
  (SELECT id FROM units WHERE title = 'Primitive Types' LIMIT 1),
  '1.1 Why Programming? Why Java?',
  '了解编程的重要性以及为什么选择 Java 作为学习语言。探索 Java 的历史、特性和在现代软件开发中的应用。',
  1,
  2,
  ARRAY[
    '理解编程在现代社会中的作用',
    '了解 Java 语言的历史和特点',
    '认识 Java 的平台无关性',
    '理解 "Write Once, Run Anywhere" 概念',
    '了解 Java 在企业级开发中的地位'
  ],
  true
),
(
  gen_random_uuid(),
  (SELECT id FROM units WHERE title = 'Primitive Types' LIMIT 1),
  '1.2 Variables and Data Types',
  '深入学习 Java 中的原始数据类型，包括 int、double、boolean 和 char。理解变量声明、初始化和作用域。',
  2,
  3,
  ARRAY[
    '掌握四种基本数据类型：int、double、boolean、char',
    '理解变量的声明和初始化',
    '学习变量命名规范',
    '理解数据类型的存储范围和精度',
    '掌握类型转换的基本概念'
  ],
  true
),
(
  gen_random_uuid(),
  (SELECT id FROM units WHERE title = 'Primitive Types' LIMIT 1),
  '1.3 Expressions and Assignment Statements',
  '学习如何使用算术运算符创建表达式，理解赋值语句的工作原理，以及运算符的优先级。',
  3,
  2,
  ARRAY[
    '掌握算术运算符：+、-、*、/、%',
    '理解运算符的优先级和结合性',
    '学习复合赋值运算符',
    '理解表达式的求值过程',
    '掌握类型提升的概念'
  ],
  true
),
(
  gen_random_uuid(),
  (SELECT id FROM units WHERE title = 'Primitive Types' LIMIT 1),
  '1.4 Compound Assignment Operators',
  '深入学习复合赋值运算符（+=、-=、*=、/=、%=），以及自增自减运算符（++、--）的使用。',
  4,
  1,
  ARRAY[
    '掌握所有复合赋值运算符的使用',
    '理解前置和后置递增/递减运算符的区别',
    '学习运算符的简写形式',
    '理解复合运算符的执行顺序',
    '掌握运算符在循环中的应用'
  ],
  true
),
(
  gen_random_uuid(),
  (SELECT id FROM units WHERE title = 'Primitive Types' LIMIT 1),
  '1.5 Casting and Ranges of Variables',
  '学习显式和隐式类型转换，理解不同数据类型的取值范围，以及类型转换中可能出现的精度损失。',
  5,
  2,
  ARRAY[
    '理解隐式类型转换（自动类型提升）',
    '掌握显式类型转换（强制类型转换）',
    '了解各种数据类型的取值范围',
    '理解精度损失和数据截断',
    '学习安全的类型转换实践'
  ],
  true
);

-- 插入练习题目
INSERT INTO exercises (id, topic_id, title, description, type, difficulty, points, content, solution, test_cases) VALUES
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.2 Variables and Data Types' LIMIT 1),
  '变量声明练习',
  '练习声明和初始化不同类型的变量',
  'coding',
  'easy',
  10,
  '// 请声明以下变量并赋予适当的初值：
// 1. 一个整数变量 age，值为 18
// 2. 一个双精度浮点数 price，值为 99.99
// 3. 一个布尔变量 isStudent，值为 true
// 4. 一个字符变量 grade，值为 ''A''

public class VariableDeclaration {
    public static void main(String[] args) {
        // 在这里写下你的代码
        
        
        
        
        // 打印所有变量
        System.out.println("Age: " + age);
        System.out.println("Price: " + price);
        System.out.println("Is Student: " + isStudent);
        System.out.println("Grade: " + grade);
    }
}',
  'public class VariableDeclaration {
    public static void main(String[] args) {
        int age = 18;
        double price = 99.99;
        boolean isStudent = true;
        char grade = ''A'';
        
        System.out.println("Age: " + age);
        System.out.println("Price: " + price);
        System.out.println("Is Student: " + isStudent);
        System.out.println("Grade: " + grade);
    }
}',
  ARRAY[
    '{"input": "", "expected_output": "Age: 18\nPrice: 99.99\nIs Student: true\nGrade: A"}',
    '{"input": "", "expected_output": "Age: 18\nPrice: 99.99\nIs Student: true\nGrade: A"}'
  ]
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.3 Expressions and Assignment Statements' LIMIT 1),
  '算术运算练习',
  '使用算术运算符计算数学表达式',
  'coding',
  'medium',
  15,
  '// 给定两个整数，计算它们的和、差、积、商和余数
// 注意：除法结果应该是整数除法

public class ArithmeticOperations {
    public static void main(String[] args) {
        int a = 17;
        int b = 5;
        
        // 计算并打印结果
        // 格式：a + b = result
        
        
        
        
        
    }
}',
  'public class ArithmeticOperations {
    public static void main(String[] args) {
        int a = 17;
        int b = 5;
        
        int sum = a + b;
        int difference = a - b;
        int product = a * b;
        int quotient = a / b;
        int remainder = a % b;
        
        System.out.println(a + " + " + b + " = " + sum);
        System.out.println(a + " - " + b + " = " + difference);
        System.out.println(a + " * " + b + " = " + product);
        System.out.println(a + " / " + b + " = " + quotient);
        System.out.println(a + " % " + b + " = " + remainder);
    }
}',
  ARRAY[
    '{"input": "", "expected_output": "17 + 5 = 22\n17 - 5 = 12\n17 * 5 = 85\n17 / 5 = 3\n17 % 5 = 2"}'
  ]
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.5 Casting and Ranges of Variables' LIMIT 1),
  '类型转换练习',
  '练习不同数据类型之间的转换',
  'multiple_choice',
  'medium',
  10,
  '以下代码的输出是什么？

```java
int x = 10;
double y = 3.7;
int result = x + (int) y;
System.out.println(result);
```

A) 13.7
B) 13
C) 14
D) 编译错误',
  'B) 13

解释：
1. x = 10 (整数)
2. y = 3.7 (双精度浮点数)
3. (int) y 将 3.7 强制转换为整数 3（截断小数部分）
4. x + (int) y = 10 + 3 = 13
5. result 被赋值为 13',
  NULL
);

-- 插入学习材料/资源
INSERT INTO learning_resources (id, topic_id, title, type, url, description, order_index) VALUES
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.1 Why Programming? Why Java?' LIMIT 1),
  'Java 官方历史文档',
  'external_link',
  'https://www.oracle.com/java/technologies/java-se-support-roadmap.html',
  'Oracle 官方提供的 Java 发展历史和支持路线图',
  1
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.2 Variables and Data Types' LIMIT 1),
  'Java 数据类型详解',
  'video',
  'https://www.youtube.com/watch?v=example',
  '详细解释 Java 中四种基本数据类型的特点和使用方法',
  1
),
(
  gen_random_uuid(),
  (SELECT id FROM topics WHERE title = '1.2 Variables and Data Types' LIMIT 1),
  'Oracle Java 教程 - 原始数据类型',
  'external_link',
  'https://docs.oracle.com/javase/tutorial/java/nutsandbolts/datatypes.html',
  'Oracle 官方教程中关于原始数据类型的详细说明',
  2
);

-- 创建相关表（如果不存在）
CREATE TABLE IF NOT EXISTS exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- 'coding', 'multiple_choice', 'fill_blank', 'true_false'
    difficulty VARCHAR(20) NOT NULL, -- 'easy', 'medium', 'hard'
    points INTEGER DEFAULT 10,
    content TEXT, -- 题目内容/代码模板
    solution TEXT, -- 参考答案
    test_cases TEXT[], -- 测试用例（JSON 格式）
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS learning_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'video', 'article', 'external_link', 'pdf'
    url TEXT,
    description TEXT,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_exercises_topic_id ON exercises(topic_id);
CREATE INDEX IF NOT EXISTS idx_exercises_difficulty ON exercises(difficulty);
CREATE INDEX IF NOT EXISTS idx_learning_resources_topic_id ON learning_resources(topic_id);

COMMENT ON TABLE exercises IS 'Unit 1 练习题目，包括编程题、选择题等多种类型';
COMMENT ON TABLE learning_resources IS 'Unit 1 学习资源，包括视频、文章、外部链接等'; 