-- 设置数据库时区为温哥华时间
-- 运行这个脚本在 Supabase SQL Editor 中

-- 检查当前时区设置
SELECT current_setting('timezone');

-- 设置会话时区为温哥华
SET timezone = 'America/Vancouver';

-- 验证设置
SELECT 
  current_setting('timezone') as current_timezone,
  now() as current_time_utc,
  now() AT TIME ZONE 'America/Vancouver' as vancouver_time;

-- 创建一个函数来获取温哥华时间
CREATE OR REPLACE FUNCTION get_vancouver_time()
RETURNS TIMESTAMPTZ
LANGUAGE SQL
STABLE
AS $$
  SELECT now() AT TIME ZONE 'America/Vancouver';
$$;

-- 创建一个函数来格式化温哥华时间
CREATE OR REPLACE FUNCTION format_vancouver_time(input_time TIMESTAMPTZ DEFAULT now())
RETURNS TEXT
LANGUAGE SQL
STABLE
AS $$
  SELECT to_char(input_time AT TIME ZONE 'America/Vancouver', 'YYYY-MM-DD HH24:MI:SS TZ');
$$;

-- 测试函数
SELECT 
  get_vancouver_time() as vancouver_now,
  format_vancouver_time() as formatted_vancouver_time;

-- 可选：为现有的 created_at 和 updated_at 字段创建触发器
-- 确保它们使用温哥华时区

-- 更新 users 表的时间戳字段默认值
ALTER TABLE users 
ALTER COLUMN created_at SET DEFAULT (now() AT TIME ZONE 'America/Vancouver'),
ALTER COLUMN updated_at SET DEFAULT (now() AT TIME ZONE 'America/Vancouver');

-- 为其他表也设置时区（如果需要）
-- ALTER TABLE units ALTER COLUMN created_at SET DEFAULT (now() AT TIME ZONE 'America/Vancouver');
-- ALTER TABLE topics ALTER COLUMN created_at SET DEFAULT (now() AT TIME ZONE 'America/Vancouver');

-- 显示设置完成信息
SELECT 
  'Timezone setup completed for Vancouver (America/Vancouver)' as message,
  current_setting('timezone') as database_timezone,
  now() AT TIME ZONE 'America/Vancouver' as current_vancouver_time; 