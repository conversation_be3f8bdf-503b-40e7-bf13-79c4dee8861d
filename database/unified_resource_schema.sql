-- 统一资源管理系统数据库架构
-- 支持分门别类和用户上传资源

-- 1. 资源分类层次表 (支持 unit/lesson 层次结构)
CREATE TABLE IF NOT EXISTS resource_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL, -- 如: "unit-1", "unit-1-lesson-2"
    parent_code VARCHAR(50), -- 父级分类，如 lesson 的父级是 unit
    level INTEGER NOT NULL, -- 1=Unit, 2=Lesson, 3=Sub-lesson
    title VARCHAR(200) NOT NULL,
    description TEXT,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 外键约束
    FOREIGN KEY (parent_code) REFERENCES resource_categories(code) ON DELETE CASCADE
);

-- 2. 统一资源表
CREATE TABLE IF NOT EXISTS unified_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 基本信息
    title VARCHAR(300) NOT NULL,
    description TEXT,
    content TEXT, -- 资源内容或说明
    
    -- 分类信息
    category_code VARCHAR(50) NOT NULL, -- 对应 resource_categories.code
    unit_code VARCHAR(50) NOT NULL, -- 如: "unit-1"
    lesson_code VARCHAR(50), -- 如: "unit-1-lesson-2" (可选)
    
    -- 资源类型和格式
    resource_type VARCHAR(50) NOT NULL, -- 'video', 'article', 'document', 'exercise', 'quiz', 'interactive', 'image', 'audio'
    format VARCHAR(50), -- 'pdf', 'docx', 'html', 'markdown', 'mp4', 'youtube', 'url', 'interactive'
    file_extension VARCHAR(10), -- 文件扩展名
    
    -- 文件和链接信息
    file_path VARCHAR(500), -- 本地文件路径
    external_url TEXT, -- 外部链接
    thumbnail_url TEXT, -- 缩略图
    file_size BIGINT, -- 文件大小(字节)
    
    -- 元数据
    metadata JSONB DEFAULT '{}', -- 扩展元数据
    tags TEXT[], -- 标签数组
    keywords TEXT[], -- 关键词数组
    
    -- 难度和时长
    difficulty_level VARCHAR(20) DEFAULT 'beginner', -- 'beginner', 'intermediate', 'advanced'
    estimated_time_minutes INTEGER, -- 预计学习时间(分钟)
    
    -- 来源和作者信息
    source_type VARCHAR(50) NOT NULL, -- 'internal', 'external', 'user_upload', 'generated'
    source_platform VARCHAR(100), -- 'youtube', 'oracle', 'geeksforgeeks', 'custom'
    author_name VARCHAR(200),
    author_email VARCHAR(200),
    created_by_user_id UUID, -- 创建用户ID
    
    -- 质量和验证
    quality_score FLOAT DEFAULT 0.0 CHECK (quality_score BETWEEN 0.0 AND 100.0),
    is_verified BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT true,
    admin_notes TEXT,
    
    -- 统计信息
    view_count INTEGER DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    rating_average FLOAT DEFAULT 0.0,
    rating_count INTEGER DEFAULT 0,
    
    -- 排序和状态
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    
    -- 外键约束
    FOREIGN KEY (category_code) REFERENCES resource_categories(code) ON DELETE CASCADE
);

-- 3. 资源评分表
CREATE TABLE IF NOT EXISTS resource_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_id UUID REFERENCES unified_resources(id) ON DELETE CASCADE,
    user_id UUID, -- 评分用户ID
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    review_text TEXT,
    is_helpful BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(resource_id, user_id)
);

-- 4. 资源访问日志表
CREATE TABLE IF NOT EXISTS resource_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_id UUID REFERENCES unified_resources(id) ON DELETE CASCADE,
    user_id UUID,
    access_type VARCHAR(50) DEFAULT 'view', -- 'view', 'download', 'like', 'share'
    user_agent TEXT,
    ip_address INET,
    session_id VARCHAR(255),
    duration_seconds INTEGER, -- 访问时长
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 资源收藏表
CREATE TABLE IF NOT EXISTS resource_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_id UUID REFERENCES unified_resources(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(resource_id, user_id)
);

-- 6. 资源标签表 (独立管理)
CREATE TABLE IF NOT EXISTS resource_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tag_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7), -- hex颜色代码
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_unified_resources_category ON unified_resources(category_code);
CREATE INDEX IF NOT EXISTS idx_unified_resources_unit ON unified_resources(unit_code);
CREATE INDEX IF NOT EXISTS idx_unified_resources_lesson ON unified_resources(lesson_code);
CREATE INDEX IF NOT EXISTS idx_unified_resources_type ON unified_resources(resource_type);
CREATE INDEX IF NOT EXISTS idx_unified_resources_source ON unified_resources(source_type);
CREATE INDEX IF NOT EXISTS idx_unified_resources_difficulty ON unified_resources(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_unified_resources_tags ON unified_resources USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_unified_resources_keywords ON unified_resources USING GIN(keywords);
CREATE INDEX IF NOT EXISTS idx_unified_resources_active ON unified_resources(is_active, is_public);
CREATE INDEX IF NOT EXISTS idx_unified_resources_order ON unified_resources(category_code, order_index);

CREATE INDEX IF NOT EXISTS idx_resource_categories_code ON resource_categories(code);
CREATE INDEX IF NOT EXISTS idx_resource_categories_parent ON resource_categories(parent_code);
CREATE INDEX IF NOT EXISTS idx_resource_categories_level ON resource_categories(level);

CREATE INDEX IF NOT EXISTS idx_resource_ratings_resource ON resource_ratings(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_access_logs_resource ON resource_access_logs(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_access_logs_date ON resource_access_logs(created_at);

-- 8. 插入基础分类数据
INSERT INTO resource_categories (code, parent_code, level, title, description, order_index) VALUES
-- Units (Level 1)
('unit-1', NULL, 1, 'Unit 1: Primitive Types', 'Java primitive data types and variables', 1),
('unit-2', NULL, 1, 'Unit 2: Using Objects', 'Creating and manipulating objects in Java', 2),
('unit-3', NULL, 1, 'Unit 3: Boolean Expressions and if Statements', 'Conditional logic and boolean expressions', 3),
('unit-4', NULL, 1, 'Unit 4: Iteration', 'Loops and iterative programming', 4),
('unit-5', NULL, 1, 'Unit 5: Writing Classes', 'Class design and encapsulation', 5),
('unit-6', NULL, 1, 'Unit 6: Array', 'One-dimensional arrays and algorithms', 6),
('unit-7', NULL, 1, 'Unit 7: ArrayList', 'Dynamic arrays and collections', 7),
('unit-8', NULL, 1, 'Unit 8: 2D Array', 'Two-dimensional arrays and matrices', 8),
('unit-9', NULL, 1, 'Unit 9: Inheritance', 'Inheritance and polymorphism', 9),
('unit-10', NULL, 1, 'Unit 10: Recursion', 'Recursive algorithms and thinking', 10),

-- Unit 1 Lessons (Level 2)
('unit-1-lesson-1', 'unit-1', 2, 'Lesson 1.1: Why Programming? Why Java?', 'Introduction to programming and Java', 1),
('unit-1-lesson-2', 'unit-1', 2, 'Lesson 1.2: Variables and Data Types', 'Primitive data types and variable declaration', 2),
('unit-1-lesson-3', 'unit-1', 2, 'Lesson 1.3: Expressions and Assignment Statements', 'Working with expressions and operators', 3),

-- Unit 2 Lessons (Level 2)
('unit-2-lesson-1', 'unit-2', 2, 'Lesson 2.1: Objects - Instances of Classes', 'Understanding objects and classes', 1),
('unit-2-lesson-2', 'unit-2', 2, 'Lesson 2.2: Creating and Storing Objects', 'Object instantiation and references', 2),
('unit-2-lesson-3', 'unit-2', 2, 'Lesson 2.3: Calling Methods', 'Method calls and return values', 3),

-- Unit 3 Lessons (Level 2)
('unit-3-lesson-1', 'unit-3', 2, 'Lesson 3.1: Boolean Expressions', 'Boolean values and expressions', 1),
('unit-3-lesson-2', 'unit-3', 2, 'Lesson 3.2: if Statements', 'Conditional statements and control flow', 2),
('unit-3-lesson-3', 'unit-3', 2, 'Lesson 3.3: if-else and else-if Statements', 'Complex conditional logic', 3),

-- Unit 4 Lessons (Level 2)
('unit-4-lesson-1', 'unit-4', 2, 'Lesson 4.1: while Loops', 'While loop fundamentals', 1),
('unit-4-lesson-2', 'unit-4', 2, 'Lesson 4.2: for Loops', 'For loop structure and usage', 2),
('unit-4-lesson-3', 'unit-4', 2, 'Lesson 4.3: Developing Algorithms Using Strings', 'String processing with loops', 3),

-- Unit 5 Lessons (Level 2)
('unit-5-lesson-1', 'unit-5', 2, 'Lesson 5.1: Anatomy of a Class', 'Class structure and components', 1),
('unit-5-lesson-2', 'unit-5', 2, 'Lesson 5.2: Constructors', 'Constructor methods and initialization', 2),
('unit-5-lesson-3', 'unit-5', 2, 'Lesson 5.3: Documentation with Comments', 'Code documentation and JavaDoc', 3)

ON CONFLICT (code) DO NOTHING;

-- 9. 插入基础标签数据
INSERT INTO resource_tags (tag_name, description, color) VALUES
('beginner', 'Suitable for beginners', '#4CAF50'),
('intermediate', 'Intermediate level content', '#FF9800'),
('advanced', 'Advanced level content', '#F44336'),
('video', 'Video content', '#2196F3'),
('tutorial', 'Step-by-step tutorial', '#9C27B0'),
('exercise', 'Practice exercise', '#00BCD4'),
('reference', 'Reference material', '#607D8B'),
('official', 'Official documentation', '#795548'),
('interactive', 'Interactive content', '#E91E63'),
('quiz', 'Quiz or assessment', '#FF5722')

ON CONFLICT (tag_name) DO NOTHING;

-- 10. 验证数据插入
DO $$
DECLARE
    categories_count INTEGER;
    tags_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO categories_count FROM resource_categories;
    SELECT COUNT(*) INTO tags_count FROM resource_tags;
    
    RAISE NOTICE '=== 统一资源系统初始化完成 ===';
    RAISE NOTICE '资源分类数量: %', categories_count;
    RAISE NOTICE '基础标签数量: %', tags_count;
    RAISE NOTICE '数据库表已创建:';
    RAISE NOTICE '  - resource_categories (资源分类)';
    RAISE NOTICE '  - unified_resources (统一资源)';
    RAISE NOTICE '  - resource_ratings (资源评分)';
    RAISE NOTICE '  - resource_access_logs (访问日志)';
    RAISE NOTICE '  - resource_favorites (收藏夹)';
    RAISE NOTICE '  - resource_tags (标签管理)';
    RAISE NOTICE '所有索引已创建完成';
    RAISE NOTICE '================================';
END $$; 