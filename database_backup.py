#!/usr/bin/env python3
"""
APCSA数据库完整备份工具
下载所有表的数据并保存为JSON文件
"""

import sys
import os
import json
from datetime import datetime
from pathlib import Path

# 添加backend路径
sys.path.append('backend')
from backend.database import init_supabase

def create_backup_directory():
    """创建备份目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path(f"database_backup_{timestamp}")
    backup_dir.mkdir(exist_ok=True)
    return backup_dir

def get_all_tables(supabase):
    """获取所有可用的表"""
    # 已知的表列表
    known_tables = [
        'units', 'topics', 'exercises', 'learning_resources',
        'chatbot_conversations', 'learning_progress', 'learning_content',
        'video_content', 'note_content', 'question_content',
        'ai_generated_content', 'unified_contents'
    ]
    
    available_tables = []
    table_info = {}
    
    print("🔍 检查可用的数据库表...")
    
    for table in known_tables:
        try:
            result = supabase.table(table).select('*', count='exact').limit(1).execute()
            available_tables.append(table)
            table_info[table] = {
                'count': result.count,
                'exists': True
            }
            print(f"✅ {table}: {result.count} 条记录")
        except Exception as e:
            table_info[table] = {
                'exists': False,
                'error': str(e)
            }
            print(f"❌ {table}: 表不存在")
    
    return available_tables, table_info

def backup_table(supabase, table_name, backup_dir):
    """备份单个表的所有数据"""
    print(f"\n📥 备份表: {table_name}")
    
    try:
        # 分批获取数据，避免内存问题
        all_data = []
        offset = 0
        batch_size = 1000
        
        while True:
            result = supabase.table(table_name).select('*').range(offset, offset + batch_size - 1).execute()
            
            if not result.data:
                break
                
            all_data.extend(result.data)
            offset += batch_size
            print(f"   已获取 {len(all_data)} 条记录...")
            
            # 如果返回的数据少于batch_size，说明已经到最后了
            if len(result.data) < batch_size:
                break
        
        # 保存到JSON文件
        backup_file = backup_dir / f"{table_name}.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump({
                'table_name': table_name,
                'backup_time': datetime.now().isoformat(),
                'record_count': len(all_data),
                'data': all_data
            }, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ {table_name}: {len(all_data)} 条记录已备份到 {backup_file}")
        return len(all_data)
        
    except Exception as e:
        print(f"❌ 备份 {table_name} 失败: {e}")
        return 0

def create_backup_summary(backup_dir, table_info, backup_results):
    """创建备份摘要文件"""
    summary = {
        'backup_time': datetime.now().isoformat(),
        'backup_directory': str(backup_dir),
        'total_tables_checked': len(table_info),
        'available_tables': len([t for t in table_info.values() if t.get('exists')]),
        'total_records_backed_up': sum(backup_results.values()),
        'table_details': table_info,
        'backup_results': backup_results
    }
    
    summary_file = backup_dir / "backup_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
    
    # 创建可读的摘要文件
    readme_file = backup_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(f"# APCSA数据库备份\n\n")
        f.write(f"**备份时间**: {summary['backup_time']}\n\n")
        f.write(f"## 备份统计\n\n")
        f.write(f"- 检查的表数量: {summary['total_tables_checked']}\n")
        f.write(f"- 可用的表数量: {summary['available_tables']}\n")
        f.write(f"- 备份的记录总数: {summary['total_records_backed_up']}\n\n")
        f.write(f"## 表详情\n\n")
        
        for table, info in table_info.items():
            if info.get('exists'):
                count = backup_results.get(table, 0)
                f.write(f"- **{table}**: {count} 条记录\n")
            else:
                f.write(f"- **{table}**: 表不存在\n")
        
        f.write(f"\n## 文件说明\n\n")
        f.write(f"- `backup_summary.json`: 详细的备份信息\n")
        f.write(f"- `[table_name].json`: 各表的数据备份\n")
        f.write(f"- `README.md`: 本说明文件\n")

def main():
    """主函数"""
    print("🚀 APCSA数据库完整备份工具")
    print("=" * 50)
    
    try:
        # 1. 初始化数据库连接
        print("🔗 连接数据库...")
        supabase = init_supabase()
        print("✅ 数据库连接成功")
        
        # 2. 创建备份目录
        backup_dir = create_backup_directory()
        print(f"📁 备份目录: {backup_dir}")
        
        # 3. 获取所有可用表
        available_tables, table_info = get_all_tables(supabase)
        
        if not available_tables:
            print("❌ 没有找到可备份的表")
            return
        
        print(f"\n📋 找到 {len(available_tables)} 个可备份的表")
        
        # 4. 备份每个表
        backup_results = {}
        total_records = 0
        
        for table in available_tables:
            record_count = backup_table(supabase, table, backup_dir)
            backup_results[table] = record_count
            total_records += record_count
        
        # 5. 创建备份摘要
        create_backup_summary(backup_dir, table_info, backup_results)
        
        # 6. 输出结果
        print("\n" + "=" * 50)
        print("🎉 数据库备份完成!")
        print(f"📁 备份目录: {backup_dir}")
        print(f"📊 备份统计:")
        print(f"   - 备份表数量: {len(available_tables)}")
        print(f"   - 总记录数: {total_records}")
        print(f"   - 备份文件: {len(available_tables) + 2} 个")
        
        print(f"\n📋 各表备份详情:")
        for table, count in backup_results.items():
            print(f"   - {table}: {count} 条记录")
        
        print(f"\n💡 使用说明:")
        print(f"   - 查看备份摘要: cat {backup_dir}/README.md")
        print(f"   - 查看具体数据: cat {backup_dir}/[table_name].json")
        
    except Exception as e:
        print(f"❌ 备份过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 