#!/usr/bin/env python3
"""
APCSA数据库恢复工具
从备份文件恢复数据到数据库
"""

import sys
import os
import json
from datetime import datetime
from pathlib import Path
import argparse

# 添加backend路径
sys.path.append('backend')
from backend.database import init_supabase

def load_backup_summary(backup_dir):
    """加载备份摘要信息"""
    summary_file = Path(backup_dir) / "backup_summary.json"
    
    if not summary_file.exists():
        raise FileNotFoundError(f"备份摘要文件不存在: {summary_file}")
    
    with open(summary_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def restore_table(supabase, table_name, backup_dir, mode='insert'):
    """恢复单个表的数据"""
    print(f"\n📤 恢复表: {table_name}")
    
    backup_file = Path(backup_dir) / f"{table_name}.json"
    
    if not backup_file.exists():
        print(f"❌ 备份文件不存在: {backup_file}")
        return 0
    
    try:
        # 加载备份数据
        with open(backup_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        data = backup_data.get('data', [])
        
        if not data:
            print(f"⏭️ {table_name}: 没有数据需要恢复")
            return 0
        
        print(f"   准备恢复 {len(data)} 条记录...")
        
        # 根据模式处理数据
        if mode == 'truncate':
            # 清空表后插入（需要谨慎使用）
            print(f"   ⚠️ 清空表 {table_name} 的所有数据...")
            # 注意：Supabase不支持直接TRUNCATE，需要删除所有记录
            existing_data = supabase.table(table_name).select('id').execute()
            if existing_data.data:
                ids = [row['id'] for row in existing_data.data]
                for record_id in ids:
                    supabase.table(table_name).delete().eq('id', record_id).execute()
                print(f"   已删除 {len(ids)} 条现有记录")
        
        # 批量插入数据
        batch_size = 100
        inserted_count = 0
        failed_count = 0
        
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            
            try:
                if mode == 'upsert':
                    # 使用upsert模式（更新或插入）
                    result = supabase.table(table_name).upsert(batch).execute()
                else:
                    # 使用insert模式
                    result = supabase.table(table_name).insert(batch).execute()
                
                if result.data:
                    inserted_count += len(result.data)
                    print(f"   已恢复 {inserted_count}/{len(data)} 条记录...")
                
            except Exception as e:
                failed_count += len(batch)
                print(f"   ❌ 批次恢复失败: {str(e)[:50]}...")
        
        print(f"✅ {table_name}: 成功恢复 {inserted_count} 条记录，失败 {failed_count} 条")
        return inserted_count
        
    except Exception as e:
        print(f"❌ 恢复 {table_name} 失败: {e}")
        return 0

def verify_restore(supabase, backup_summary):
    """验证恢复结果"""
    print("\n🔍 验证恢复结果...")
    
    backup_results = backup_summary.get('backup_results', {})
    verification_results = {}
    
    for table_name, expected_count in backup_results.items():
        try:
            result = supabase.table(table_name).select('*', count='exact').execute()
            actual_count = result.count
            verification_results[table_name] = {
                'expected': expected_count,
                'actual': actual_count,
                'match': expected_count == actual_count
            }
            
            status = "✅" if expected_count == actual_count else "⚠️"
            print(f"   {status} {table_name}: 期望 {expected_count}, 实际 {actual_count}")
            
        except Exception as e:
            verification_results[table_name] = {
                'expected': expected_count,
                'actual': 0,
                'match': False,
                'error': str(e)
            }
            print(f"   ❌ {table_name}: 验证失败 - {str(e)[:50]}...")
    
    return verification_results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='APCSA数据库恢复工具')
    parser.add_argument('backup_dir', help='备份目录路径')
    parser.add_argument('--mode', choices=['insert', 'upsert', 'truncate'], 
                       default='insert', help='恢复模式')
    parser.add_argument('--tables', nargs='+', help='指定要恢复的表（默认恢复所有表）')
    parser.add_argument('--dry-run', action='store_true', help='仅显示恢复计划，不执行实际恢复')
    
    args = parser.parse_args()
    
    print("🔄 APCSA数据库恢复工具")
    print("=" * 50)
    
    try:
        # 1. 检查备份目录
        backup_dir = Path(args.backup_dir)
        if not backup_dir.exists():
            print(f"❌ 备份目录不存在: {backup_dir}")
            return
        
        print(f"📁 备份目录: {backup_dir}")
        
        # 2. 加载备份摘要
        backup_summary = load_backup_summary(backup_dir)
        print(f"📋 备份时间: {backup_summary['backup_time']}")
        print(f"📊 备份记录总数: {backup_summary['total_records_backed_up']}")
        
        # 3. 确定要恢复的表
        available_tables = list(backup_summary['backup_results'].keys())
        tables_to_restore = args.tables if args.tables else available_tables
        
        # 过滤掉不存在的表
        tables_to_restore = [t for t in tables_to_restore if t in available_tables]
        
        if not tables_to_restore:
            print("❌ 没有找到可恢复的表")
            return
        
        print(f"\n📋 计划恢复 {len(tables_to_restore)} 个表:")
        for table in tables_to_restore:
            count = backup_summary['backup_results'][table]
            print(f"   - {table}: {count} 条记录")
        
        print(f"\n🔧 恢复模式: {args.mode}")
        if args.mode == 'truncate':
            print("   ⚠️ 警告: truncate模式将删除表中的所有现有数据!")
        
        # 4. 干运行模式
        if args.dry_run:
            print("\n🔍 干运行模式 - 不会执行实际恢复")
            print("✅ 恢复计划验证完成")
            return
        
        # 5. 确认恢复
        if args.mode == 'truncate':
            confirm = input("\n⚠️ 确认要清空表并恢复数据吗? (输入 'YES' 确认): ")
            if confirm != 'YES':
                print("❌ 恢复已取消")
                return
        
        # 6. 连接数据库
        print("\n🔗 连接数据库...")
        supabase = init_supabase()
        print("✅ 数据库连接成功")
        
        # 7. 执行恢复
        restore_results = {}
        total_restored = 0
        
        for table in tables_to_restore:
            restored_count = restore_table(supabase, table, backup_dir, args.mode)
            restore_results[table] = restored_count
            total_restored += restored_count
        
        # 8. 验证恢复结果
        verification_results = verify_restore(supabase, backup_summary)
        
        # 9. 输出结果
        print("\n" + "=" * 50)
        print("🎉 数据库恢复完成!")
        print(f"📊 恢复统计:")
        print(f"   - 恢复表数量: {len(tables_to_restore)}")
        print(f"   - 总恢复记录数: {total_restored}")
        
        print(f"\n📋 各表恢复详情:")
        for table, count in restore_results.items():
            verification = verification_results.get(table, {})
            status = "✅" if verification.get('match', False) else "⚠️"
            print(f"   {status} {table}: 恢复 {count} 条记录")
        
        # 检查是否有验证失败的表
        failed_tables = [t for t, v in verification_results.items() if not v.get('match', False)]
        if failed_tables:
            print(f"\n⚠️ 以下表的恢复结果与备份不匹配:")
            for table in failed_tables:
                v = verification_results[table]
                print(f"   - {table}: 期望 {v['expected']}, 实际 {v['actual']}")
        
    except Exception as e:
        print(f"❌ 恢复过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 