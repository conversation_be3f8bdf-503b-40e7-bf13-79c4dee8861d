#!/usr/bin/env python3
"""
部署数据库Schema
"""
import sys
import os
sys.path.append('backend')
from backend.database import init_supabase

def deploy_unified_schema():
    """部署统一学习内容schema"""
    print('🚀 开始部署统一学习内容数据库schema...')
    
    try:
        # 读取schema文件
        schema_file = 'database/unified_learning_content_schema.sql'
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # 拆分成单独的SQL语句
        statements = []
        current_statement = ""
        
        for line in schema_sql.split('\n'):
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('--') or line.startswith('/*'):
                continue
            
            # 处理多行注释结束
            if '*/' in line:
                continue
                
            current_statement += line + "\n"
            
            # 如果遇到分号，表示一个语句结束
            if line.endswith(';'):
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
        
        # 添加最后一个语句（如果有的话）
        if current_statement.strip():
            statements.append(current_statement.strip())
        
        print(f'📜 解析出 {len(statements)} 条SQL语句')
        
        # 连接数据库
        supabase = init_supabase()
        
        # 执行每条语句
        success_count = 0
        for i, statement in enumerate(statements):
            try:
                if statement.strip():
                    # 使用rpc方式执行原始SQL
                    result = supabase.rpc('exec_sql', {'sql': statement}).execute()
                    success_count += 1
                    print(f'✅ 语句 {i+1}/{len(statements)} 执行成功')
            except Exception as e:
                print(f'❌ 语句 {i+1} 执行失败: {str(e)}')
                print(f'   SQL: {statement[:100]}...')
        
        print(f'\n📊 部署结果: {success_count}/{len(statements)} 条语句成功执行')
        
        if success_count == len(statements):
            print('🎉 Schema部署完成!')
            return True
        else:
            print('⚠️  部分语句执行失败，但可能是正常的（表已存在等）')
            return True
            
    except Exception as e:
        print(f'❌ Schema部署失败: {e}')
        return False

def test_tables_after_deployment():
    """部署后测试表是否创建成功"""
    print('\n🔍 检查表创建结果...')
    
    try:
        supabase = init_supabase()
        
        # 检查各个表的记录数
        tables = ['learning_content', 'video_content', 'note_content', 'question_content', 'learning_progress']
        for table in tables:
            try:
                result = supabase.table(table).select('count', count='exact').execute()
                print(f'✅ {table}: 表已创建，当前 {result.count} 条记录')
            except Exception as e:
                print(f'❌ {table}: {str(e)}')
        
        return True
    except Exception as e:
        print(f'❌ 表检查失败: {e}')
        return False

def main():
    """主函数"""
    print('🗄️  统一学习内容数据库Schema部署工具')
    print('=' * 50)
    
    # 1. 部署schema
    deploy_ok = deploy_unified_schema()
    if not deploy_ok:
        print('❌ Schema部署失败')
        return
    
    # 2. 测试表创建结果
    test_tables_after_deployment()

if __name__ == '__main__':
    main() 