#!/usr/bin/env python3
"""
在Docker环境中部署数据库Schema
"""
import sys
sys.path.append('/app')
from database import init_supabase

def deploy_tables_manually():
    """手动创建所需的表"""
    print('🚀 开始在Docker环境中部署学习内容表...')
    
    supabase = init_supabase()
    
    # 1. 创建主要学习内容表
    learning_content_sql = """
    CREATE TABLE IF NOT EXISTS learning_content (
        id SERIAL PRIMARY KEY,
        unit_id VARCHAR(50) NOT NULL,
        topic_id UUID,
        lesson_code VARCHAR(50),
        title VARCHAR(500) NOT NULL,
        description TEXT,
        content_type VARCHAR(50) NOT NULL,
        format VARCHAR(50),
        content_data JSONB NOT NULL,
        source_platform VARCHAR(100),
        source_url TEXT,
        external_id VARCHAR(200),
        channel_name VARCHAR(200),
        author_name VARCHA<PERSON>(200),
        difficulty_level VARCHAR(20) DEFAULT 'beginner',
        estimated_duration INTEGER,
        learning_objectives TEXT[],
        prerequisites TEXT[],
        tags TEXT[],
        keywords TEXT[],
        quality_score INTEGER DEFAULT 0,
        is_verified BOOLEAN DEFAULT FALSE,
        is_featured BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        view_count INTEGER DEFAULT 0,
        completion_count INTEGER DEFAULT 0,
        average_rating DECIMAL(3,2) DEFAULT 0.00,
        created_by VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_scraped_at TIMESTAMP WITH TIME ZONE,
        CONSTRAINT unique_external_content UNIQUE(source_platform, external_id)
    );
    """
    
    # 2. 创建视频内容表
    video_content_sql = """
    CREATE TABLE IF NOT EXISTS video_content (
        id SERIAL PRIMARY KEY,
        learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
        video_url TEXT NOT NULL,
        embed_code TEXT,
        thumbnail_url TEXT,
        duration_seconds INTEGER,
        video_quality VARCHAR(20),
        channel_id VARCHAR(200),
        channel_name VARCHAR(200),
        channel_url TEXT,
        view_count_external INTEGER DEFAULT 0,
        like_count INTEGER DEFAULT 0,
        upload_date TIMESTAMP WITH TIME ZONE,
        has_subtitles BOOLEAN DEFAULT FALSE,
        subtitle_languages TEXT[],
        transcript TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    # 3. 创建笔记内容表
    note_content_sql = """
    CREATE TABLE IF NOT EXISTS note_content (
        id SERIAL PRIMARY KEY,
        learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
        content_markdown TEXT NOT NULL,
        content_html TEXT,
        sections JSONB,
        code_examples JSONB,
        diagrams JSONB,
        version INTEGER DEFAULT 1,
        parent_version_id INTEGER REFERENCES note_content(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    # 4. 创建问题内容表
    question_content_sql = """
    CREATE TABLE IF NOT EXISTS question_content (
        id SERIAL PRIMARY KEY,
        learning_content_id INTEGER NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
        question_type VARCHAR(50) NOT NULL,
        question_text TEXT NOT NULL,
        question_data JSONB NOT NULL,
        starter_code TEXT,
        solution_code TEXT,
        test_cases JSONB,
        points INTEGER DEFAULT 1,
        time_limit_minutes INTEGER,
        explanation TEXT,
        hints TEXT[],
        common_mistakes JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    # 5. 创建学习进度表
    learning_progress_sql = """
    CREATE TABLE IF NOT EXISTS learning_progress (
        id SERIAL PRIMARY KEY,
        student_id UUID NOT NULL,
        learning_content_id INTEGER NOT NULL REFERENCES learning_content(id),
        status VARCHAR(20) DEFAULT 'not_started',
        progress_percentage INTEGER DEFAULT 0,
        time_spent_minutes INTEGER DEFAULT 0,
        started_at TIMESTAMP WITH TIME ZONE,
        completed_at TIMESTAMP WITH TIME ZONE,
        last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        score INTEGER,
        attempts INTEGER DEFAULT 0,
        best_score INTEGER DEFAULT 0,
        notes TEXT,
        bookmarked BOOLEAN DEFAULT FALSE,
        rating INTEGER,
        feedback TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        CONSTRAINT unique_student_content UNIQUE(student_id, learning_content_id)
    );
    """
    
    tables = [
        ("learning_content", learning_content_sql),
        ("video_content", video_content_sql),
        ("note_content", note_content_sql),
        ("question_content", question_content_sql),
        ("learning_progress", learning_progress_sql)
    ]
    
    success_count = 0
    for table_name, sql in tables:
        try:
            print(f"📝 创建表: {table_name}")
            result = supabase.rpc('exec_sql', {'sql': sql}).execute()
            print(f"✅ {table_name} 创建成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {table_name} 创建失败: {str(e)}")
    
    print(f"\n📊 结果: {success_count}/{len(tables)} 个表创建成功")
    return success_count == len(tables)

def test_tables():
    """测试表是否创建成功"""
    print('\n🔍 测试表创建结果...')
    
    supabase = init_supabase()
    tables = ['learning_content', 'video_content', 'note_content', 'question_content', 'learning_progress']
    
    for table in tables:
        try:
            result = supabase.table(table).select('count', count='exact').execute()
            print(f'✅ {table}: 表已创建，当前 {result.count} 条记录')
        except Exception as e:
            print(f'❌ {table}: {str(e)}')

def main():
    """主函数"""
    print('🗄️  Docker环境数据库表部署')
    print('=' * 50)
    
    # 1. 部署表
    deploy_ok = deploy_tables_manually()
    
    # 2. 测试表
    test_tables()
    
    if deploy_ok:
        print('\n🎉 数据库表部署完成！')
    else:
        print('\n⚠️  部分表部署失败，请检查错误信息')

if __name__ == '__main__':
    main() 