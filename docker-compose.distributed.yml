version: '3.8'

services:
  # ==========================================
  # Load Balancer
  # ==========================================
  nginx:
    image: nginx:alpine
    container_name: apcsa-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway-1
      - api-gateway-2
      - api-gateway-3
    networks:
      - apcsa-network
    restart: unless-stopped

  # ==========================================
  # API Gateway Cluster
  # ==========================================
  api-gateway-1:
    build:
      context: ./api_gateway
      dockerfile: Dockerfile
    container_name: apcsa-api-1
    environment:
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=********************************************/apcsa
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - INSTANCE_ID=api-1
    depends_on:
      - postgres
      - redis-master
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-gateway-2:
    build:
      context: ./api_gateway
      dockerfile: Dockerfile
    container_name: apcsa-api-2
    environment:
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=********************************************/apcsa
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - INSTANCE_ID=api-2
    depends_on:
      - postgres
      - redis-master
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-gateway-3:
    build:
      context: ./api_gateway
      dockerfile: Dockerfile
    container_name: apcsa-api-3
    environment:
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=********************************************/apcsa
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - INSTANCE_ID=api-3
    depends_on:
      - postgres
      - redis-master
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # Redis Cluster
  # ==========================================
  redis-master:
    image: redis:7-alpine
    container_name: apcsa-redis-master
    command: redis-server --appendonly yes --replica-read-only no
    ports:
      - "6379:6379"
    volumes:
      - redis-master-data:/data
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-slave-1:
    image: redis:7-alpine
    container_name: apcsa-redis-slave-1
    command: redis-server --slaveof redis-master 6379 --appendonly yes
    depends_on:
      - redis-master
    volumes:
      - redis-slave-1-data:/data
    networks:
      - apcsa-network
    restart: unless-stopped

  redis-slave-2:
    image: redis:7-alpine
    container_name: apcsa-redis-slave-2
    command: redis-server --slaveof redis-master 6379 --appendonly yes
    depends_on:
      - redis-master
    volumes:
      - redis-slave-2-data:/data
    networks:
      - apcsa-network
    restart: unless-stopped

  # ==========================================
  # Celery Cluster
  # ==========================================
  celery-beat:
    build:
      context: ./task_queue
      dockerfile: Dockerfile
    container_name: apcsa-celery-beat
    command: celery -A celery_app beat --loglevel=info
    environment:
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - CELERY_RESULT_BACKEND=redis://redis-master:6379/1
      - DATABASE_URL=********************************************/apcsa
    depends_on:
      - redis-master
      - postgres
    volumes:
      - ./task_queue:/app
    networks:
      - apcsa-network
    restart: unless-stopped

  celery-worker-1:
    build:
      context: ./task_queue
      dockerfile: Dockerfile
    container_name: apcsa-worker-1
    command: celery -A celery_app worker --loglevel=info --queues=scraping,monitoring --concurrency=4
    environment:
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - CELERY_RESULT_BACKEND=redis://redis-master:6379/1
      - DATABASE_URL=********************************************/apcsa
      - WORKER_ID=worker-1
    depends_on:
      - redis-master
      - postgres
      - celery-beat
    volumes:
      - ./task_queue:/app
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3

  celery-worker-2:
    build:
      context: ./task_queue
      dockerfile: Dockerfile
    container_name: apcsa-worker-2
    command: celery -A celery_app worker --loglevel=info --queues=scraping,cleanup --concurrency=4
    environment:
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - CELERY_RESULT_BACKEND=redis://redis-master:6379/1
      - DATABASE_URL=********************************************/apcsa
      - WORKER_ID=worker-2
    depends_on:
      - redis-master
      - postgres
      - celery-beat
    volumes:
      - ./task_queue:/app
    networks:
      - apcsa-network
    restart: unless-stopped

  celery-worker-3:
    build:
      context: ./task_queue
      dockerfile: Dockerfile
    container_name: apcsa-worker-3
    command: celery -A celery_app worker --loglevel=info --queues=alerts,monitoring --concurrency=2
    environment:
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - CELERY_RESULT_BACKEND=redis://redis-master:6379/1
      - DATABASE_URL=********************************************/apcsa
      - WORKER_ID=worker-3
    depends_on:
      - redis-master
      - postgres
      - celery-beat
    volumes:
      - ./task_queue:/app
    networks:
      - apcsa-network
    restart: unless-stopped

  # ==========================================
  # Scrapy Cluster
  # ==========================================
  scrapyd-1:
    build:
      context: ./scrapy_cluster
      dockerfile: Dockerfile
    container_name: apcsa-scrapyd-1
    ports:
      - "6800:6800"
    environment:
      - SCRAPYD_NODE_ID=scrapyd-1
      - REDIS_URL=redis://redis-master:6379/2
      - DATABASE_URL=********************************************/apcsa
    depends_on:
      - redis-master
      - postgres
    volumes:
      - scrapyd-1-data:/var/lib/scrapyd
      - ./scrapy_cluster:/app
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6800/daemonstatus.json"]
      interval: 30s
      timeout: 10s
      retries: 3

  scrapyd-2:
    build:
      context: ./scrapy_cluster
      dockerfile: Dockerfile
    container_name: apcsa-scrapyd-2
    ports:
      - "6801:6800"
    environment:
      - SCRAPYD_NODE_ID=scrapyd-2
      - REDIS_URL=redis://redis-master:6379/2
      - DATABASE_URL=********************************************/apcsa
    depends_on:
      - redis-master
      - postgres
    volumes:
      - scrapyd-2-data:/var/lib/scrapyd
      - ./scrapy_cluster:/app
    networks:
      - apcsa-network
    restart: unless-stopped

  scrapyd-3:
    build:
      context: ./scrapy_cluster
      dockerfile: Dockerfile
    container_name: apcsa-scrapyd-3
    ports:
      - "6802:6800"
    environment:
      - SCRAPYD_NODE_ID=scrapyd-3
      - REDIS_URL=redis://redis-master:6379/2
      - DATABASE_URL=********************************************/apcsa
    depends_on:
      - redis-master
      - postgres
    volumes:
      - scrapyd-3-data:/var/lib/scrapyd
      - ./scrapy_cluster:/app
    networks:
      - apcsa-network
    restart: unless-stopped

  # ==========================================
  # Monitoring Stack
  # ==========================================
  prometheus:
    image: prom/prometheus:latest
    container_name: apcsa-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml
      - prometheus-data:/prometheus
    networks:
      - apcsa-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: apcsa-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - apcsa-network
    restart: unless-stopped

  alertmanager:
    image: prom/alertmanager:latest
    container_name: apcsa-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager-data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    networks:
      - apcsa-network
    restart: unless-stopped

  flower:
    build:
      context: ./task_queue
      dockerfile: Dockerfile
    container_name: apcsa-flower
    command: celery -A celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - CELERY_RESULT_BACKEND=redis://redis-master:6379/1
    depends_on:
      - redis-master
      - celery-beat
    networks:
      - apcsa-network
    restart: unless-stopped

  # ==========================================
  # Exporters for Monitoring
  # ==========================================
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: apcsa-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis-master:6379
    depends_on:
      - redis-master
    networks:
      - apcsa-network
    restart: unless-stopped

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: apcsa-postgres-exporter
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=********************************************/apcsa?sslmode=disable
    depends_on:
      - postgres
    networks:
      - apcsa-network
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:latest
    container_name: apcsa-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - apcsa-network
    restart: unless-stopped

  # ==========================================
  # Storage Services
  # ==========================================
  postgres:
    image: postgres:15-alpine
    container_name: apcsa-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=apcsa
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  mongodb:
    image: mongo:6.0
    container_name: apcsa-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=apcsa
    volumes:
      - mongodb-data:/data/db
      - ./database/mongo-init:/docker-entrypoint-initdb.d
    networks:
      - apcsa-network
    restart: unless-stopped

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: apcsa-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - apcsa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # Log Management
  # ==========================================
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: apcsa-logstash
    ports:
      - "5044:5044"
      - "9600:9600"
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config:/usr/share/logstash/config
    depends_on:
      - elasticsearch
    networks:
      - apcsa-network
    restart: unless-stopped

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: apcsa-filebeat
    user: root
    volumes:
      - ./monitoring/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - logstash
    networks:
      - apcsa-network
    restart: unless-stopped

  # ==========================================
  # Frontend (保留原有)
  # ==========================================
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: apcsa-frontend
    ports:
      - "3002:3000"
    environment:
      - REACT_APP_API_URL=http://localhost/api
      - REACT_APP_WS_URL=ws://localhost/ws
    depends_on:
      - nginx
    networks:
      - apcsa-network
    restart: unless-stopped

# ==========================================
# Networks
# ==========================================
networks:
  apcsa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ==========================================
# Volumes
# ==========================================
volumes:
  postgres-data:
    driver: local
  mongodb-data:
    driver: local
  elasticsearch-data:
    driver: local
  redis-master-data:
    driver: local
  redis-slave-1-data:
    driver: local
  redis-slave-2-data:
    driver: local
  scrapyd-1-data:
    driver: local
  scrapyd-2-data:
    driver: local
  scrapyd-3-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  alertmanager-data:
    driver: local 