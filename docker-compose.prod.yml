version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: apcsa_postgres_prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - backend_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: apcsa_redis_prod
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - backend_network

  # FastAPI 后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: apcsa_backend_prod
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - ENVIRONMENT=production
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend_network
      - frontend_network

  # React 前端（构建后的静态文件）
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: apcsa_frontend_prod
    restart: unless-stopped
    networks:
      - frontend_network

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: apcsa_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - static_files:/var/www/static
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - frontend_network

volumes:
  postgres_data:
  redis_data:
  static_files:

networks:
  backend_network:
    driver: bridge
  frontend_network:
    driver: bridge
