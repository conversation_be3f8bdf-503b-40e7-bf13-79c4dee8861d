services:
  frontend:
    build:
      context: ./frontend
      target: development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - TZ=America/Vancouver
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_SUPABASE_URL=https://gfbfgiboyqkhgktflanr.supabase.co
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmYmZnaWJveXFraGdrdGZsYW5yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NDcxODQsImV4cCI6MjA2NDEyMzE4NH0.us9DEKJPhTKEaws-NgyJr4MbYRWqHmaaZAYgq1MUT5Y
      - REACT_APP_TIMEZONE=America/Vancouver
    depends_on:
      - backend
    stdin_open: true
    tty: true

  backend:
    build:
      context: ./backend
      target: development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    environment:
      - TZ=America/Vancouver
      - DATABASE_URL=********************************************/apcsa_ai_platform
      - REDIS_URL=redis://redis:6379
      - NEXT_PUBLIC_SUPABASE_URL=https://gfbfgiboyqkhgktflanr.supabase.co
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmYmZnaWJveXFraGdrdGZsYW5yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NDcxODQsImV4cCI6MjA2NDEyMzE4NH0.us9DEKJPhTKEaws-NgyJr4MbYRWqHmaaZAYgq1MUT5Y
      - NODE_ENV=development
      - DEBUG=true
      - TIMEZONE=America/Vancouver
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production-2024
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: apcsa_ai_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      TZ: America/Vancouver
      PGTZ: America/Vancouver
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/1-schema.sql
      - ./database/seeds/apcsa_units.sql:/docker-entrypoint-initdb.d/2-seeds.sql
    command: postgres -c timezone='America/Vancouver'

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    environment:
      - TZ=America/Vancouver

volumes:
  postgres_data:
  redis_data: 