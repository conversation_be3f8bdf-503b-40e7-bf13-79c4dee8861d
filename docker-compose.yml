version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: apcsa_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-apcsa_platform}
      POSTGRES_USER: ${POSTGRES_USER:-apcsa_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-apcsa_password}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-apcsa_user} -d ${POSTGRES_DB:-apcsa_platform}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: apcsa_redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # FastAPI 后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: apcsa_backend
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-apcsa_user}:${POSTGRES_PASSWORD:-apcsa_password}@postgres:5432/${POSTGRES_DB:-apcsa_platform}
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./backend:/app
      - backend_cache:/app/.cache
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # React 前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: apcsa_frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - REACT_APP_API_URL=http://localhost:${BACKEND_PORT:-8000}
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - frontend_cache:/app/.cache
    depends_on:
      - backend
    stdin_open: true
    tty: true
    restart: unless-stopped

  # 数据库管理工具（开发环境）
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: apcsa_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:
  backend_cache:
  frontend_cache:

networks:
  default:
    name: apcsa_network
