# 🤝 贡献指南

感谢您对 APCSA AI 学习平台的贡献！我们欢迎各种形式的贡献，包括但不限于代码、文档、问题报告和功能建议。

## 🚀 快速开始

### 1. Fork 和克隆项目

```bash
# Fork 项目到您的账户，然后克隆
git clone https://github.com/your-username/apcsa-ai-platform.git
cd apcsa-ai-platform

# 添加上游仓库
git remote add upstream https://github.com/original-owner/apcsa-ai-platform.git
```

### 2. 设置开发环境

```bash
# 安装依赖
npm run install:all

# 配置环境变量
cp env.example .env
# 编辑 .env 文件，填入必要的配置

# 启动开发服务器
npm run dev
```

## 📝 贡献类型

### 🐛 Bug 报告
发现bug？请：
1. 检查是否已有相关 [Issues](https://github.com/your-username/apcsa-ai-platform/issues)
2. 使用 Bug 报告模板创建新 Issue
3. 提供详细的复现步骤和环境信息

### 💡 功能建议
有好的想法？请：
1. 检查是否已有相关 [Discussions](https://github.com/your-username/apcsa-ai-platform/discussions)
2. 在讨论区提出您的建议
3. 描述功能的使用场景和预期效果

### 📚 文档改进
文档可以更好？请：
1. 直接编辑 `docs/` 目录下的文档
2. 提交 Pull Request
3. 说明改进的内容和原因

### 🔧 代码贡献
想要贡献代码？请遵循以下流程：

## 🔄 贡献流程

### 1. 选择任务
- 查看 [Issues](https://github.com/your-username/apcsa-ai-platform/issues) 选择感兴趣的任务
- 寻找标有 `good first issue` 的新手友好任务
- 或者实现您自己的功能想法

### 2. 创建分支
```bash
# 确保主分支是最新的
git checkout main
git pull upstream main

# 创建新分支
git checkout -b feature/your-feature-name
# 或
git checkout -b fix/bug-description
```

### 3. 开发和测试
```bash
# 进行开发...

# 运行测试
npm run test

# 检查代码风格
npm run lint

# 构建项目
npm run build
```

### 4. 提交代码
```bash
# 添加改动
git add .

# 提交（使用清晰的提交信息）
git commit -m "feat: add amazing feature"
# 或
git commit -m "fix: resolve login issue"
```

### 5. 推送和创建 PR
```bash
# 推送分支
git push origin feature/your-feature-name

# 在 GitHub 上创建 Pull Request
```

## 📋 代码规范

### 提交信息格式
使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型**：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式（不影响代码逻辑）
- `refactor`: 重构代码
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**：
```
feat(auth): add Google OAuth integration
fix(quiz): resolve scoring calculation error
docs(api): update authentication endpoints
```

### 代码风格

#### 前端 (TypeScript/React)
- 使用 ESLint + Prettier
- 遵循 React Hooks 最佳实践
- 使用 TypeScript 严格模式
- 组件使用 PascalCase，文件使用 kebab-case

```typescript
// ✅ 好的例子
interface UserProfile {
  id: string;
  name: string;
  email: string;
}

const UserCard: React.FC<{ user: UserProfile }> = ({ user }) => {
  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <p>{user.email}</p>
    </div>
  );
};
```

#### 后端 (Python/FastAPI)
- 遵循 PEP 8 标准
- 使用 Black 格式化代码
- 使用类型注解
- 函数和变量使用 snake_case

```python
# ✅ 好的例子
from typing import List, Optional
from pydantic import BaseModel

class UserCreate(BaseModel):
    name: str
    email: str
    password: str

async def create_user(user_data: UserCreate) -> User:
    """创建新用户"""
    # 实现逻辑...
    pass
```

## 🧪 测试要求

### 前端测试
```bash
# 运行所有测试
npm run test

# 监听模式
npm run test:watch

# 覆盖率报告
npm run test:coverage
```

### 后端测试
```bash
# 在 backend 目录下
poetry run pytest

# 带覆盖率
poetry run pytest --cov=app
```

### 测试覆盖要求
- 新功能必须包含相应的测试
- 测试覆盖率不应低于 80%
- 关键功能（认证、AI集成、数据存储）需要高覆盖率

## 📖 文档要求

### API 文档
- 所有新的 API 端点必须包含 OpenAPI 文档
- 使用 FastAPI 的自动文档生成功能
- 提供示例请求和响应

### 代码注释
- 复杂逻辑必须有注释说明
- 公共 API 需要详细的文档字符串
- 使用英文注释

### 用户文档
- 新功能需要更新用户手册
- 提供截图和步骤说明
- 更新相关的 README 文件

## 🔍 代码审查

### PR 要求
- [ ] 代码通过所有测试
- [ ] 遵循代码规范
- [ ] 包含必要的文档更新
- [ ] PR 描述清晰，说明改动内容
- [ ] 如有需要，包含截图或演示

### 审查流程
1. 自动化测试必须通过
2. 至少一个维护者审查代码
3. 解决所有反馈意见
4. 获得批准后合并

## 🎉 认可贡献者

我们会在以下地方认可您的贡献：
- GitHub Contributors 列表
- 项目 README 的贡献者部分
- 发版说明中的感谢名单
- 特殊贡献可能获得项目徽章

## 📞 获取帮助

遇到问题？我们随时为您提供帮助：

- 💬 **GitHub Discussions** - 技术讨论和问答
- 📧 **邮件联系** - <EMAIL>
- 🐛 **Issue 报告** - 具体的问题和bug
- 📖 **文档** - 查看完整文档

## 🏆 贡献者奖励

积极的贡献者可能获得：
- 项目维护者权限
- 技术分享机会
- 推荐信支持
- 开源社区认可

---

**感谢您为 APCSA AI 平台做出的贡献！** 🎓✨ 