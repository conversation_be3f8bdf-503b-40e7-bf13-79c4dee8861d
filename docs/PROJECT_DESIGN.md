# APCSA AI 自动化学习平台 - 项目设计文档

## 📋 项目概述

**项目定位**：AP Computer Science A (APCSA) 智能学习平台  
**核心理念**：AI生成 + 手动上传 + 智能管理 + 数据驱动  
**目标用户**：APCSA学生、教师、补习班

## 🎯 核心功能模块

### 1. 题目与测试系统

#### AI自动出题
* 基于APCSA课程大纲自动生成选择题和编程题
* 支持按单元、难度、知识点筛选生成
* AI生成的题目自动保存到题库，供后续复用
* 包含自动生成的标准答案和评分标准(Rubric)

#### 手动题目管理
* 教师可手动上传自定义Quiz（支持.docx, .pdf, .txt格式）
* 支持导入外部题库（JSON/CSV格式）
* 题目编辑器：所见即所得的在线编辑器
* 题目标签系统：按知识点、难度、题型分类

#### 智能判分系统
* AI判分（基于Gemini/GPT + 自定义Rubric）
* 编程题支持多种测试用例验证
* 判分结果自动存档，包含详细反馈
* 支持教师人工复核和调整分数

### 2. 作业与文档系统

#### 作业上传与管理
* 学生上传：支持.java, .doc, .docx, .pdf, .zip等格式
* 教师布置：可设置截止时间、提交要求、评分标准
* 版本控制：自动保存学生多次提交的历史版本
* 批量下载：教师可批量下载全班作业

#### AI辅助批改
* 自动提取.doc/.docx中的代码和文字内容
* 基于预设Rubric进行AI初评
* 生成详细批改报告（语法、逻辑、最佳实践建议）
* 教师可在AI评分基础上进行调整

### 3. 视频与资源系统

#### 视频内容管理
* **手动上传**：支持mp4, mov, avi等格式，自动转码
* **外部链接**：YouTube, Khan Academy等平台视频嵌入
* **AI抓取**：使用Firecrawl MCP自动获取优质教程
* **字幕系统**：自动生成中英文字幕，支持手动校正

#### 资源库管理
* 所有AI生成和抓取的内容本地化存储
* 按APCSA单元结构化组织（Unit 1-10）
* 资源标签系统：难度、时长、知识点、语言
* 教师可手动补充和修正AI推荐的资源

### 4. 学生进度跟踪系统

#### 详细进度记录
```sql
-- 学生学习进度表设计
StudentProgress {
  student_id: string
  unit_id: string           -- APCSA Unit 1-10
  topic_id: string          -- 具体知识点
  completion_rate: float    -- 完成度 0-100%
  mastery_level: enum       -- NotStarted/Learning/Practicing/Mastered
  last_accessed: timestamp
  time_spent: integer       -- 总学习时间(分钟)
  attempts_count: integer   -- 尝试次数
  best_score: float        -- 最高分数
  created_at: timestamp
  updated_at: timestamp
}

-- 学习活动记录表
LearningActivity {
  id: string
  student_id: string
  activity_type: enum       -- Quiz/Assignment/Video/Practice
  content_id: string        -- 关联的内容ID
  score: float             -- 得分
  time_spent: integer      -- 花费时间
  submission_data: json    -- 提交内容
  ai_feedback: text        -- AI反馈
  teacher_feedback: text   -- 教师反馈
  timestamp: timestamp
}
```

#### 复习与记忆系统
* 基于艾宾浩斯曲线的智能复习提醒
* 错题本自动生成和推送
* 知识点掌握度热力图显示
* 个性化复习计划生成

### 5. AI内容生成与存储

#### 自动内容创建
* **题目生成**：每日自动生成新题并入库
* **解析生成**：为每道题自动生成详细解题步骤
* **示例代码**：AI生成多种解法和最佳实践示例
* **学习建议**：基于学生表现生成个性化建议

#### 数据库存储策略
```sql
-- AI生成内容表
AIGeneratedContent {
  id: string
  content_type: enum        -- Question/Explanation/Example/Suggestion
  prompt_used: text         -- 生成时使用的提示词
  raw_response: json        -- AI原始响应
  processed_content: json   -- 处理后的结构化内容
  quality_score: float      -- 内容质量评分
  usage_count: integer      -- 使用次数
  teacher_reviewed: boolean -- 是否经过教师审核
  is_active: boolean        -- 是否启用
  tags: string[]           -- 标签数组
  created_at: timestamp
  updated_at: timestamp
}

-- 内容使用统计表
ContentUsageStats {
  content_id: string
  student_interactions: integer -- 学生交互次数
  average_score: float         -- 平均得分
  difficulty_rating: float     -- 难度评级
  effectiveness_score: float   -- 教学效果评分
}
```

## 🏗️ 技术架构设计

### 数据库设计 (PostgreSQL + Supabase)

#### 核心表结构
```sql
-- 用户系统
Users, Teachers, Students

-- 课程内容
Units (APCSA 10个单元)
Topics (具体知识点)
Questions (题目库)
Assignments (作业)
Videos (视频资源)

-- 学习数据
StudentProgress (学习进度)
LearningActivity (学习活动)
SubmissionHistory (提交历史)
ReviewSchedule (复习计划)

-- AI内容
AIGeneratedContent (AI生成内容)
ContentUsageStats (使用统计)
PromptTemplates (提示词模板)
```

### 后端API设计 (FastAPI)

```
核心接口：
├── /auth/* (认证授权)
├── /content/* 
│   ├── /upload (手动上传)
│   ├── /generate (AI生成)
│   ├── /manage (内容管理)
├── /quiz/*
│   ├── /create (创建测试)
│   ├── /submit (提交答案)
│   ├── /grade (AI判分)
├── /progress/*
│   ├── /track (进度追踪)
│   ├── /analytics (学习分析)
│   ├── /review (复习系统)
├── /assignment/*
│   ├── /upload (作业上传)
│   ├── /batch-grade (批量判分)
└── /video/*
    ├── /upload (视频上传)
    ├── /process (视频处理)
    ├── /recommend (智能推荐)
```

### 前端页面结构 (React + TailwindCSS)

```
页面架构：
├── /dashboard (教师/学生仪表板)
├── /units/:id (单元学习页面)
├── /quiz (测试页面)
├── /assignment (作业管理)
├── /upload (内容上传页面)
├── /progress (进度分析)
├── /review (复习中心)
├── /ide (在线编程环境)
└── /analytics (数据分析页面)
```

## 🚀 MVP开发计划

### Phase 1: 基础框架 (4周)
- [ ] 用户认证系统
- [ ] 基础数据库设计
- [ ] 文件上传功能
- [ ] 简单的题目管理

### Phase 2: AI集成 (4周)
- [ ] AI题目生成
- [ ] AI判分系统
- [ ] 内容自动保存
- [ ] 基础进度追踪

### Phase 3: 高级功能 (4周)
- [ ] 视频系统
- [ ] 复习算法
- [ ] 数据分析面板
- [ ] 批量处理功能

### Phase 4: 优化完善 (4周)
- [ ] 性能优化
- [ ] 用户体验改进
- [ ] 错误处理完善
- [ ] 测试与部署

## 💾 数据持久化策略

### AI内容管理
* 所有AI生成内容实时保存到数据库
* 定期质量评估，标记高质量内容
* 建立内容版本控制系统
* 支持内容的批量导出和备份

### 学生数据保护
* 符合COPPA法规的数据处理
* 学习数据加密存储
* 定期数据备份和恢复测试
* 学生数据可导出和删除

### 缓存与性能
* Redis缓存热门内容
* CDN分发静态资源
* 数据库读写分离
* AI API调用结果缓存 