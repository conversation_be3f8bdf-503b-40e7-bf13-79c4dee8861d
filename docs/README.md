# 📚 APCSA AI Platform 文档中心

欢迎查阅 APCSA AI 学习平台的完整文档。本文档中心包含了项目的各个方面，从快速开始到详细的技术设计。

## 📖 文档导航

### 🚀 快速开始
- [项目介绍](../README.md) - 项目概述和特性介绍
- [快速开始指南](../GETTING_STARTED.md) - 5分钟快速上手
- [环境配置](../env.example) - 环境变量配置说明

### 🔧 开发指南
- [Mac M1 开发指南](../MAC_M1_SETUP.md) - Apple Silicon 开发环境配置
- [跨平台部署指南](../CROSS_PLATFORM_DEPLOYMENT.md) - Mac M1 → Ubuntu 服务器部署
- [开发计划](../DEVELOPMENT_PLAN.md) - 详细的开发路线图

### 📋 设计文档
- [项目设计文档](PROJECT_DESIGN.md) - 详细的功能设计和架构说明
- [数据库设计](../database/schema.sql) - 完整的数据库结构
- [API设计](API_REFERENCE.md) - RESTful API 接口文档

### 🏗️ 技术架构
- [系统架构](ARCHITECTURE.md) - 整体系统架构设计
- [前端技术栈](FRONTEND_GUIDE.md) - React + TypeScript 开发指南
- [后端技术栈](BACKEND_GUIDE.md) - FastAPI + Python 开发指南

### 🚀 部署运维
- [部署指南](DEPLOYMENT.md) - 生产环境部署说明
- [Docker 配置](../deploy/) - 容器化部署配置
- [监控运维](MONITORING.md) - 系统监控和运维指南

### 🧪 测试文档
- [测试策略](TESTING.md) - 自动化测试指南
- [性能测试](PERFORMANCE.md) - 性能基准和优化
- [安全测试](SECURITY.md) - 安全性测试和最佳实践

### 🤝 贡献指南
- [贡献指南](CONTRIBUTING.md) - 如何参与项目贡献
- [代码规范](CODE_STYLE.md) - 代码风格和规范
- [发布流程](RELEASE.md) - 版本发布流程

## 🔍 快速查找

### 按角色分类
- **学生用户** → [用户手册](USER_MANUAL.md)
- **教师用户** → [教师指南](TEACHER_GUIDE.md)
- **开发者** → [开发指南](#🔧-开发指南)
- **运维人员** → [部署运维](#🚀-部署运维)

### 按功能分类
- **AI功能** → [AI集成指南](AI_INTEGRATION.md)
- **用户认证** → [认证系统](AUTH_SYSTEM.md)
- **文件上传** → [文件系统](FILE_SYSTEM.md)
- **学习分析** → [数据分析](ANALYTICS.md)

## 📞 获取帮助

- 🐛 **Bug 报告** → [GitHub Issues](https://github.com/your-username/apcsa-ai-platform/issues)
- 💡 **功能建议** → [Feature Requests](https://github.com/your-username/apcsa-ai-platform/discussions)
- 📧 **技术支持** → <EMAIL>
- 💬 **社区讨论** → [Discord/Slack](https://your-community-link)

## 📝 文档贡献

文档同样重要！如果您发现文档有改进空间：

1. Fork 项目仓库
2. 在 `docs/` 目录下编辑或添加文档
3. 提交 Pull Request
4. 我们会及时审查并合并

## 🔄 文档更新

- **最后更新**: 2024年12月
- **更新频率**: 随项目开发实时更新
- **版本管理**: 跟随项目版本发布

---

**开始您的 APCSA AI 平台之旅！** 🎓✨ 