# Environment Variables Template
# Copy this file to .env and fill in your actual values

# =============================================
# Supabase Configuration
# =============================================
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# =============================================
# AI Services
# =============================================
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Google Gemini API Configuration
GOOGLE_API_KEY=your_google_api_key
GEMINI_MODEL=gemini-pro

# =============================================
# Application Settings
# =============================================
# Frontend URL (for CORS and callbacks)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:8000

# =============================================
# Database
# =============================================
# PostgreSQL Database URL (if not using Supabase)
DATABASE_URL=postgresql://username:password@localhost:5432/apcsa_ai_platform

# =============================================
# Redis Configuration (for caching and tasks)
# =============================================
REDIS_URL=redis://localhost:6379

# =============================================
# File Storage
# =============================================
# Supabase Storage Bucket
SUPABASE_STORAGE_BUCKET=apcsa-files

# Maximum file upload size (in MB)
MAX_FILE_SIZE_MB=20

# Allowed file types for uploads
ALLOWED_FILE_TYPES=java,doc,docx,pdf,txt,zip,mp4,mov,avi

# =============================================
# Security
# =============================================
# JWT Secret for session management
JWT_SECRET=your_super_secret_jwt_key_here

# API Rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# =============================================
# Email Configuration (for notifications)
# =============================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# =============================================
# Development/Production Settings
# =============================================
NODE_ENV=development
DEBUG=true

# Logging level (debug, info, warn, error)
LOG_LEVEL=debug

# =============================================
# Monitoring and Analytics
# =============================================
# Sentry DSN for error tracking
SENTRY_DSN=your_sentry_dsn

# Google Analytics ID
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Mixpanel Project Token
MIXPANEL_TOKEN=your_mixpanel_token

# =============================================
# AI Cost Management
# =============================================
# Daily AI API cost limit (in USD)
DAILY_AI_COST_LIMIT=50.00

# Cost tracking webhook URL
COST_ALERT_WEBHOOK_URL=your_webhook_url

# =============================================
# Code Execution (for IDE functionality)
# =============================================
# Docker configuration for code execution
DOCKER_ENABLED=true
DOCKER_TIMEOUT_SECONDS=30
DOCKER_MEMORY_LIMIT=128m

# =============================================
# Feature Flags
# =============================================
# Enable/disable specific features
FEATURE_AI_GRADING=true
FEATURE_VIDEO_UPLOAD=true
FEATURE_REAL_TIME_COLLABORATION=false
FEATURE_ADVANCED_ANALYTICS=true

# =============================================
# External APIs
# =============================================
# YouTube API (for video integration)
YOUTUBE_API_KEY=your_youtube_api_key

# Khan Academy API (if available)
KHAN_ACADEMY_API_KEY=your_khan_academy_api_key 