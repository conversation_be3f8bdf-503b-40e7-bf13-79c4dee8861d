# 🌍 Ubuntu 服务器生产环境配置
# 复制此文件为 .env.production 并填入实际值

# 服务器信息
SERVER_HOST=your-ubuntu-server.com
SERVER_USER=ubuntu
DEPLOY_PATH=/opt/apcsa-ai-platform

# Docker 配置
DOCKER_REGISTRY=local  # 或者使用 docker.io/yourusername
IMAGE_NAME=apcsa-backend

# 数据库配置 (Supabase)
DATABASE_URL=postgresql://postgres:[password]@[host]:5432/[database]
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# AI API 配置
OPENAI_API_KEY=sk-your-openai-api-key
GOOGLE_API_KEY=your-google-gemini-api-key

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
NODE_ENV=production
SECRET_KEY=your-super-secret-jwt-key
CORS_ORIGINS=["https://your-frontend-domain.com"]

# 文件上传配置
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/opt/apcsa-ai-platform/uploads

# 监控配置 (可选)
SENTRY_DSN=https://your-sentry-dsn
LOG_LEVEL=INFO 