{"name": "apcsa-ai-frontend", "version": "1.0.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@monaco-editor/react": "^4.7.0", "@tanstack/react-query": "^5.59.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "axios": "^1.7.7", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "monaco-editor": "^0.52.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^4.1.2", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "typescript": "^5.6.3", "web-vitals": "^4.2.4", "zustand": "^5.0.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^22.15.24", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "postcss": "^8.5.4", "prettier": "^3.3.3", "tailwindcss": "^3.4.0", "webpack-bundle-analyzer": "^4.10.2"}}