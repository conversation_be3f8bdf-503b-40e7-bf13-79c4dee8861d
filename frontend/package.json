{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@monaco-editor/react": "^4.7.0", "@supabase/supabase-js": "^2.49.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "ajv": "^8.17.1", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "monaco-editor": "^0.52.2", "PostCSS": "npm:postcss@^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^22.15.24", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.4.0"}}