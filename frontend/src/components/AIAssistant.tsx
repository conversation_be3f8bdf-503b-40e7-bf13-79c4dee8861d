import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Brain, MessageCircle, BookOpen, Lightbulb, Send, Loader } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface AIAssistantProps {
  isOpen: boolean;
  onClose: () => void;
  context?: {
    topic?: string;
    unitId?: string;
    exerciseId?: string;
  };
}

const AIAssistant: React.FC<AIAssistantProps> = ({ isOpen, onClose, context = {} }) => {
  const [activeTab, setActiveTab] = useState<'explain' | 'hint' | 'ask'>('ask');
  const [loading, setLoading] = useState(false);
  const [question, setQuestion] = useState('');
  const [concept, setConcept] = useState('');
  const [exerciseCode, setExerciseCode] = useState('');
  const [aiResponse, setAiResponse] = useState<any>(null);
  const { token } = useAuth();

  const handleAskQuestion = async () => {
    if (!question.trim()) return;
    
    setLoading(true);
    try {
      // 优先使用认证API，fallback到demo API
      let response;
      if (token) {
        try {
          response = await fetch('http://localhost:8000/api/ai/ask', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              question: question,
              context: `Unit: ${context.topic || 'Unit 1'}, Topic: ${context.unitId || 'Primitive Types'}`
            }),
          });
          
          if (!response.ok) {
            throw new Error('认证API失败');
          }
        } catch (authError) {
          console.log('🔄 认证API失败，使用demo API:', authError);
          response = await fetch('http://localhost:8000/api/demo/ai/ask', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              question: question,
              context: `Unit: ${context.topic || 'Unit 1'}, Topic: ${context.unitId || 'Primitive Types'}`
            }),
          });
        }
      } else {
        response = await fetch('http://localhost:8000/api/demo/ai/ask', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            question: question,
            context: `Unit: ${context.topic || 'Unit 1'}, Topic: ${context.unitId || 'Primitive Types'}`
          }),
        });
      }

      const result = await response.json();
      setAiResponse(result);
      
      // 保存到文档库
      saveToDocumentLibrary('qa', {
        question,
        answer: result.answer,
        context: context,
        timestamp: new Date().toISOString()
      });

      // 保存对话到数据库
      saveChatbotConversation(question, result.answer, 'qa');
    } catch (error) {
      console.error('AI请求失败:', error);
      setAiResponse({
        success: false,
        answer: '抱歉，AI助手暂时不可用。请稍后再试。'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExplainConcept = async () => {
    if (!concept.trim()) return;
    
    setLoading(true);
    try {
      let response;
      if (token) {
        try {
          response = await fetch('http://localhost:8000/api/ai/explain', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              topic: context.topic || 'Unit 1',
              concept: concept,
              user_level: 'beginner'
            }),
          });
          
          if (!response.ok) {
            throw new Error('认证API失败');
          }
        } catch (authError) {
          console.log('🔄 认证API失败，使用demo API:', authError);
          response = await fetch('http://localhost:8000/api/demo/ai/explain', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              topic: context.topic || 'Unit 1',
              concept: concept,
              user_level: 'beginner'
            }),
          });
        }
      } else {
        response = await fetch('http://localhost:8000/api/demo/ai/explain', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            topic: context.topic || 'Unit 1',
            concept: concept,
            user_level: 'beginner'
          }),
        });
      }

      const result = await response.json();
      setAiResponse(result);
      
      // 保存到文档库
      saveToDocumentLibrary('concept', {
        concept,
        explanation: result.explanation,
        topic: context.topic,
        timestamp: new Date().toISOString()
      });

      // 保存对话到数据库
      saveChatbotConversation(concept, result.explanation, 'concept');
    } catch (error) {
      console.error('AI请求失败:', error);
      setAiResponse({
        success: false,
        explanation: '抱歉，AI助手暂时不可用。请稍后再试。'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGetHint = async () => {
    setLoading(true);
    try {
      let response;
      if (token) {
        try {
          response = await fetch('http://localhost:8000/api/ai/hint', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              exercise_description: `当前练习: ${context.exerciseId || 'Hello World练习'}`,
              student_code: exerciseCode
            }),
          });
          
          if (!response.ok) {
            throw new Error('认证API失败');
          }
        } catch (authError) {
          console.log('🔄 认证API失败，使用demo API:', authError);
          response = await fetch('http://localhost:8000/api/demo/ai/hint', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              exercise_description: `当前练习: ${context.exerciseId || 'Hello World练习'}`,
              student_code: exerciseCode
            }),
          });
        }
      } else {
        response = await fetch('http://localhost:8000/api/demo/ai/hint', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            exercise_description: `当前练习: ${context.exerciseId || 'Hello World练习'}`,
            student_code: exerciseCode
          }),
        });
      }

      const result = await response.json();
      setAiResponse(result);
      
      // 保存到文档库
      saveToDocumentLibrary('hint', {
        exercise_description: context.exerciseId,
        student_code: exerciseCode,
        hint: result.hint,
        timestamp: new Date().toISOString()
      });

      // 保存对话到数据库
      saveChatbotConversation(exerciseCode, result.hint, 'hint');
    } catch (error) {
      console.error('AI请求失败:', error);
      setAiResponse({
        success: false,
        hint: '抱歉，AI助手暂时不可用。请稍后再试。'
      });
    } finally {
      setLoading(false);
    }
  };

  // 保存对话到数据库的函数
  const saveChatbotConversation = async (userMessage: string, aiResponse: string, type: string) => {
    if (!token) return; // 只有登录用户才保存对话
    
    try {
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      await fetch('http://localhost:8000/api/chatbot/conversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userMessage,
          aiResponse,
          type,
          context,
          sessionId
        }),
      });
    } catch (error) {
      console.log('保存对话失败:', error);
    }
  };

  // 保存到文档库的函数
  const saveToDocumentLibrary = async (type: string, data: any) => {
    try {
      await fetch('http://localhost:8000/api/document-library/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          data,
          unit_id: context.unitId,
          topic: context.topic
        }),
      });
    } catch (error) {
      console.log('保存到文档库失败:', error);
    }
  };

  const tabs = [
    { id: 'ask', label: 'AI问答', icon: MessageCircle, color: 'text-blue-400' },
    { id: 'explain', label: '概念解释', icon: BookOpen, color: 'text-green-400' },
    { id: 'hint', label: '智能提示', icon: Lightbulb, color: 'text-yellow-400' },
  ];

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-dark-800 border border-dark-600 rounded-xl shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-500 to-blue-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Brain className="w-8 h-8" />
              <div>
                <h2 className="text-2xl font-bold">AI学习助手</h2>
                <p className="text-purple-100">您的APCSA Java编程伙伴</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="flex h-[60vh]">
          {/* Sidebar */}
          <div className="w-1/4 bg-dark-700 border-r border-dark-600">
            <div className="p-4">
              <h3 className="font-semibold text-dark-100 mb-4">AI功能</h3>
              <div className="space-y-2">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveTab(tab.id as any);
                        setAiResponse(null);
                      }}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary-500 text-white border border-primary-400'
                          : 'hover:bg-dark-600 text-dark-200'
                      }`}
                    >
                      <IconComponent className={`w-5 h-5 ${activeTab === tab.id ? 'text-white' : tab.color}`} />
                      <span className="font-medium">{tab.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col bg-dark-800">
            <div className="flex-1 p-6 overflow-y-auto">
              {activeTab === 'ask' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center space-x-2 text-dark-100">
                    <MessageCircle className="w-5 h-5 text-blue-400" />
                    <span>AI问答助手</span>
                  </h3>
                  <p className="text-dark-300">有任何Java编程问题都可以问我！</p>
                  
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-dark-200">
                      您的问题:
                    </label>
                    <textarea
                      value={question}
                      onChange={(e) => setQuestion(e.target.value)}
                      placeholder="例如：什么是变量？如何声明一个整数变量？"
                      className="w-full p-3 bg-dark-700 border border-dark-500 text-dark-100 placeholder-dark-400 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={3}
                    />
                    <button
                      onClick={handleAskQuestion}
                      disabled={loading || !question.trim()}
                      className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
                      <span>{loading ? '思考中...' : '提问'}</span>
                    </button>
                  </div>
                </div>
              )}

              {activeTab === 'explain' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center space-x-2 text-dark-100">
                    <BookOpen className="w-5 h-5 text-green-400" />
                    <span>概念解释器</span>
                  </h3>
                  <p className="text-dark-300">让AI为您详细解释Java编程概念</p>
                  
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-dark-200">
                      请输入要解释的概念:
                    </label>
                    <input
                      type="text"
                      value={concept}
                      onChange={(e) => setConcept(e.target.value)}
                      placeholder="例如：variables, data types, methods"
                      className="w-full p-3 bg-dark-700 border border-dark-500 text-dark-100 placeholder-dark-400 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                    <button
                      onClick={handleExplainConcept}
                      disabled={loading || !concept.trim()}
                      className="flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Brain className="w-4 h-4" />}
                      <span>{loading ? '解释中...' : '获取解释'}</span>
                    </button>
                  </div>
                </div>
              )}

              {activeTab === 'hint' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center space-x-2 text-dark-100">
                    <Lightbulb className="w-5 h-5 text-yellow-400" />
                    <span>智能提示</span>
                  </h3>
                  <p className="text-dark-300">遇到困难？让AI给您一些编程提示！</p>
                  
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-dark-200">
                      您的代码 (可选):
                    </label>
                    <textarea
                      value={exerciseCode}
                      onChange={(e) => setExerciseCode(e.target.value)}
                      placeholder="粘贴您的Java代码，AI会分析并给出建议..."
                      className="w-full p-3 bg-dark-700 border border-dark-500 text-dark-100 placeholder-dark-400 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent font-mono"
                      rows={5}
                    />
                    <button
                      onClick={handleGetHint}
                      disabled={loading}
                      className="flex items-center space-x-2 bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
                      <span>{loading ? '分析中...' : '获取提示'}</span>
                    </button>
                  </div>
                </div>
              )}

              {/* AI Response */}
              {aiResponse && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-6 p-4 bg-dark-700 border border-dark-500 rounded-lg"
                >
                  <div className="flex items-center space-x-2 mb-3">
                    <Brain className="w-5 h-5 text-purple-400" />
                    <span className="font-semibold text-dark-100">AI回答:</span>
                  </div>
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap text-dark-200 leading-relaxed">
                      {aiResponse.explanation || aiResponse.answer || aiResponse.hint || 'AI响应格式错误'}
                    </div>
                  </div>
                  {aiResponse.model_used && (
                    <div className="mt-3 text-xs text-dark-400">
                      使用模型: {aiResponse.model_used} • {new Date(aiResponse.timestamp).toLocaleTimeString()}
                    </div>
                  )}
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AIAssistant; 