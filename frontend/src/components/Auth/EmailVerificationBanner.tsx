import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';

interface EmailVerificationBannerProps {
  email?: string;
}

const EmailVerificationBanner: React.FC<EmailVerificationBannerProps> = ({ email }) => {
  const { user, resendVerificationEmail, checkEmailVerification } = useAuth();
  const [isResending, setIsResending] = useState(false);
  const [message, setMessage] = useState('');
  const [isChecking, setIsChecking] = useState(false);

  const userEmail = email || user?.email;

  const handleResendEmail = async () => {
    if (!userEmail) return;
    
    setIsResending(true);
    setMessage('');
    
    const result = await resendVerificationEmail(userEmail);
    setMessage(result.message || '');
    
    setIsResending(false);
  };

  const handleCheckVerification = async () => {
    setIsChecking(true);
    setMessage('');
    
    const isVerified = await checkEmailVerification();
    
    if (isVerified) {
      setMessage('邮箱已验证！请刷新页面。');
      // 可以触发页面刷新或状态更新
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else {
      setMessage('邮箱尚未验证。请检查邮箱并点击确认链接。');
    }
    
    setIsChecking(false);
  };

  if (!userEmail || (user && user.email_confirmed_at)) {
    return null;
  }

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            邮箱验证待完成
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p>
              我们已向 <strong>{userEmail}</strong> 发送了验证邮件。
              请检查您的邮箱（包括垃圾邮件文件夹）并点击确认链接来激活您的账户。
            </p>
            {message && (
              <p className={`mt-2 ${message.includes('成功') || message.includes('已验证') ? 'text-green-700' : 'text-red-700'}`}>
                {message}
              </p>
            )}
          </div>
          <div className="mt-4 flex space-x-3">
            <button
              onClick={handleResendEmail}
              disabled={isResending}
              className="text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded-md transition-colors disabled:opacity-50"
            >
              {isResending ? '发送中...' : '重发验证邮件'}
            </button>
            <button
              onClick={handleCheckVerification}
              disabled={isChecking}
              className="text-sm bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded-md transition-colors disabled:opacity-50"
            >
              {isChecking ? '检查中...' : '我已验证'}
            </button>
          </div>
          <div className="mt-3 text-xs text-yellow-600">
            <p>💡 提示：</p>
            <ul className="mt-1 space-y-1">
              <li>• 验证邮件可能需要几分钟才能到达</li>
              <li>• 请检查垃圾邮件文件夹</li>
              <li>• 确认链接将在24小时后过期</li>
              <li>• 如有问题请联系支持团队</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationBanner; 