import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Lock, Eye, EyeOff, LogIn, Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

// Google Icon SVG
const GoogleIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24">
    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
  </svg>
);

interface SignInProps {
  onToggleMode: () => void;
  onSuccess?: () => void;
}

const SignIn: React.FC<SignInProps> = ({ onToggleMode, onSuccess }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isResendingVerification, setIsResendingVerification] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [showEmailActions, setShowEmailActions] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const { signIn, signInWithGoogle, resendVerificationEmail, resetPassword } = useAuth();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // 清除错误和成功信息
    if (error) setError('');
    if (successMessage) setSuccessMessage('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password) {
      setError('请填写所有字段');
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      const result = await signIn(formData.email, formData.password);
      
      if (result.success) {
        setSuccessMessage(result.message || '登录成功！');
        onSuccess?.();
      } else {
        setError(result.message || '登录失败');
        // 如果是邮箱验证错误，显示邮箱操作按钮
        if (result.message?.includes('邮箱') || result.message?.includes('确认')) {
          setShowEmailActions(true);
        }
      }
    } catch (error) {
      setError('发生意外错误，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    if (!formData.email) {
      setError('请先输入邮箱地址');
      return;
    }

    setIsResendingVerification(true);
    setError('');
    setSuccessMessage('');

    try {
      const result = await resendVerificationEmail(formData.email);
      if (result.success) {
        setSuccessMessage(result.message || '验证邮件已发送');
      } else {
        setError(result.message || '发送失败');
      }
    } catch (error) {
      setError('发送验证邮件失败，请重试');
    } finally {
      setIsResendingVerification(false);
    }
  };

  const handleResetPassword = async () => {
    if (!formData.email) {
      setError('请先输入邮箱地址');
      return;
    }

    setIsResettingPassword(true);
    setError('');
    setSuccessMessage('');

    try {
      const result = await resetPassword(formData.email);
      if (result.success) {
        setSuccessMessage(result.message || '重置密码邮件已发送');
      } else {
        setError(result.message || '发送失败');
      }
    } catch (error) {
      setError('发送重置密码邮件失败，请重试');
    } finally {
      setIsResettingPassword(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      const result = await signInWithGoogle();
      if (result.success) {
        setSuccessMessage(result.message || '正在跳转到Google登录...');
        // Google登录会跳转，成功后会自动处理回调
      } else {
        setError(result.message || 'Google登录失败');
      }
    } catch (error) {
      setError('Google登录失败，请重试');
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="w-full max-w-md mx-auto"
    >
      <div className="glass-effect rounded-2xl p-8 shadow-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <LogIn className="w-8 h-8 text-primary-600" />
          </motion.div>
          <h2 className="text-3xl font-bold gradient-text mb-2">欢迎回来</h2>
          <p className="text-dark-400">登录继续您的 APCSA 学习之旅</p>
        </div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center space-x-2"
          >
            <AlertCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-700 text-sm">{error}</span>
          </motion.div>
        )}

        {/* Success Message */}
        {successMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center space-x-2"
          >
            <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="text-green-700 text-sm">{successMessage}</span>
          </motion.div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-dark-300 mb-2">
              邮箱地址
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-dark-400" />
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-3 border border-dark-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white/50 backdrop-blur-sm transition-all"
                placeholder="输入您的邮箱"
                required
              />
            </div>
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-dark-300 mb-2">
              密码
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-dark-400" />
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full pl-10 pr-12 py-3 border border-dark-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white/50 backdrop-blur-sm transition-all"
                placeholder="输入您的密码"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-dark-400 hover:text-dark-600 transition-colors"
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={isLoading}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>登录中...</span>
              </>
            ) : (
              <>
                <LogIn className="w-5 h-5" />
                <span>登录</span>
              </>
            )}
          </motion.button>
        </form>

        {/* 分隔线 */}
        <div className="my-6 flex items-center">
          <div className="flex-1 border-t border-dark-200"></div>
          <span className="px-4 text-sm text-dark-400">或</span>
          <div className="flex-1 border-t border-dark-200"></div>
        </div>

        {/* Google 登录按钮 */}
        <motion.button
          onClick={handleGoogleSignIn}
          disabled={isGoogleLoading}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full bg-white border border-dark-200 text-dark-600 py-3 rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3"
        >
          {isGoogleLoading ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin" />
              <span>连接Google中...</span>
            </>
          ) : (
            <>
              <GoogleIcon />
              <span>使用 Google 登录</span>
            </>
          )}
        </motion.button>

        {/* Email Actions */}
        {showEmailActions && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <p className="text-sm text-blue-800 mb-3">需要帮助？</p>
            <div className="flex space-x-2">
              <button
                onClick={handleResendVerification}
                disabled={isResendingVerification || !formData.email}
                className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded-md text-sm transition-colors disabled:opacity-50 flex items-center justify-center space-x-1"
              >
                {isResendingVerification ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                <span>{isResendingVerification ? '发送中...' : '重发验证邮件'}</span>
              </button>
              <button
                onClick={handleResetPassword}
                disabled={isResettingPassword || !formData.email}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-2 rounded-md text-sm transition-colors disabled:opacity-50 flex items-center justify-center space-x-1"
              >
                {isResettingPassword ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Mail className="w-4 h-4" />
                )}
                <span>{isResettingPassword ? '发送中...' : '忘记密码'}</span>
              </button>
            </div>
          </motion.div>
        )}

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-dark-400 text-sm">
            还没有账户？{' '}
            <button
              onClick={onToggleMode}
              className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
            >
              免费注册
            </button>
          </p>
          <button
            onClick={() => setShowEmailActions(!showEmailActions)}
            className="mt-2 text-xs text-dark-400 hover:text-dark-600 transition-colors"
          >
            {showEmailActions ? '隐藏' : '显示'}邮箱验证选项
          </button>
        </div>

        {/* Demo Credentials */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 mb-2">演示账户：</h4>
          <div className="text-xs text-blue-700 space-y-1">
            <p><strong>学生：</strong> <EMAIL> / password123</p>
            <p><strong>教师：</strong> <EMAIL> / password123</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default SignIn; 