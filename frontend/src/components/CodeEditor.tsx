import React, { useState, useRef } from 'react';
import Editor from '@monaco-editor/react';
import { Play, Save, RotateCcw, CheckCircle, XCircle } from 'lucide-react';

interface CodeEditorProps {
  initialCode?: string;
  onCodeChange?: (code: string) => void;
  onSave?: (code: string) => void;
  readOnly?: boolean;
  height?: string;
}

interface ExecutionResult {
  success: boolean;
  output: string;
  error?: string;
  executionTime?: number;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  initialCode = '// Write your Java code here\npublic class Main {\n    public static void main(String[] args) {\n        System.out.println("Hello, APCSA!");\n    }\n}',
  onCodeChange,
  onSave,
  readOnly = false,
  height = '400px'
}) => {
  const [code, setCode] = useState(initialCode);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);
  const [isSaved, setIsSaved] = useState(false);
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;
    
    // Configure Java language features
    monaco.languages.java?.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
    });
  };

  const handleCodeChange = (value: string | undefined) => {
    const newCode = value || '';
    setCode(newCode);
    setIsSaved(false);
    onCodeChange?.(newCode);
  };

  const handleSave = () => {
    onSave?.(code);
    setIsSaved(true);
    setTimeout(() => setIsSaved(false), 2000);
  };

  const handleReset = () => {
    setCode(initialCode);
    setExecutionResult(null);
    onCodeChange?.(initialCode);
  };

  const simulateExecution = async () => {
    setIsExecuting(true);
    setExecutionResult(null);

    // Simulate compilation and execution delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simple simulation - in real implementation, this would call a backend API
    try {
      // Basic validation
      if (!code.includes('public static void main')) {
        throw new Error('Missing main method');
      }

      if (!code.includes('class')) {
        throw new Error('Missing class declaration');
      }

      // Simulate successful execution
      const output = code.includes('System.out.println') 
        ? 'Hello, APCSA!\nProgram executed successfully!'
        : 'Program compiled and executed successfully!';

      setExecutionResult({
        success: true,
        output,
        executionTime: Math.random() * 1000 + 500 // Random execution time
      });
    } catch (error) {
      setExecutionResult({
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Compilation error'
      });
    }

    setIsExecuting(false);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Java Editor
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              APCSA Practice
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleReset}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
              title="Reset to initial code"
            >
              <RotateCcw size={14} />
              <span>Reset</span>
            </button>
            
            <button
              onClick={handleSave}
              className={`flex items-center space-x-1 px-3 py-1 text-sm rounded transition-colors ${
                isSaved
                  ? 'bg-green-200 dark:bg-green-700 text-green-800 dark:text-green-200'
                  : 'bg-blue-200 dark:bg-blue-700 text-blue-800 dark:text-blue-200 hover:bg-blue-300 dark:hover:bg-blue-600'
              }`}
              title="Save code"
            >
              {isSaved ? <CheckCircle size={14} /> : <Save size={14} />}
              <span>{isSaved ? 'Saved!' : 'Save'}</span>
            </button>
            
            <button
              onClick={simulateExecution}
              disabled={isExecuting || readOnly}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-green-200 dark:bg-green-700 text-green-800 dark:text-green-200 rounded hover:bg-green-300 dark:hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Run code"
            >
              <Play size={14} />
              <span>{isExecuting ? 'Running...' : 'Run'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="relative">
        <Editor
          height={height}
          defaultLanguage="java"
          value={code}
          onChange={handleCodeChange}
          onMount={handleEditorDidMount}
          options={{
            readOnly,
            minimap: { enabled: false },
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 4,
            insertSpaces: true,
            wordWrap: 'on',
            theme: 'vs-dark'
          }}
        />
      </div>

      {/* Execution Results */}
      {executionResult && (
        <div className="border-t border-gray-200 dark:border-gray-600">
          <div className="px-4 py-2 bg-gray-50 dark:bg-gray-700">
            <div className="flex items-center space-x-2">
              {executionResult.success ? (
                <CheckCircle size={16} className="text-green-600 dark:text-green-400" />
              ) : (
                <XCircle size={16} className="text-red-600 dark:text-red-400" />
              )}
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {executionResult.success ? 'Execution Successful' : 'Execution Failed'}
              </span>
              {executionResult.executionTime && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  ({Math.round(executionResult.executionTime)}ms)
                </span>
              )}
            </div>
          </div>
          
          <div className="px-4 py-3 bg-gray-900 text-green-400 font-mono text-sm max-h-32 overflow-y-auto">
            {executionResult.success ? (
              <pre>{executionResult.output}</pre>
            ) : (
              <pre className="text-red-400">{executionResult.error}</pre>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CodeEditor; 