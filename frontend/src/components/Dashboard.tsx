import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Code2, 
  Brain, 
  BookOpen, 
  Trophy, 
  Clock, 
  Target,
  Zap,
  ChevronRight,
  Play,
  FileText
} from 'lucide-react';
import TypingAnimation from './TypingAnimation';
import ProgressTracker from './ProgressTracker';
import AIAssistant from './AIAssistant';
import { useAuth } from '../contexts/AuthContext';

interface DashboardStats {
  unitsCompleted: string;
  totalUnits: string;
  problemsSolved: string;
  totalProblems: string;
  currentStreak: string;
  averageScore: string;
}

interface RecentActivity {
  type: 'quiz' | 'assignment' | 'video';
  title: string;
  score?: number;
  duration?: string;
  time: string;
  unit?: string;
}

interface NextUnit {
  id: number;
  title: string;
  progress: number;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [nextUnits, setNextUnits] = useState<NextUnit[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false);
  const { token, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!token || !user) {
        setLoading(false);
        return;
      }

      try {
        // 获取用户统计数据
        const statsResponse = await fetch('http://localhost:8000/api/user/stats', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (statsResponse.ok) {
          const statsResult = await statsResponse.json();
          setStats(statsResult.data);
        }

        // 获取用户进度数据（包含活动数据）
        const progressResponse = await fetch('http://localhost:8000/api/user/progress', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (progressResponse.ok) {
          const progressResult = await progressResponse.json();
          setRecentActivities(progressResult.data.recentActivities || []);
          
          // 基于总单元数生成后续单元
          const totalUnits = progressResult.data.totalUnits;
          const completedUnits = progressResult.data.completedUnits;
          
          const nextUnitsData: NextUnit[] = [];
          for (let i = completedUnits + 1; i <= Math.min(completedUnits + 3, totalUnits); i++) {
            const unitTitles = [
              'Primitive Types', 'Using Objects', 'Boolean Expressions and if Statements',
              'Iteration', 'Writing Classes', 'Array', 'ArrayList', '2D Array', 
              'Inheritance', 'Recursion'
            ];
            
            nextUnitsData.push({
              id: i,
              title: unitTitles[i - 1] || `Unit ${i}`,
              progress: 0,
              difficulty: i <= 3 ? 'Beginner' : i <= 6 ? 'Intermediate' : i <= 8 ? 'Advanced' : 'Expert'
            });
          }
          setNextUnits(nextUnitsData);
        }

      } catch (err) {
        console.error('获取Dashboard数据失败:', err);
        
        // 使用基础默认数据
        setStats({
          unitsCompleted: '0',
          totalUnits: '10',
          problemsSolved: '0',
          totalProblems: '50',
          currentStreak: '0',
          averageScore: '0'
        });
        setRecentActivities([]);
        setNextUnits([
          { id: 1, title: 'Primitive Types', progress: 0, difficulty: 'Beginner' },
          { id: 2, title: 'Using Objects', progress: 0, difficulty: 'Beginner' },
          { id: 3, title: 'Boolean Expressions', progress: 0, difficulty: 'Intermediate' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [token, user]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-dark-400 mb-4">无法加载Dashboard数据</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  const statsData = [
    { 
      label: 'Units Completed', 
      value: stats.unitsCompleted, 
      total: stats.totalUnits, 
      icon: BookOpen, 
      color: 'text-blue-500' 
    },
    { 
      label: 'Problems Solved', 
      value: stats.problemsSolved, 
      total: stats.totalProblems, 
      icon: Code2, 
      color: 'text-green-500' 
    },
    { 
      label: 'Current Streak', 
      value: stats.currentStreak, 
      unit: 'days', 
      icon: Zap, 
      color: 'text-yellow-500' 
    },
    { 
      label: 'Average Score', 
      value: stats.averageScore, 
      unit: '%', 
      icon: Trophy, 
      color: 'text-purple-500' 
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen p-6"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="code-terminal mb-4"
          >
            <div className="text-green-400 font-code text-sm mb-2">
              $ apcsa-ai --status
            </div>
            <div className="text-primary-500">
              <TypingAnimation text={`Welcome back, ${user?.full_name || 'Student'}! Ready to code?`} delay={500} />
            </div>
          </motion.div>
          
          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-4xl font-bold gradient-text mb-2"
          >
            Learning Dashboard
          </motion.h1>
          <p className="text-dark-400">Track your APCSA journey with AI-powered insights</p>
        </div>

        {/* Progress Tracker Section */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mb-8"
        >
          <ProgressTracker />
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsData.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 + index * 0.1 }}
              className="bg-dark-100 border border-dark-200 rounded-lg p-6 hover-lift hover:bg-dark-50 transition-all duration-300 shadow-lg"
            >
              <div className="flex items-center justify-between mb-4">
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
                <div className="text-right">
                  <div className="text-2xl font-bold text-dark-900">
                    {stat.value}
                    {stat.unit && <span className="text-lg text-dark-400">{stat.unit}</span>}
                  </div>
                  {stat.total && (
                    <div className="text-sm text-dark-400">of {stat.total}</div>
                  )}
                </div>
              </div>
              <div className="text-sm font-medium text-dark-600">{stat.label}</div>
              {stat.total && (
                <div className="mt-2 w-full bg-dark-200 rounded-full h-2">
                  <div 
                    className="bg-primary-500 h-2 rounded-full transition-all"
                    style={{ width: `${Math.min((parseInt(stat.value) / parseInt(stat.total)) * 100, 100)}%` }}
                  />
                </div>
              )}
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.7 }}
            className="lg:col-span-2 bg-dark-100 border border-dark-200 rounded-lg p-6 shadow-lg"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-dark-900 flex items-center">
                <Clock className="w-5 h-5 mr-2 text-primary-500" />
                Recent Activity
              </h2>
              <button 
                onClick={() => navigate('/units')}
                className="text-primary-500 hover:text-primary-600 text-sm font-medium"
              >
                View All
              </button>
            </div>
            
            <div className="space-y-4">
              {recentActivities.length > 0 ? (
                recentActivities.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    className="flex items-center justify-between p-4 bg-dark-100 rounded-lg hover:bg-dark-200 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      {activity.type === 'quiz' && <Brain className="w-5 h-5 text-blue-500" />}
                      {activity.type === 'assignment' && <FileText className="w-5 h-5 text-green-500" />}
                      {activity.type === 'video' && <Play className="w-5 h-5 text-purple-500" />}
                      
                      <div>
                        <div className="font-medium text-dark-900">{activity.title}</div>
                        <div className="text-sm text-dark-400">
                          {activity.unit} • {activity.time}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      {activity.score && (
                        <div className="font-bold text-primary-500">{activity.score}%</div>
                      )}
                      {activity.duration && (
                        <div className="text-sm text-dark-500">{activity.duration}</div>
                      )}
                    </div>
                  </motion.div>
                ))
              ) : (
                <div className="text-center py-8 text-dark-400">
                  <BookOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>还没有学习活动</p>
                  <p className="text-sm">开始学习来查看你的进度</p>
                </div>
              )}
            </div>
          </motion.div>

          {/* Next Up */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="bg-dark-100 border border-dark-200 rounded-lg p-6 shadow-lg"
          >
            <h2 className="text-xl font-bold text-dark-900 mb-6 flex items-center">
              <Target className="w-5 h-5 mr-2 text-primary-500" />
              Next Up
            </h2>
            
            <div className="space-y-4">
              {nextUnits.map((unit, index) => (
                <motion.div
                  key={unit.id}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.9 + index * 0.1 }}
                  onClick={() => navigate(`/units/${unit.id}`)}
                  className="p-4 bg-dark-100 rounded-lg hover:bg-dark-200 transition-colors cursor-pointer group"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-dark-900">Unit {unit.id}</span>
                    <ChevronRight className="w-4 h-4 text-dark-400 group-hover:text-primary-500 transition-colors" />
                  </div>
                  
                  <div className="text-sm text-dark-600 mb-2">{unit.title}</div>
                  
                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      unit.difficulty === 'Beginner' ? 'bg-green-100 text-green-600' :
                      unit.difficulty === 'Intermediate' ? 'bg-blue-100 text-blue-600' :
                      unit.difficulty === 'Advanced' ? 'bg-orange-100 text-orange-600' : 
                      'bg-red-100 text-red-600'
                    }`}>
                      {unit.difficulty}
                    </span>
                    
                    {unit.progress > 0 ? (
                      <div className="flex items-center text-xs text-dark-500">
                        <div className="w-16 bg-dark-200 rounded-full h-1 mr-2">
                          <div 
                            className="bg-primary-500 h-1 rounded-full"
                            style={{ width: `${unit.progress}%` }}
                          />
                        </div>
                        {unit.progress}%
                      </div>
                    ) : (
                      <div className="text-xs text-dark-400">Not Started</div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
            
            <button 
              onClick={() => navigate('/units')}
              className="w-full mt-6 bg-primary-500 hover:bg-primary-600 text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              <Play className="w-4 h-4 mr-2" />
              Continue Learning
            </button>
          </motion.div>
        </div>
      </div>

      {/* AI助手浮动按钮 */}
      <motion.button
        onClick={() => setAiAssistantOpen(true)}
        className="fixed bottom-6 right-6 bg-gradient-to-r from-purple-500 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-40"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
      >
        <Brain className="w-6 h-6" />
      </motion.button>

      {/* AI助手组件 */}
      <AIAssistant
        isOpen={aiAssistantOpen}
        onClose={() => setAiAssistantOpen(false)}
        context={{
          topic: "Learning Dashboard",
          unitId: "dashboard",
          exerciseId: "general-questions"
        }}
      />
    </motion.div>
  );
};

export default Dashboard; 