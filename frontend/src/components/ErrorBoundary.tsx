import React from 'react';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { motion } from 'framer-motion';

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-theme-background p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-md w-full text-center"
      >
        <div className="bg-theme-surface border border-theme-border rounded-lg p-8 shadow-lg">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </motion.div>

          <h1 className="text-2xl font-bold text-theme-text mb-4">
            Oops! Something went wrong
          </h1>

          <p className="text-theme-text-secondary mb-6">
            We encountered an unexpected error. Don't worry, it's not your fault.
          </p>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
            <h3 className="text-sm font-medium text-red-800 mb-2">Error Details:</h3>
            <code className="text-xs text-red-700 break-all">
              {error.message}
            </code>
          </div>

          <div className="space-y-3">
            <button
              onClick={resetErrorBoundary}
              className="w-full flex items-center justify-center space-x-2 bg-theme-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Try Again</span>
            </button>

            <button
              onClick={handleGoHome}
              className="w-full flex items-center justify-center space-x-2 bg-theme-surface border border-theme-border text-theme-text py-3 px-4 rounded-lg hover:bg-theme-border transition-colors"
            >
              <Home className="w-4 h-4" />
              <span>Go Home</span>
            </button>
          </div>

          <p className="text-xs text-theme-text-secondary mt-6">
            If this problem persists, please contact support.
          </p>
        </div>
      </motion.div>
    </div>
  );
};

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({
  children,
  fallback = ErrorFallback,
  onError,
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // 记录错误到控制台
    console.error('Error caught by boundary:', error, errorInfo);
    
    // 可以在这里发送错误到监控服务
    // 例如: Sentry, LogRocket, 等
    
    // 调用自定义错误处理函数
    onError?.(error, errorInfo);
  };

  return (
    <ReactErrorBoundary
      FallbackComponent={fallback}
      onError={handleError}
      onReset={() => {
        // 清除任何错误状态
        window.location.reload();
      }}
    >
      {children}
    </ReactErrorBoundary>
  );
};

export default ErrorBoundary;
