import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, CheckCircle, Clock, Target, BookOpen } from 'lucide-react';
import CodeEditor from './CodeEditor';
import { useAuth } from '../contexts/AuthContext';

interface Exercise {
  id: string;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  timeLimit: number; // minutes
  points: number;
  initialCode: string;
  expectedOutput: string;
  hints: string[];
  testCases: TestCase[];
}

interface TestCase {
  input: string;
  expectedOutput: string;
  description: string;
}

const ExercisePage: React.FC = () => {
  const { exerciseId } = useParams<{ exerciseId: string }>();
  const navigate = useNavigate();
  const [currentCode, setCurrentCode] = useState('');
  const [showHints, setShowHints] = useState(false);
  const [completedTestCases] = useState<number>(0);
  const [exercise, setExercise] = useState<Exercise | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const { token } = useAuth();

  useEffect(() => {
    const fetchExercise = async () => {
      if (!exerciseId) return;

      try {
        // 首先尝试从数据库获取练习
        let exerciseData = null;

        if (token) {
          try {
            const response = await fetch(`http://localhost:8000/api/exercises/${exerciseId}`, {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              const result = await response.json();
              exerciseData = result;
            }
          } catch (authError) {
            console.log('认证API获取练习失败，使用demo API');
          }
        }

        // 如果认证API失败，使用demo API
        if (!exerciseData) {
          const demoResponse = await fetch(`http://localhost:8000/api/demo/exercise/${exerciseId}`);
          if (demoResponse.ok) {
            exerciseData = await demoResponse.json();
          }
        }

        if (exerciseData) {
          setExercise(exerciseData);
          setCurrentCode(exerciseData.initialCode || '');
        } else {
          setError('Exercise not found');
        }
      } catch (err) {
        console.error('获取练习失败:', err);
        setError('Failed to load exercise');
      } finally {
        setLoading(false);
      }
    };

    fetchExercise();
  }, [exerciseId, token]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'hard': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const handleCodeChange = (code: string) => {
    setCurrentCode(code);
  };

  const handleCodeSave = (code: string) => {
    console.log('Saving code:', code);
    // In real app, save to backend
  };

  const handleSubmit = async () => {
    if (!exercise || !currentCode.trim()) return;
    
    setSubmitting(true);
    try {
      if (token) {
        // 认证用户提交到真实API
        const response = await fetch(`http://localhost:8000/api/exercises/${exerciseId}/submit`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            code: currentCode,
            exercise_id: exerciseId
          })
        });

        if (response.ok) {
          const result = await response.json();
          alert(`Exercise submitted successfully! Score: ${result.score || 'N/A'}`);
        } else {
          alert('Submission failed. Please try again.');
        }
      } else {
        // 演示模式
        console.log('Demo submission:', currentCode);
        alert('Exercise submitted! (Demo mode)');
      }
    } catch (error) {
      console.error('Submission error:', error);
      alert('Submission failed. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !exercise) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4 py-6">
          <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error || 'Exercise not found'}</span>
          </div>
          <button
            onClick={() => navigate(-1)}
            className="mt-4 flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            <ArrowLeft size={20} />
            <span>Back to Unit</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors mb-4"
          >
            <ArrowLeft size={20} />
            <span>Back to Unit</span>
          </button>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {exercise?.title}
                </h1>
                <div className="flex items-center space-x-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise?.difficulty || '')}`}>
                    {exercise?.difficulty.charAt(0).toUpperCase() + exercise?.difficulty.slice(1)}
                  </span>
                  <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                    <Clock size={16} />
                    <span className="text-sm">{exercise?.timeLimit} minutes</span>
                  </div>
                  <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                    <Target size={16} />
                    <span className="text-sm">{exercise?.points} points</span>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              {exercise?.description}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Code Editor */}
          <div className="lg:col-span-2">
            <div className="mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Code Editor
              </h2>
            </div>
            <CodeEditor
              initialCode={exercise?.initialCode || ''}
              onCodeChange={handleCodeChange}
              onSave={handleCodeSave}
              height="500px"
            />
            
            {/* Submit Button */}
            <div className="mt-4 flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={submitting || !currentCode.trim()}
                className="px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors flex items-center space-x-2"
              >
                <CheckCircle size={20} />
                <span>{submitting ? 'Submitting...' : 'Submit Solution'}</span>
              </button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Expected Output */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Expected Output
              </h3>
              <div className="bg-gray-900 text-green-400 font-mono text-sm p-3 rounded">
                <pre>{exercise?.expectedOutput}</pre>
              </div>
            </div>

            {/* Test Cases */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Test Cases
              </h3>
              <div className="space-y-3">
                {exercise?.testCases.map((testCase, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-600 rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Test Case {index + 1}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {completedTestCases > index ? '✅ Passed' : '⏳ Pending'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {testCase.description}
                    </p>
                    <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded text-xs font-mono">
                      Expected: {testCase.expectedOutput}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Hints */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Hints
                </h3>
                <button
                  onClick={() => setShowHints(!showHints)}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                >
                  {showHints ? 'Hide' : 'Show'} Hints
                </button>
              </div>
              
              {showHints && (
                <div className="space-y-2">
                  {exercise?.hints.map((hint, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <BookOpen size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {hint}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExercisePage; 