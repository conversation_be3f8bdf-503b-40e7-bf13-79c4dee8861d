import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ExternalLink,
  Play,
  FileText,
  Code,
  BookOpen,
  Clock,
  Star,
  Loader2,
  RefreshCw
} from 'lucide-react';

interface ExternalResource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  source: string;
  channel?: string;
  thumbnail?: string;
  difficulty?: string;
  estimated_time?: string;
}

interface ExternalContent {
  videos: ExternalResource[];
  articles: ExternalResource[];
  exercises: ExternalResource[];
}

interface ExternalResourcesPanelProps {
  unitId: string;
  unitTitle: string;
  isOpen: boolean;
  onClose: () => void;
}

const ExternalResourcesPanel: React.FC<ExternalResourcesPanelProps> = ({
  unitId,
  unitTitle,
  isOpen,
  onClose
}) => {
  const [content, setContent] = useState<ExternalContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'videos' | 'articles' | 'exercises'>('videos');

  useEffect(() => {
    if (isOpen && unitId) {
      fetchExternalContent();
    }
  }, [isOpen, unitId]);

  const fetchExternalContent = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`http://localhost:8000/api/demo/external-content/unit/${unitId}`);
      const data = await response.json();
      
      if (data.success) {
        setContent(data.content);
      } else {
        setError(data.message || 'Failed to fetch external content');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error fetching external content:', err);
    } finally {
      setLoading(false);
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'video': return Play;
      case 'article': return FileText;
      case 'interactive_exercise': 
      case 'coding_challenge': return Code;
      default: return BookOpen;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'video': return 'bg-red-100 text-red-600';
      case 'article': return 'bg-green-100 text-green-600';
      case 'interactive_exercise': 
      case 'coding_challenge': return 'bg-purple-100 text-purple-600';
      default: return 'bg-blue-100 text-blue-600';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
      case 'beginner': return 'bg-green-100 text-green-600';
      case 'medium':
      case 'intermediate': return 'bg-yellow-100 text-yellow-600';
      case 'hard':
      case 'advanced': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const renderResourceCard = (resource: ExternalResource) => {
    const IconComponent = getResourceIcon(resource.type);
    
    return (
      <motion.div
        key={resource.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-300"
      >
        <div className="flex items-start space-x-3">
          <div className={`p-2 rounded-lg ${getTypeColor(resource.type)}`}>
            <IconComponent className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <h4 className="font-medium text-gray-900 line-clamp-2">{resource.title}</h4>
              <span className="text-xs px-2 py-1 bg-blue-500 text-white rounded-full ml-2 flex-shrink-0">
                {resource.source}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{resource.description}</p>
            
            {/* 元数据 */}
            <div className="flex items-center space-x-3 mt-2 text-xs text-gray-500">
              {resource.channel && (
                <span className="flex items-center space-x-1">
                  <span>🎥</span>
                  <span className="truncate max-w-24">{resource.channel}</span>
                </span>
              )}
              {resource.difficulty && (
                <span className={`px-2 py-1 rounded-full ${getDifficultyColor(resource.difficulty)}`}>
                  {resource.difficulty}
                </span>
              )}
              {resource.estimated_time && (
                <span className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{resource.estimated_time}</span>
                </span>
              )}
            </div>
            
            <a
              href={resource.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary-500 hover:text-primary-600 text-sm mt-3 inline-flex items-center space-x-1 font-medium"
            >
              <span>
                {resource.type === 'video' ? 'Watch Video' :
                 resource.type.includes('exercise') || resource.type.includes('challenge') ? 'Start Exercise' :
                 'Read Article'}
              </span>
              <ExternalLink className="w-3 h-3" />
            </a>
          </div>
        </div>
        
        {/* 视频缩略图 */}
        {resource.type === 'video' && resource.thumbnail && (
          <div className="mt-3">
            <img 
              src={resource.thumbnail} 
              alt={resource.title}
              className="w-full h-32 object-cover rounded-lg"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </div>
        )}
      </motion.div>
    );
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-500 to-blue-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">External Learning Resources</h2>
              <p className="text-primary-100 mt-1">{unitTitle} - Additional Content from Top Educational Platforms</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={fetchExternalContent}
                disabled={loading}
                className="p-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
              >
                <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={onClose}
                className="text-2xl hover:text-primary-200 transition-colors"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 px-6">
          <div className="flex space-x-8">
            {(['videos', 'articles', 'exercises'] as const).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
                {content && (
                  <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                    {content[tab]?.length || 0}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="text-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-4" />
              <p className="text-gray-600">Loading external content...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">Error: {error}</p>
              <button
                onClick={fetchExternalContent}
                className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
              >
                Try Again
              </button>
            </div>
          ) : content ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {content[activeTab]?.length > 0 ? (
                content[activeTab].map(renderResourceCard)
              ) : (
                <div className="col-span-full text-center py-12">
                  <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No {activeTab} available for this unit.</p>
                </div>
              )}
            </div>
          ) : null}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <p>Resources are fetched from YouTube, Oracle Documentation, GeeksforGeeks, CodeHS, and more.</p>
            <div className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span>Curated for APCSA students</span>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ExternalResourcesPanel; 