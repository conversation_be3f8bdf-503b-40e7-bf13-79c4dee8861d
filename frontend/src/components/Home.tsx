import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Code2, 
  Brain, 
  BookOpen, 
  Trophy, 
  ArrowRight,
  Database,
  Zap,
  Users,
  Star
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const Home: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const { currentTheme } = useTheme();

  const features = [
    {
      icon: BookOpen,
      title: 'Interactive Lessons',
      description: 'Learn APCSA concepts with interactive lessons and real-time coding practice',
      color: 'text-blue-500'
    },
    {
      icon: Brain,
      title: 'AI-Powered Assistant',
      description: 'Get instant help from our AI tutor that understands your coding challenges',
      color: 'text-purple-500'
    },
    {
      icon: Code2,
      title: 'Code Editor',
      description: 'Practice Java programming with our built-in IDE and instant feedback',
      color: 'text-green-500'
    },
    {
      icon: Database,
      title: 'Resource Library',
      description: 'Access curated external resources from top educational platforms',
      color: 'text-orange-500'
    },
    {
      icon: Trophy,
      title: 'Progress Tracking',
      description: 'Monitor your learning journey with detailed analytics and achievements',
      color: 'text-yellow-500'
    },
    {
      icon: Users,
      title: 'Community Learning',
      description: 'Join a community of students preparing for the AP Computer Science A exam',
      color: 'text-indigo-500'
    }
  ];

  const stats = [
    { number: '1,000+', label: 'Students Enrolled' },
    { number: '95%', label: 'Pass Rate' },
    { number: '500+', label: 'Practice Problems' },
    { number: '24/7', label: 'AI Support' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative px-6 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Master{' '}
              <span className="gradient-text">AP Computer Science A</span>
              {' '}with AI
            </h1>
            <p className="text-xl text-theme-text-secondary mb-8 max-w-2xl mx-auto">
              Interactive lessons, AI-powered assistance, and comprehensive practice problems 
              to help you excel in your AP Computer Science A journey.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            {isAuthenticated ? (
              <Link
                to="/dashboard"
                className="group flex items-center space-x-2 px-8 py-4 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-all duration-300 text-lg font-medium"
              >
                <span>Continue Learning</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            ) : (
              <>
                <Link
                  to="/register"
                  className="group flex items-center space-x-2 px-8 py-4 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-all duration-300 text-lg font-medium"
                >
                  <span>Get Started Free</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  to="/login"
                  className="px-8 py-4 border-2 border-primary-500 text-primary-500 rounded-lg hover:bg-primary-50 transition-colors text-lg font-medium"
                >
                  Sign In
                </Link>
              </>
            )}
          </motion.div>

          {isAuthenticated && user && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg inline-block"
            >
              <p className="text-green-700">
                Welcome back, {user.full_name}! Ready to continue your APCSA journey?
              </p>
            </motion.div>
          )}
        </div>

        {/* Floating Animation Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 rounded-full opacity-20"
              style={{
                backgroundColor: currentTheme.colors.primary,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, 20, -20],
                opacity: [0.2, 0.8, 0.2],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-theme-surface/50">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-primary-500 mb-2">
                  {stat.number}
                </div>
                <div className="text-theme-text-secondary">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-4">
              Everything You Need to <span className="gradient-text">Succeed</span>
            </h2>
            <p className="text-xl text-theme-text-secondary max-w-2xl mx-auto">
              Our comprehensive platform provides all the tools and resources you need 
              to master AP Computer Science A.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="group p-6 bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300"
              >
                <div className={`inline-flex p-3 rounded-lg bg-gray-50 group-hover:bg-primary-50 transition-colors ${feature.color}`}>
                  <feature.icon className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold mt-4 mb-2">{feature.title}</h3>
                <p className="text-theme-text-secondary">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-primary-500 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h2 className="text-4xl font-bold mb-4">
              Ready to Ace Your AP Computer Science A Exam?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of students who have successfully prepared for their 
              AP Computer Science A exam with our AI-powered platform.
            </p>
            
            {!isAuthenticated && (
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/register"
                  className="group flex items-center justify-center space-x-2 px-8 py-4 bg-white text-primary-600 rounded-lg hover:bg-gray-50 transition-all duration-300 text-lg font-medium"
                >
                  <span>Start Learning Today</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  to="/resources"
                  className="px-8 py-4 border-2 border-white text-white rounded-lg hover:bg-white/10 transition-colors text-lg font-medium"
                >
                  Explore Resources
                </Link>
              </div>
            )}

            <div className="mt-8 flex items-center justify-center space-x-1 text-sm opacity-75">
              <Star className="w-4 h-4 fill-current" />
              <Star className="w-4 h-4 fill-current" />
              <Star className="w-4 h-4 fill-current" />
              <Star className="w-4 h-4 fill-current" />
              <Star className="w-4 h-4 fill-current" />
              <span className="ml-2">Trusted by 1,000+ students</span>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home; 