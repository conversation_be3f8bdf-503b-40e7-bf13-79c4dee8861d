import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Code2, 
  Brain, 
  BookOpen, 
  Trophy, 
  User, 
  Terminal,
  Database,
  Palette,
  LogOut,
  LogIn,
  Menu,
  X
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import ThemeSelector from './ThemeSelector';
import AuthModal from './Auth/AuthModal';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { isAuthenticated, user, signOut } = useAuth();
  const { currentTheme } = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navItems = [
    { path: '/', icon: Terminal, label: 'Home' },
    { path: '/dashboard', icon: BookOpen, label: 'Dashboard', protected: true },
    { path: '/units', icon: BookOpen, label: 'Units', protected: true },
    { path: '/resources', icon: Database, label: 'Resources' },
    { path: '/quiz', icon: Brain, label: 'Quiz', protected: true },
    { path: '/profile', icon: User, label: 'Profile', protected: true },
  ];

  const handleSignOut = () => {
    signOut();
    navigate('/');
  };

  const handleAuthRequired = () => {
    setShowAuthModal(true);
  };

  return (
    <div className="min-h-screen matrix-bg text-theme-text">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-theme-border">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2 group">
              <div className="relative">
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <Code2 className="w-8 h-8 text-theme-primary group-hover:text-theme-secondary transition-colors" />
                </motion.div>
                <div 
                  className="absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse"
                  style={{ backgroundColor: currentTheme.colors.success }}
                ></div>
              </div>
              <span className="font-bold text-xl gradient-text">APCSA.AI</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              {navItems.map((item) => {
                const isActive = location.pathname === item.path;
                const canAccess = !item.protected || isAuthenticated;
                
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    onClick={(e) => {
                      if (item.protected && !isAuthenticated) {
                        e.preventDefault();
                        handleAuthRequired();
                      }
                    }}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors group ${
                      isActive 
                        ? 'bg-theme-primary text-white' 
                        : canAccess 
                          ? 'hover:bg-theme-surface text-theme-text-primary' 
                          : 'opacity-50 cursor-not-allowed text-theme-text-secondary'
                    }`}
                  >
                    <item.icon className="w-5 h-5 group-hover:text-theme-primary transition-colors" />
                    <span className="font-medium">{item.label}</span>
                    {item.protected && !isAuthenticated && (
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    )}
                  </Link>
                );
              })}
            </div>

            {/* Right Side */}
            <div className="flex items-center space-x-4">
              {/* Theme Selector */}
              <ThemeSelector />
              
              {/* Auth Section */}
              {isAuthenticated ? (
                <div className="flex items-center space-x-3">
                  {/* User Info */}
                  <div className="hidden sm:flex items-center space-x-2 text-sm">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-primary-600" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{user?.full_name}</div>
                      <div className="text-xs text-theme-text-secondary capitalize">{user?.role}</div>
                    </div>
                  </div>
                  
                  {/* Sign Out Button */}
                  <button
                    onClick={handleSignOut}
                    className="flex items-center space-x-2 px-3 py-2 text-sm bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors"
                    title="Sign Out"
                  >
                    <LogOut className="w-4 h-4" />
                    <span className="hidden sm:inline">Sign Out</span>
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowAuthModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                >
                  <LogIn className="w-4 h-4" />
                  <span>Sign In</span>
                </button>
              )}

              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden p-2 rounded-lg hover:bg-theme-surface transition-colors"
              >
                {mobileMenuOpen ? (
                  <X className="w-6 h-6" />
                ) : (
                  <Menu className="w-6 h-6" />
                )}
              </button>
              
              {/* Status Indicator */}
              <div 
                className="w-2 h-2 rounded-full animate-pulse"
                style={{ backgroundColor: currentTheme.colors.success }}
              ></div>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden border-t border-theme-border py-4"
            >
              <div className="space-y-2">
                {navItems.map((item) => {
                  const isActive = location.pathname === item.path;
                  const canAccess = !item.protected || isAuthenticated;
                  
                  return (
                    <Link
                      key={item.path}
                      to={item.path}
                      onClick={(e) => {
                        if (item.protected && !isAuthenticated) {
                          e.preventDefault();
                          handleAuthRequired();
                        } else {
                          setMobileMenuOpen(false);
                        }
                      }}
                      className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors ${
                        isActive 
                          ? 'bg-theme-primary text-white' 
                          : canAccess 
                            ? 'hover:bg-theme-surface text-theme-text-primary' 
                            : 'opacity-50 cursor-not-allowed text-theme-text-secondary'
                      }`}
                    >
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.label}</span>
                      {item.protected && !isAuthenticated && (
                        <div className="w-2 h-2 bg-yellow-500 rounded-full ml-auto"></div>
                      )}
                    </Link>
                  );
                })}
              </div>
            </motion.div>
          )}
        </div>
      </nav>

      {/* Main Content */}
      <main className="pt-16">
        {children}
      </main>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={() => setShowAuthModal(false)}
      />
    </div>
  );
};

export default Layout; 