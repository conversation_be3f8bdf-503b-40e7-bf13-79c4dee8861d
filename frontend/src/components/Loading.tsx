import React from 'react';
import { motion } from 'framer-motion';
import { Code2, <PERSON>, <PERSON>O<PERSON> } from 'lucide-react';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
  variant?: 'spinner' | 'dots' | 'pulse' | 'code';
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  text,
  fullScreen = false,
  variant = 'spinner',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  const containerClasses = fullScreen
    ? 'fixed inset-0 flex items-center justify-center bg-theme-background bg-opacity-80 backdrop-blur-sm z-50'
    : 'flex items-center justify-center p-4';

  const renderSpinner = () => (
    <motion.div
      className={`border-2 border-theme-border border-t-theme-primary rounded-full ${sizeClasses[size]}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    />
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className={`bg-theme-primary rounded-full ${
            size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-2 h-2' : 'w-3 h-3'
          }`}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <motion.div
      className={`bg-theme-primary rounded-full ${sizeClasses[size]}`}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.5, 1, 0.5],
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
      }}
    />
  );

  const renderCode = () => {
    const icons = [Code2, Brain, BookOpen];
    
    return (
      <div className="flex space-x-2">
        {icons.map((Icon, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -10, 0],
              opacity: [0.3, 1, 0.3],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.3,
            }}
          >
            <Icon className={`text-theme-primary ${sizeClasses[size]}`} />
          </motion.div>
        ))}
      </div>
    );
  };

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'code':
        return renderCode();
      default:
        return renderSpinner();
    }
  };

  return (
    <div className={containerClasses}>
      <div className="text-center">
        <div className="mb-4">{renderLoader()}</div>
        {text && (
          <motion.p
            className="text-theme-text-secondary text-sm"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            {text}
          </motion.p>
        )}
      </div>
    </div>
  );
};

// 专用加载组件
export const PageLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <Loading fullScreen variant="code" text={text} size="lg" />
);

export const ButtonLoading: React.FC = () => (
  <Loading variant="spinner" size="sm" />
);

export const SectionLoading: React.FC<{ text?: string }> = ({ text }) => (
  <Loading variant="dots" text={text} size="md" />
);

export default Loading;
