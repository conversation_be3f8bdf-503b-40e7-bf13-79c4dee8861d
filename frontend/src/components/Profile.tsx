import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  User, 
  Trophy, 
  Target, 
  Calendar,
  Code2,
  Brain,
  BookOpen,
  Zap,
  Star,
  TrendingUp,
  Award,
  Clock,
  ChevronRight,
  Settings,
  Download
} from 'lucide-react';
import AIAssistant from './AIAssistant';

const Profile: React.FC = () => {
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false);
  
  // TODO: 从API获取成就数据
  const achievements: any[] = [];

  // TODO: 从API获取学习统计数据
  const learningStats = [
    { label: 'Total Study Time', value: '0', unit: 'hours', icon: Clock, color: 'text-blue-500' },
    { label: 'Problems Solved', value: '0', unit: 'problems', icon: Code2, color: 'text-green-500' },
    { label: 'Average Score', value: '0', unit: '%', icon: Target, color: 'text-purple-500' },
    { label: 'Current Streak', value: '0', unit: 'days', icon: Zap, color: 'text-yellow-500' },
  ];

  // TODO: 从API获取最近活动数据
  const recentActivity: any[] = [];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen p-6"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="text-4xl font-bold gradient-text mb-2"
          >
            Student Profile
          </motion.h1>
          <p className="text-dark-400">Track your learning journey and achievements</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Profile Info */}
          <div className="lg:col-span-1 space-y-6">
            {/* Profile Card */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="text-center mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="w-12 h-12 text-white" />
                </div>
                <h2 className="text-xl font-bold text-dark-900">{user?.full_name || 'Student'}</h2>
                <p className="text-dark-500">{user?.role || 'APCSA Student'}</p>
                <div className="flex items-center justify-center space-x-4 mt-4 text-sm">
                  <div className="flex items-center text-green-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Level 7
                  </div>
                  <div className="text-dark-500">•</div>
                  <div className="text-dark-500">Joined Jan 2024</div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-dark-600">Progress to Level 8</span>
                  <span className="text-sm text-dark-600">750/1000 XP</span>
                </div>
                <div className="w-full bg-dark-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-primary-500 to-purple-500 h-2 rounded-full transition-all duration-500" style={{ width: '75%' }} />
                </div>
              </div>

              <button className="w-full mt-6 flex items-center justify-center space-x-2 bg-dark-100 hover:bg-dark-200 text-dark-700 py-2 rounded-lg transition-colors">
                <Settings className="w-4 h-4" />
                <span>Edit Profile</span>
              </button>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="glass-effect rounded-lg p-6"
            >
              <h3 className="text-lg font-bold text-dark-900 mb-4">Quick Stats</h3>
              <div className="space-y-4">
                {learningStats.map((stat, index) => (
                  <div key={stat.label} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <stat.icon className={`w-5 h-5 ${stat.color}`} />
                      <span className="text-sm text-dark-600">{stat.label}</span>
                    </div>
                    <div className="text-right">
                      <span className="font-bold text-dark-900">{stat.value}</span>
                      <span className="text-xs text-dark-500 ml-1">{stat.unit}</span>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Right Column - Achievements & Activity */}
          <div className="lg:col-span-2 space-y-6">
            {/* Achievements */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-dark-900 flex items-center">
                  <Trophy className="w-6 h-6 mr-2 text-yellow-500" />
                  Achievements
                </h3>
                <span className="text-sm text-dark-500">3 of 6 unlocked</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.id}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      achievement.unlocked 
                        ? 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200 hover:border-yellow-300' 
                        : 'bg-dark-100 border-dark-200 opacity-60'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${
                        achievement.unlocked ? 'bg-yellow-100' : 'bg-dark-200'
                      }`}>
                        <achievement.icon className={`w-5 h-5 ${
                          achievement.unlocked ? 'text-yellow-600' : 'text-dark-400'
                        }`} />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className={`font-medium ${
                            achievement.unlocked ? 'text-dark-900' : 'text-dark-500'
                          }`}>
                            {achievement.title}
                          </h4>
                          {achievement.unlocked && (
                            <Award className="w-4 h-4 text-yellow-500" />
                          )}
                        </div>
                        
                        <p className={`text-xs ${
                          achievement.unlocked ? 'text-dark-600' : 'text-dark-400'
                        }`}>
                          {achievement.description}
                        </p>
                        
                        {achievement.unlocked && achievement.date && (
                          <p className="text-xs text-yellow-600 mt-1">
                            Unlocked {new Date(achievement.date).toLocaleDateString()}
                          </p>
                        )}
                        
                        {!achievement.unlocked && achievement.progress && (
                          <div className="mt-2">
                            <div className="flex items-center justify-between text-xs mb-1">
                              <span className="text-dark-500">Progress</span>
                              <span className="text-dark-500">{achievement.progress}%</span>
                            </div>
                            <div className="w-full bg-dark-200 rounded-full h-1">
                              <div 
                                className="bg-primary-500 h-1 rounded-full transition-all"
                                style={{ width: `${achievement.progress}%` }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Recent Activity */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-dark-900 flex items-center">
                  <TrendingUp className="w-6 h-6 mr-2 text-primary-500" />
                  Recent Activity
                </h3>
                <button className="text-primary-500 hover:text-primary-600 text-sm font-medium flex items-center">
                  Download Report
                  <Download className="w-4 h-4 ml-1" />
                </button>
              </div>

              <div className="space-y-3">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="flex items-center justify-between p-4 bg-dark-100 rounded-lg hover:bg-dark-200 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-4 h-4 text-dark-400" />
                      <div>
                        <div className="font-medium text-dark-900">{activity.activity}</div>
                        <div className="text-xs text-dark-500">{activity.date}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {activity.score && (
                        <span className={`text-sm font-bold ${
                          activity.score >= 90 ? 'text-green-500' : 
                          activity.score >= 80 ? 'text-yellow-500' : 'text-red-500'
                        }`}>
                          {activity.score}%
                        </span>
                      )}
                      {activity.duration && (
                        <span className="text-sm text-dark-500">{activity.duration}</span>
                      )}
                      <ChevronRight className="w-4 h-4 text-dark-400" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Performance Chart Placeholder */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="glass-effect rounded-lg p-6"
            >
              <h3 className="text-xl font-bold text-dark-900 mb-4 flex items-center">
                <TrendingUp className="w-6 h-6 mr-2 text-primary-500" />
                Performance Trends
              </h3>
              
              <div className="h-64 bg-dark-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <TrendingUp className="w-12 h-12 text-dark-400 mx-auto mb-2" />
                  <p className="text-dark-500">Performance chart coming soon...</p>
                  <p className="text-xs text-dark-400 mt-1">Track your progress over time</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* AI助手浮动按钮 */}
      <motion.button
        onClick={() => setAiAssistantOpen(true)}
        className="fixed bottom-6 right-6 bg-gradient-to-r from-purple-500 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-40"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
      >
        <Brain className="w-6 h-6" />
      </motion.button>

      {/* AI助手组件 */}
      <AIAssistant
        isOpen={aiAssistantOpen}
        onClose={() => setAiAssistantOpen(false)}
        context={{
          topic: "Student Profile & Progress",
          unitId: "profile-page",
          exerciseId: "profile-questions"
        }}
      />
    </motion.div>
  );
};

export default Profile; 