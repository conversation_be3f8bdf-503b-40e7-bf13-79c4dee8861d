import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Code, 
  Clock, 
  TrendingUp
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: string;
  category: 'completion' | 'coding' | 'consistency' | 'mastery';
}

interface WeeklyProgress {
  week: string;
  exercisesCompleted: number;
  timeSpent: number;
  unitsCompleted: number;
}

interface ProgressData {
  totalUnits: number;
  completedUnits: number;
  inProgressUnits: number;
  totalExercises: number;
  completedExercises: number;
  totalTimeSpent: number;
  currentStreak: number;
  achievements: Achievement[];
  weeklyProgress: WeeklyProgress[];
}

const ProgressTracker: React.FC = () => {
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'all'>('week');
  const { token, user } = useAuth();

  useEffect(() => {
    const fetchProgressData = async () => {
      try {
        if (token && user) {
          // 获取用户总体进度
          const progressResponse = await fetch('http://localhost:8000/api/user/progress', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (progressResponse.ok) {
            const progressResult = await progressResponse.json();
            setProgressData(progressResult.data);
            return;
          }
        }
        
        // 如果没有token或API失败，使用默认数据
        await fetchCalculatedProgress();
      } catch (err) {
        console.warn('获取进度API失败，使用计算数据:', err);
        await fetchCalculatedProgress();
      }
    };

    const fetchCalculatedProgress = async () => {
      try {
        let calculatedData: ProgressData = {
          totalUnits: 10, // 默认10个单元
          completedUnits: 0,
          inProgressUnits: 0,
          totalExercises: 50, // 10个单元 * 5个练习
          completedExercises: 0,
          totalTimeSpent: 0,
          currentStreak: 0,
          achievements: [],
          weeklyProgress: [
            { week: 'Week 1', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 },
            { week: 'Week 2', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 },
            { week: 'Week 3', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 },
            { week: 'Week 4', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 }
          ]
        };

        // 尝试获取单元数据来更新总数
        try {
          const unitsResponse = await fetch('http://localhost:8000/api/public/units');
          if (unitsResponse.ok) {
            const unitsData = await unitsResponse.json();
            calculatedData.totalUnits = unitsData.length;
            calculatedData.totalExercises = unitsData.length * 5;
          }
        } catch (unitsError) {
          console.warn('获取单元数据失败，使用默认值');
        }

        setProgressData(calculatedData);
      } catch (err) {
        console.error('计算进度数据失败:', err);
        // 设置最基本的默认数据
        setProgressData({
          totalUnits: 10,
          completedUnits: 0,
          inProgressUnits: 0,
          totalExercises: 50,
          completedExercises: 0,
          totalTimeSpent: 0,
          currentStreak: 0,
          achievements: [],
          weeklyProgress: [
            { week: 'Week 1', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 },
            { week: 'Week 2', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 },
            { week: 'Week 3', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 },
            { week: 'Week 4', exercisesCompleted: 0, timeSpent: 0, unitsCompleted: 0 }
          ]
        });
      }
    };

    fetchProgressData();
  }, [token, user]);

  if (!progressData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  const completionPercentage = Math.round((progressData.completedUnits / progressData.totalUnits) * 100);
  const exerciseCompletionPercentage = Math.round((progressData.completedExercises / progressData.totalExercises) * 100);

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-dark-100 border border-dark-200 rounded-lg shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-dark-400">Units Completed</p>
              <p className="text-2xl font-bold text-dark-900">
                {progressData.completedUnits}/{progressData.totalUnits}
              </p>
              <p className="text-sm text-green-600">
                {completionPercentage}% complete
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-dark-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${completionPercentage}%` }}
              ></div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-dark-100 border border-dark-200 rounded-lg shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-dark-400">Exercises Done</p>
              <p className="text-2xl font-bold text-dark-900">
                {progressData.completedExercises}/{progressData.totalExercises}
              </p>
              <p className="text-sm text-green-600">
                {exerciseCompletionPercentage}% complete
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Code className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-dark-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${exerciseCompletionPercentage}%` }}
              ></div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-dark-100 border border-dark-200 rounded-lg shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-dark-400">Time Spent</p>
              <p className="text-2xl font-bold text-dark-900">
                {Math.floor(progressData.totalTimeSpent / 60)}h {progressData.totalTimeSpent % 60}m
              </p>
              <p className="text-sm text-blue-600">
                This month
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-dark-100 border border-dark-200 rounded-lg shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-dark-400">Current Streak</p>
              <p className="text-2xl font-bold text-dark-900">
                {progressData.currentStreak} days
              </p>
              <p className="text-sm text-orange-600">
                {progressData.currentStreak > 0 ? 'Keep it up! 🔥' : 'Start your journey! 📚'}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Weekly Progress Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-dark-100 border border-dark-200 rounded-lg shadow-lg p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-dark-900">
            Weekly Progress
          </h3>
          <div className="flex space-x-2">
            {(['week', 'month', 'all'] as const).map((timeframe) => (
              <button
                key={timeframe}
                onClick={() => setSelectedTimeframe(timeframe)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  selectedTimeframe === timeframe
                    ? 'bg-primary-100 text-primary-600'
                    : 'text-dark-400 hover:text-dark-900'
                }`}
              >
                {timeframe.charAt(0).toUpperCase() + timeframe.slice(1)}
              </button>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          {progressData.weeklyProgress.map((week, index) => (
            <div key={week.week} className="flex items-center space-x-4">
              <div className="w-16 text-sm font-medium text-dark-400">
                {week.week}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-dark-600">
                    {week.exercisesCompleted} exercises
                  </span>
                  <span className="text-sm text-dark-600">
                    {Math.floor(week.timeSpent / 60)}h {week.timeSpent % 60}m
                  </span>
                </div>
                <div className="w-full bg-dark-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min((week.exercisesCompleted / 10) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Achievements */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-dark-100 border border-dark-200 rounded-lg shadow-lg p-6"
      >
        <h3 className="text-lg font-semibold text-dark-900 mb-6">
          Recent Achievements
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {progressData.achievements.length > 0 ? (
            progressData.achievements.map((achievement, index) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                className="flex items-center space-x-3 p-4 bg-gradient-to-r from-primary-100/20 to-primary-200/20 rounded-lg border border-primary-200/30"
              >
                <div className="text-2xl">{achievement.icon}</div>
                <div>
                  <h4 className="font-medium text-dark-900">
                    {achievement.title}
                  </h4>
                  <p className="text-sm text-dark-600">
                    {achievement.description}
                  </p>
                  <p className="text-xs text-dark-500 mt-1">
                    {new Date(achievement.unlockedAt).toLocaleDateString()}
                  </p>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="col-span-full text-center py-8 text-dark-400">
              <div className="text-4xl mb-4">🏆</div>
              <p className="text-lg font-medium text-dark-600 mb-2">No achievements yet</p>
              <p className="text-sm">Start learning to unlock your first achievement!</p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default ProgressTracker; 