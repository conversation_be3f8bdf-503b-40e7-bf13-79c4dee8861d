import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Code2, 
  CheckCircle2, 
  Clock,
  Lightbulb,
  RefreshCw,
  Send,
  Terminal,
  Copy,
  Play
} from 'lucide-react';
import AIAssistant from './AIAssistant';

const Quiz: React.FC = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [userCode, setUserCode] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false);

  // TODO: 从API获取问题数据
  const questions: any[] = [];

  const question = questions[currentQuestion];

  const handleAnswerSelect = (answer: string) => {
    setSelectedAnswer(answer);
  };

  const handleNext = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setUserCode('');
    } else {
      setShowResults(true);
    }
  };

  const copyCode = () => {
    navigator.clipboard.writeText(question.type === 'coding' ? question.template || '' : question.code || '');
  };

  const runCode = () => {
    // Simulate code execution
    console.log('Running code:', userCode);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen p-6"
    >
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="code-terminal mb-4"
          >
            <div className="text-green-400 font-code text-sm mb-2">
              $ apcsa-ai quiz --unit=arrays --difficulty=intermediate
            </div>
            <div className="text-primary-500">
              AI-Generated Quiz • Question {currentQuestion + 1} of {questions.length}
            </div>
          </motion.div>

          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-4xl font-bold gradient-text mb-2"
          >
            Interactive Quiz
          </motion.h1>
          <p className="text-dark-400">Test your Java knowledge with AI-powered questions</p>
        </div>

        {!showResults ? (
          <div className="space-y-6">
            {/* Progress Bar */}
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ delay: 0.3 }}
              className="glass-effect rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-dark-600">Progress</span>
                <span className="text-sm text-dark-500">
                  {Math.round(((currentQuestion + 1) / questions.length) * 100)}%
                </span>
              </div>
              <div className="w-full bg-dark-200 rounded-full h-2">
                <div 
                  className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
                />
              </div>
            </motion.div>

            {/* Question */}
            <motion.div
              key={currentQuestion}
              initial={{ x: 50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Brain className="w-6 h-6 text-primary-500" />
                  <span className="font-bold text-lg">Question {currentQuestion + 1}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-dark-500">
                  <Clock className="w-4 h-4" />
                  <span>5:00</span>
                </div>
              </div>

              <h2 className="text-xl font-medium text-dark-900 mb-4">{question.question}</h2>

              {/* Code Block */}
              {question.code && (
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-dark-600 flex items-center">
                      <Terminal className="w-4 h-4 mr-1" />
                      Code
                    </span>
                    <button 
                      onClick={copyCode}
                      className="text-xs text-primary-500 hover:text-primary-600 flex items-center"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </button>
                  </div>
                  <div className="code-block">
                    <pre className="text-sm overflow-x-auto">
                      <code>{question.code}</code>
                    </pre>
                  </div>
                </div>
              )}

              {/* Multiple Choice Options */}
              {question.type === 'multiple-choice' && question.options && (
                <div className="space-y-3">
                  {question.options.map((option, index) => (
                    <motion.button
                      key={index}
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      onClick={() => handleAnswerSelect(option)}
                      className={`w-full p-4 text-left border-2 rounded-lg transition-all ${
                        selectedAnswer === option
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-dark-200 hover:border-primary-300 bg-dark-100 hover:bg-dark-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-code">{String.fromCharCode(65 + index)}. {option}</span>
                        {selectedAnswer === option && (
                          <CheckCircle2 className="w-5 h-5 text-primary-500" />
                        )}
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}

              {/* Coding Question */}
              {question.type === 'coding' && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-dark-600 flex items-center">
                      <Code2 className="w-4 h-4 mr-1" />
                      Code Editor
                    </span>
                    <div className="flex space-x-2">
                      <button 
                        onClick={runCode}
                        className="text-xs bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded flex items-center"
                      >
                        <Play className="w-3 h-3 mr-1" />
                        Run
                      </button>
                      <button 
                        onClick={copyCode}
                        className="text-xs text-primary-500 hover:text-primary-600 flex items-center"
                      >
                        <Copy className="w-3 h-3 mr-1" />
                        Copy Template
                      </button>
                    </div>
                  </div>
                  
                  <textarea
                    value={userCode || question.template}
                    onChange={(e) => setUserCode(e.target.value)}
                    className="w-full h-64 p-4 bg-dark-100 border border-dark-200 rounded-lg font-code text-sm resize-none focus:outline-none focus:border-primary-500"
                    placeholder="Write your Java code here..."
                  />

                  {question.hint && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <Lightbulb className="w-4 h-4 text-yellow-500 mt-0.5" />
                        <div>
                          <span className="text-sm font-medium text-yellow-800">Hint:</span>
                          <p className="text-sm text-yellow-700 mt-1">{question.hint}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Navigation */}
              <div className="flex justify-between items-center mt-6 pt-6 border-t border-dark-200">
                <button className="text-primary-500 hover:text-primary-600 font-medium flex items-center">
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Generate New Question
                </button>
                
                <button
                  onClick={handleNext}
                  disabled={question.type === 'multiple-choice' ? !selectedAnswer : !userCode.trim()}
                  className="bg-primary-500 hover:bg-primary-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center"
                >
                  {currentQuestion === questions.length - 1 ? (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      Submit Quiz
                    </>
                  ) : (
                    'Next Question'
                  )}
                </button>
              </div>
            </motion.div>
          </div>
        ) : (
          /* Results */
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="glass-effect rounded-lg p-8 text-center"
          >
            <CheckCircle2 className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-dark-900 mb-2">Quiz Completed!</h2>
            <p className="text-dark-600 mb-6">Great job! Your answers have been submitted for AI evaluation.</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-primary-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-primary-500">85%</div>
                <div className="text-sm text-primary-600">Overall Score</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-500">2/3</div>
                <div className="text-sm text-green-600">Correct Answers</div>
              </div>
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-500">A-</div>
                <div className="text-sm text-blue-600">Grade</div>
              </div>
            </div>

            <div className="flex justify-center space-x-4">
              <button className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg font-medium">
                Review Answers
              </button>
              <button 
                onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setSelectedAnswer(null);
                  setUserCode('');
                }}
                className="border border-primary-500 text-primary-500 hover:bg-primary-50 px-6 py-2 rounded-lg font-medium"
              >
                Take Another Quiz
              </button>
            </div>
          </motion.div>
        )}
      </div>

      {/* AI助手浮动按钮 */}
      <motion.button
        onClick={() => setAiAssistantOpen(true)}
        className="fixed bottom-6 right-6 bg-gradient-to-r from-purple-500 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-40"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
      >
        <Brain className="w-6 h-6" />
      </motion.button>

      {/* AI助手组件 */}
      <AIAssistant
        isOpen={aiAssistantOpen}
        onClose={() => setAiAssistantOpen(false)}
        context={{
          topic: `Quiz Question ${currentQuestion + 1}`,
          unitId: "quiz-page",
          exerciseId: `quiz-question-${currentQuestion + 1}`
        }}
      />
    </motion.div>
  );
};

export default Quiz; 