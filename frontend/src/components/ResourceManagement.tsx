import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Download,
  Database,
  RefreshCw,
  Play,
  FileText,
  Code,
  ExternalLink,
  Star,
  CheckCircle,
  AlertCircle,
  Clock,
  Eye,
  <PERSON>Pointer,
  Filter,
  Search,
  Loader2
} from 'lucide-react';

interface StoredResource {
  id: string;
  title: string;
  description: string;
  url: string;
  resource_type: string;
  source_platform: string;
  thumbnail_url?: string;
  channel_name?: string;
  difficulty_level?: string;
  estimated_time?: string;
  unit_ids: string[];
  topic_keywords: string[];
  tags: string[];
  category: string;
  quality_score: number;
  is_verified: boolean;
  is_active: boolean;
  view_count_internal: number;
  click_count: number;
  created_at: string;
  updated_at: string;
}

interface CollectionTask {
  id: string;
  task_type: string;
  target_unit_id: string;
  search_query: string;
  status: string;
  progress: number;
  resources_found: number;
  resources_saved: number;
  resources_skipped: number;
  created_by: string;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

const ResourceManagement: React.FC = () => {
  const [storedResources, setStoredResources] = useState<StoredResource[]>([]);
  const [collectionTasks, setCollectionTasks] = useState<CollectionTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [collecting, setCollecting] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'resources' | 'tasks'>('resources');
  
  // 筛选状态
  const [filterUnitId, setFilterUnitId] = useState<string>('');
  const [filterType, setFilterType] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    loadStoredResources();
    loadCollectionTasks();
  }, []);

  const loadStoredResources = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filterUnitId) params.append('unit_id', filterUnitId);
      if (filterType) params.append('resource_type', filterType);
      params.append('limit', '50');

      const response = await fetch(`http://localhost:8000/api/demo/resources/stored?${params}`);
      const data = await response.json();

      if (data.success) {
        setStoredResources(data.resources);
      }
    } catch (error) {
      console.error('Error loading stored resources:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCollectionTasks = async () => {
    try {
      // TODO: 实现真实的API调用获取任务数据
      const tasks: CollectionTask[] = [];
      setCollectionTasks(tasks);
    } catch (error) {
      console.error('Failed to load collection tasks:', error);
      setCollectionTasks([]);
    }
  };

    } catch (error) {
      console.error('Error loading collection tasks:', error);
    }
  };

  const collectResourcesForUnit = async (unitId: string) => {
    setCollecting(unitId);
    try {
      // TODO: 实现真实的API调用收集资源
      alert('资源收集功能需要后端支持');
    } catch (error) {
      console.error('Error collecting resources:', error);
      alert('收集资源时发生错误');
    } finally {
      setCollecting(null);
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'video': return Play;
      case 'article': return FileText;
      case 'interactive_exercise': 
      case 'coding_challenge': return Code;
      default: return FileText;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'video': return 'bg-red-100 text-red-600';
      case 'article': return 'bg-green-100 text-green-600';
      case 'interactive_exercise': 
      case 'coding_challenge': return 'bg-purple-100 text-purple-600';
      default: return 'bg-blue-100 text-blue-600';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
      case 'beginner': return 'bg-green-100 text-green-600';
      case 'medium':
      case 'intermediate': return 'bg-yellow-100 text-yellow-600';
      case 'hard':
      case 'advanced': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-600';
      case 'running': return 'bg-blue-100 text-blue-600';
      case 'failed': return 'bg-red-100 text-red-600';
      case 'pending': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const filteredResources = storedResources.filter(resource => {
    const matchesSearch = searchQuery === '' || 
      resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.topic_keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesSearch;
  });

  // TODO: 从API获取单元选项
  const unitOptions = [
    { value: '', label: '所有单元' },
  ];

  const typeOptions = [
    { value: '', label: '所有类型' },
    { value: 'video', label: '视频' },
    { value: 'article', label: '文章' },
    { value: 'interactive_exercise', label: '交互练习' },
    { value: 'coding_challenge', label: '编程挑战' },
  ];

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold gradient-text mb-2">资源管理中心</h1>
          <p className="text-gray-600">收集、管理和查看外部学习资源库</p>
        </div>

        {/* Action Bar */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-wrap items-center gap-4">
            <h3 className="text-lg font-semibold text-gray-800">快速收集资源</h3>
            <div className="flex flex-wrap gap-2">
              {unitOptions.slice(1).map((unit) => (
                <button
                  key={unit.value}
                  onClick={() => collectResourcesForUnit(unit.value)}
                  disabled={collecting !== null}
                  className={`px-4 py-2 rounded-lg border transition-all duration-300 ${
                    collecting === unit.value 
                      ? 'bg-primary-500 text-white border-primary-500' 
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-primary-50 hover:border-primary-300'
                  }`}
                >
                  {collecting === unit.value ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>收集中...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Download className="w-4 h-4" />
                      <span>{unit.label.split(':')[0]}</span>
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200 px-6">
            <div className="flex space-x-8">
              {(['resources', 'tasks'] as const).map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab === 'resources' ? (
                    <div className="flex items-center space-x-2">
                      <Database className="w-4 h-4" />
                      <span>已存储资源</span>
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                        {storedResources.length}
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <RefreshCw className="w-4 h-4" />
                      <span>收集任务</span>
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                        {collectionTasks.length}
                      </span>
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === 'resources' ? (
              <div>
                {/* Filters */}
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <div className="flex items-center space-x-2">
                    <Search className="w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="搜索资源..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  
                  <select
                    value={filterUnitId}
                    onChange={(e) => setFilterUnitId(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  >
                    {unitOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>

                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  >
                    {typeOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>

                  <button
                    onClick={loadStoredResources}
                    className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 flex items-center space-x-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>刷新</span>
                  </button>
                </div>

                {/* Resources Grid */}
                {loading ? (
                  <div className="text-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-4" />
                    <p className="text-gray-600">加载资源中...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredResources.map((resource) => {
                      const IconComponent = getResourceIcon(resource.resource_type);
                      return (
                        <motion.div
                          key={resource.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-300"
                        >
                          <div className="flex items-start space-x-3 mb-3">
                            <div className={`p-2 rounded-lg ${getTypeColor(resource.resource_type)}`}>
                              <IconComponent className="w-4 h-4" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-start justify-between">
                                <h4 className="font-medium text-gray-900 text-sm line-clamp-2">{resource.title}</h4>
                                {resource.is_verified && (
                                  <CheckCircle className="w-4 h-4 text-green-500 ml-2 flex-shrink-0" />
                                )}
                              </div>
                              <p className="text-xs text-gray-600 mt-1 line-clamp-2">{resource.description}</p>
                            </div>
                          </div>

                          {/* 资源信息 */}
                          <div className="space-y-2 mb-3">
                            <div className="flex items-center justify-between text-xs">
                              <span className={`px-2 py-1 rounded-full ${getTypeColor(resource.resource_type)}`}>
                                {resource.source_platform}
                              </span>
                                                             <span className={`px-2 py-1 rounded-full ${getDifficultyColor(resource.difficulty_level || 'beginner')}`}>
                                 {resource.difficulty_level || 'beginner'}
                               </span>
                            </div>
                            
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span className="flex items-center space-x-1">
                                <Star className="w-3 h-3" />
                                <span>{resource.quality_score}</span>
                              </span>
                              <span className="flex items-center space-x-1">
                                <Eye className="w-3 h-3" />
                                <span>{resource.view_count_internal}</span>
                              </span>
                              <span className="flex items-center space-x-1">
                                <MousePointer className="w-3 h-3" />
                                <span>{resource.click_count}</span>
                              </span>
                            </div>
                          </div>

                          {/* 标签 */}
                          <div className="flex flex-wrap gap-1 mb-3">
                            {resource.tags.slice(0, 3).map((tag, index) => (
                              <span key={index} className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                                {tag}
                              </span>
                            ))}
                          </div>

                          {/* 缩略图 */}
                          {resource.resource_type === 'video' && resource.thumbnail_url && (
                            <div className="mb-3">
                              <img 
                                src={resource.thumbnail_url} 
                                alt={resource.title}
                                className="w-full h-32 object-cover rounded-lg"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                            </div>
                          )}

                          {/* 操作按钮 */}
                          <a
                            href={resource.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors text-sm"
                          >
                            <ExternalLink className="w-4 h-4" />
                            <span>访问资源</span>
                          </a>
                        </motion.div>
                      );
                    })}
                  </div>
                )}

                {filteredResources.length === 0 && !loading && (
                  <div className="text-center py-12">
                    <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">暂无存储的资源</p>
                    <p className="text-gray-400 text-sm">点击上方的收集按钮开始收集外部资源</p>
                  </div>
                )}
              </div>
            ) : (
              /* Collection Tasks */
              <div className="space-y-4">
                {collectionTasks.map((task) => (
                  <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`px-3 py-1 rounded-full text-sm ${getStatusColor(task.status)}`}>
                          {task.status}
                        </div>
                        <h4 className="font-medium">{task.search_query}</h4>
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(task.created_at).toLocaleString()}
                      </div>
                    </div>

                    {/* 进度条 */}
                    <div className="mb-3">
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                        <span>进度</span>
                        <span>{task.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* 统计信息 */}
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="font-medium text-blue-600">{task.resources_found}</div>
                        <div className="text-gray-500">找到</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-green-600">{task.resources_saved}</div>
                        <div className="text-gray-500">保存</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-yellow-600">{task.resources_skipped}</div>
                        <div className="text-gray-500">跳过</div>
                      </div>
                    </div>

                    {task.error_message && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center space-x-2 text-red-600">
                          <AlertCircle className="w-4 h-4" />
                          <span className="text-sm font-medium">错误信息</span>
                        </div>
                        <p className="text-sm text-red-600 mt-1">{task.error_message}</p>
                      </div>
                    )}
                  </div>
                ))}

                {collectionTasks.length === 0 && (
                  <div className="text-center py-12">
                    <RefreshCw className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">暂无收集任务</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourceManagement; 