import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, Check, Monitor, ChevronDown } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeSelector: React.FC = () => {
  const { currentTheme, setTheme, availableThemes } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const handleThemeChange = (themeId: string) => {
    setTheme(themeId);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 bg-theme-surface border border-theme-border rounded-lg hover:bg-opacity-80 transition-all"
      >
        <Palette className="w-4 h-4 text-theme-primary" />
        <span className="text-sm font-medium text-theme-text">{currentTheme.icon}</span>
        <ChevronDown className={`w-4 h-4 text-theme-text-secondary transition-transform ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* 主题选择面板 */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full right-0 mt-2 w-80 bg-theme-surface border border-theme-border rounded-xl shadow-2xl z-50 overflow-hidden"
            >
              <div className="p-4 border-b border-theme-border">
                <h3 className="text-lg font-bold text-theme-text flex items-center">
                  <Palette className="w-5 h-5 mr-2 text-theme-primary" />
                  选择主题
                </h3>
                <p className="text-sm text-theme-text-secondary mt-1">
                  个性化你的编程体验
                </p>
              </div>

              <div className="max-h-96 overflow-y-auto">
                <div className="grid grid-cols-1 gap-2 p-4">
                  {availableThemes.map((theme) => (
                    <motion.button
                      key={theme.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleThemeChange(theme.id)}
                      className={`relative p-4 rounded-lg border-2 transition-all text-left ${
                        currentTheme.id === theme.id
                          ? 'border-theme-primary'
                          : 'border-theme-border hover:border-theme-primary hover:border-opacity-50'
                      }`}
                      style={{
                        backgroundColor: currentTheme.id === theme.id 
                          ? `${currentTheme.colors.primary}10` 
                          : 'transparent'
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-xl">{theme.icon}</span>
                            <span className="font-semibold text-theme-text">{theme.name}</span>
                            {currentTheme.id === theme.id && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="text-theme-primary"
                              >
                                <Check className="w-4 h-4" />
                              </motion.div>
                            )}
                          </div>
                          <p className="text-sm text-theme-text-secondary mb-3">
                            {theme.description}
                          </p>
                          
                          {/* 主题色彩预览 */}
                          <div className="flex space-x-1">
                            <div 
                              className="w-4 h-4 rounded-full border border-white border-opacity-30"
                              style={{ backgroundColor: theme.colors.primary }}
                            />
                            <div 
                              className="w-4 h-4 rounded-full border border-white border-opacity-30"
                              style={{ backgroundColor: theme.colors.secondary }}
                            />
                            <div 
                              className="w-4 h-4 rounded-full border border-white border-opacity-30"
                              style={{ backgroundColor: theme.colors.accent }}
                            />
                            <div 
                              className="w-4 h-4 rounded-full border border-white border-opacity-30"
                              style={{ backgroundColor: theme.colors.background }}
                            />
                            <div 
                              className="w-4 h-4 rounded-full border border-white border-opacity-30"
                              style={{ backgroundColor: theme.colors.surface }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 悬停效果 */}
                      {currentTheme.id !== theme.id && (
                        <motion.div
                          className="absolute inset-0 bg-theme-primary opacity-0 hover:opacity-5 rounded-lg transition-opacity"
                        />
                      )}
                    </motion.button>
                  ))}
                </div>
              </div>

              <div className="p-4 border-t border-theme-border bg-theme-background bg-opacity-50">
                <div className="flex items-center justify-between text-xs text-theme-text-secondary">
                  <div className="flex items-center space-x-1">
                    <Monitor className="w-3 h-3" />
                    <span>实时预览</span>
                  </div>
                  <span>{availableThemes.length} 个主题可用</span>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ThemeSelector; 