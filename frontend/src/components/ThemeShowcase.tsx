import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>, 
  Eye, 
  Code2, 
  <PERSON><PERSON><PERSON>, 
  Monitor, 
  <PERSON>, 
  Sun,
  <PERSON>ap,
  Star,
  Heart,
  Coffee,
  Gamepad2
} from 'lucide-react';
import { useTheme, themes } from '../contexts/ThemeContext';
import TypingAnimation from './TypingAnimation';

const ThemeShowcase: React.FC = () => {
  const { currentTheme, setTheme } = useTheme();

  const themeFeatures = {
    matrix: ['代码雨背景', '绿色荧光', '黑客风格', '终端美学'],
    cyberpunk: ['霓虹紫红', '未来科幻', '电子朋克', '数字城市'],
    oceanic: ['深海蓝调', '海洋渐变', '宁静清爽', '专业商务'],
    forest: ['护眼绿色', '自然森林', '长时间编程', '舒适温和'],
    sunset: ['日落橙黄', '温暖暖色', '傍晚氛围', '创意灵感'],
    light: ['简洁明亮', '护眼白底', '日间办公', '经典设计'],
    retrowave: ['80年代', '蒸汽波', '复古未来', '霓虹粉紫'],
    arctic: ['极地冰雪', '清冷蓝白', '北欧简约', '专注编程']
  };

  const themeIcons = {
    matrix: Code2,
    cyberpunk: Zap,
    oceanic: Monitor,
    forest: Coffee,
    sunset: Heart,
    light: Sun,
    retrowave: Gamepad2,
    arctic: Star
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen p-6"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 text-center">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="code-terminal mb-6 mx-auto max-w-2xl"
          >
            <div className="text-theme-primary font-code text-sm mb-2">
              $ apcsa-ai themes --showcase --all
            </div>
            <div className="text-theme-text">
              <TypingAnimation text="探索 8 种精美主题，个性化你的编程体验" delay={500} />
            </div>
          </motion.div>

          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-5xl font-bold gradient-text mb-4"
          >
            主题展示厅
          </motion.h1>
          <p className="text-theme-text-secondary text-lg">
            为不同的编程场景选择最适合的视觉主题
          </p>
        </div>

        {/* Current Theme Highlight */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mb-12 glass-effect rounded-2xl p-8 glow-effect"
        >
          <div className="text-center mb-6">
            <div className="inline-flex items-center space-x-3 mb-4">
              <Sparkles className="w-8 h-8 text-theme-primary" />
              <h2 className="text-2xl font-bold text-theme-text">当前主题</h2>
            </div>
            <div className="text-6xl mb-4">{currentTheme.icon}</div>
            <h3 className="text-3xl font-bold text-theme-primary mb-2">{currentTheme.name}</h3>
            <p className="text-theme-text-secondary">{currentTheme.description}</p>
          </div>

          {/* Color Palette */}
          <div className="flex justify-center space-x-4 mb-6">
            {Object.entries(currentTheme.colors).slice(0, 8).map(([key, color]) => (
              <motion.div
                key={key}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.5 + Object.keys(currentTheme.colors).indexOf(key) * 0.1 }}
                className="text-center"
              >
                <div 
                  className="w-12 h-12 rounded-full border-2 border-theme-border mb-2 shadow-lg hover:scale-110 transition-transform"
                  style={{ backgroundColor: color }}
                />
                <span className="text-xs text-theme-text-secondary capitalize">{key}</span>
              </motion.div>
            ))}
          </div>

          {/* Features */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {themeFeatures[currentTheme.id as keyof typeof themeFeatures]?.map((feature, index) => (
              <motion.div
                key={feature}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                className="bg-theme-surface rounded-lg p-3 text-center border border-theme-border"
              >
                <span className="text-sm text-theme-text">{feature}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Theme Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Object.values(themes).map((theme, index) => {
            const ThemeIcon = themeIcons[theme.id as keyof typeof themeIcons];
            const isActive = currentTheme.id === theme.id;

            return (
              <motion.div
                key={theme.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className={`relative cursor-pointer transition-all duration-300 ${
                  isActive ? 'scale-105' : 'hover:scale-102'
                }`}
                onClick={() => setTheme(theme.id)}
              >
                <div 
                  className={`theme-card relative overflow-hidden ${
                    isActive ? 'ring-2 ring-theme-primary glow-effect' : ''
                  }`}
                  style={{
                    background: `linear-gradient(135deg, ${theme.colors.background}, ${theme.colors.surface})`,
                    borderColor: theme.colors.border
                  }}
                >
                  {/* Active Indicator */}
                  {isActive && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute top-3 right-3 w-6 h-6 bg-theme-primary rounded-full flex items-center justify-center"
                    >
                      <Eye className="w-3 h-3 text-white" />
                    </motion.div>
                  )}

                  {/* Theme Icon */}
                  <div className="text-center mb-4">
                    <div className="text-4xl mb-2">{theme.icon}</div>
                    <ThemeIcon 
                      className="w-6 h-6 mx-auto mb-2"
                      style={{ color: theme.colors.primary }}
                    />
                  </div>

                  {/* Theme Info */}
                  <div className="text-center mb-4">
                    <h3 
                      className="font-bold text-lg mb-1"
                      style={{ color: theme.colors.text }}
                    >
                      {theme.name}
                    </h3>
                    <p 
                      className="text-sm"
                      style={{ color: theme.colors.textSecondary }}
                    >
                      {theme.description}
                    </p>
                  </div>

                  {/* Color Preview */}
                  <div className="flex justify-center space-x-1 mb-4">
                    {[theme.colors.primary, theme.colors.secondary, theme.colors.accent].map((color, colorIndex) => (
                      <div 
                        key={colorIndex}
                        className="w-6 h-6 rounded-full border border-white border-opacity-30"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>

                  {/* Sample Code Block */}
                  <div 
                    className="text-xs font-mono p-3 rounded border"
                    style={{ 
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                      color: theme.colors.text
                    }}
                  >
                    <div style={{ color: theme.colors.primary }}>public class</div>
                    <div style={{ color: theme.colors.secondary }}>  Theme</div>
                    <div style={{ color: theme.colors.text }}>{'{'}</div>
                    <div style={{ color: theme.colors.accent }}>  // code</div>
                    <div style={{ color: theme.colors.text }}>{'}'}</div>
                  </div>

                  {/* Hover Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-transparent to-white opacity-0 hover:opacity-5 transition-opacity"
                    style={{ 
                      background: `linear-gradient(135deg, transparent, ${theme.colors.primary})`
                    }}
                  />
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Usage Tips */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mt-12 glass-effect rounded-xl p-6"
        >
          <div className="flex items-center mb-4">
            <Palette className="w-6 h-6 mr-3 text-theme-primary" />
            <h3 className="text-xl font-bold text-theme-text">使用建议</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-theme-surface rounded-lg border border-theme-border">
              <Moon className="w-8 h-8 mx-auto mb-2 text-theme-primary" />
              <h4 className="font-medium text-theme-text mb-1">夜间编程</h4>
              <p className="text-xs text-theme-text-secondary">选择深色主题保护视力</p>
            </div>
            
            <div className="text-center p-4 bg-theme-surface rounded-lg border border-theme-border">
              <Sun className="w-8 h-8 mx-auto mb-2 text-theme-primary" />
              <h4 className="font-medium text-theme-text mb-1">日间办公</h4>
              <p className="text-xs text-theme-text-secondary">亮色主题适合明亮环境</p>
            </div>
            
            <div className="text-center p-4 bg-theme-surface rounded-lg border border-theme-border">
              <Eye className="w-8 h-8 mx-auto mb-2 text-theme-primary" />
              <h4 className="font-medium text-theme-text mb-1">护眼模式</h4>
              <p className="text-xs text-theme-text-secondary">绿色主题减少眼部疲劳</p>
            </div>
            
            <div className="text-center p-4 bg-theme-surface rounded-lg border border-theme-border">
              <Sparkles className="w-8 h-8 mx-auto mb-2 text-theme-primary" />
              <h4 className="font-medium text-theme-text mb-1">创意灵感</h4>
              <p className="text-xs text-theme-text-secondary">彩色主题激发编程热情</p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ThemeShowcase; 