import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft,
  BookOpen,
  Code,
  ExternalLink,
  Play,
  FileText,
  Clock,
  Target,
  CheckCircle2,
  Loader2,
  Brain,
  Award,
  Users,
  Youtube,
  Video
} from 'lucide-react';
import AIAssistant from './AIAssistant';
import ExternalResourcesPanel from './ExternalResourcesPanel';

interface Topic {
  id: string;
  unit_id: string;
  title: string;
  description: string;
  order_index: number;
  estimated_hours: number;
  learning_objectives: string[] | null;
  is_active: boolean;
}

interface Exercise {
  id: string;
  topic_id: string;
  title: string;
  description: string;
  type: string;
  difficulty: string;
  points: number;
  content?: string;
}

interface LearningResource {
  id: string;
  topic_id: string;
  title: string;
  type: string;
  url?: string;
  description?: string;
  order_index: number;
}

// 新增：Lesson内容接口
interface LessonContent {
  khan_academy_videos: any[];
  codehs_notes: any[];
  codehs_documents: any[];
  quizzes: any[];
  assignments: any[];
  all_resources: any[];
}

interface LessonContentResponse {
  success: boolean;
  lesson_code: string;
  content: LessonContent;
  summary: {
    khan_academy_videos_count: number;
    codehs_notes_count: number;
    codehs_documents_count: number;
    quizzes_count: number;
    assignments_count: number;
    total_resources: number;
  };
}

const UnitDetail: React.FC = () => {
  const { unitId } = useParams<{ unitId: string }>();
  const navigate = useNavigate();
  const { token } = useAuth();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [exercises, setExercises] = useState<{ [topicId: string]: Exercise[] }>({});
  const [resources, setResources] = useState<{ [topicId: string]: LearningResource[] }>({});
  
  // 新增：lesson内容状态
  const [lessonContent, setLessonContent] = useState<{ [lessonCode: string]: LessonContent }>({});
  const [unitLessons, setUnitLessons] = useState<any[]>([]);
  const [selectedLesson, setSelectedLesson] = useState<string | null>(null);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false);
  const [externalResourcesOpen, setExternalResourcesOpen] = useState(false);
  const [showKhanAcademyContent, setShowKhanAcademyContent] = useState(true);

  // 新增：获取lesson内容的函数
  const fetchLessonContent = async (unitCode: string) => {
    try {
      console.log(`🎓 获取unit ${unitCode}的lesson内容...`);
      
      // TODO: 实现真实的API调用获取lessons
      // const lessonsResponse = await fetch(`http://localhost:8000/api/lessons/units/${unitCode}/lessons`);
      console.log(`📡 API响应状态: ${lessonsResponse.status}`);
      
      if (lessonsResponse.ok) {
        const lessonsData = await lessonsResponse.json();
        console.log(`📊 API返回数据:`, lessonsData);
        
        if (lessonsData.success) {
          const lessons = Object.entries(lessonsData.lessons).map(([key, value]: [string, any]) => ({
            lesson_key: key,
            lesson_code: value.lesson_code,
            content_summary: value.content_summary,
            has_khan_videos: value.has_khan_videos,
            has_codehs_resources: value.has_codehs_resources,
            has_assessments: value.has_assessments
          }));
          
          console.log(`🎯 处理后的lessons数组:`, lessons);
          setUnitLessons(lessons);
          console.log(`✅ 找到 ${lessons.length} 个lessons`);
          
          // 获取每个lesson的详细内容
          const lessonContentData: { [lessonCode: string]: LessonContent } = {};
          for (const lesson of lessons) {
            try {
              // TODO: 实现真实的API调用获取lesson内容
              // const contentResponse = await fetch(`http://localhost:8000/api/lessons/${lesson.lesson_code}/content`);
              if (contentResponse.ok) {
                const contentData: LessonContentResponse = await contentResponse.json();
                if (contentData.success) {
                  lessonContentData[lesson.lesson_code] = contentData.content;
                  console.log(`✅ 获取 ${lesson.lesson_code} 内容: ${contentData.summary.total_resources} 个资源`);
                }
              }
            } catch (err) {
              console.warn(`⚠️ 获取lesson ${lesson.lesson_code}内容失败:`, err);
            }
          }
          
          setLessonContent(lessonContentData);
          
          // 默认选择第一个lesson
          if (lessons.length > 0) {
            setSelectedLesson(lessons[0].lesson_code);
          }
          
          return true;
        } else {
          console.error('❌ API返回success=false');
        }
      } else {
        console.error(`❌ API请求失败: ${lessonsResponse.status} ${lessonsResponse.statusText}`);
      }
      return false;
    } catch (error) {
      console.error('❌ 获取lesson内容失败:', error);
      return false;
    }
  };

  useEffect(() => {
    const fetchUnitContent = async () => {
      if (!unitId) return;

      try {
        // 首先尝试获取Khan Academy + CodeHS lesson内容
        const unitCode = `unit-${unitId}`;
        console.log(`🚀 开始获取 ${unitCode} 的内容...`);
        
        const lessonSuccess = await fetchLessonContent(unitCode);
        if (lessonSuccess) {
          setShowKhanAcademyContent(true);
          console.log('✅ 成功获取Khan Academy + CodeHS内容');
        }

        // 获取传统的主题数据作为备用
        let topicsData;
        let usingDemo = false;
        
        if (token) {
          try {
            const authHeaders = {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            };
            
            const topicsResponse = await fetch(`http://localhost:8000/api/units/${unitId}/topics`, {
              headers: authHeaders
            });

            if (topicsResponse.ok) {
              topicsData = await topicsResponse.json();
              if (topicsData.data && topicsData.data.length > 0) {
                console.log('✅ 使用认证API获取主题成功');
              } else {
                throw new Error('认证API无数据，使用demo数据');
              }
            } else {
              throw new Error(`认证API失败: ${topicsResponse.status}`);
            }
          } catch (authError) {
            console.warn('认证API失败:', authError);
            throw new Error('无法获取主题数据');
          }
        } else {
          // 无token，无法获取数据
          throw new Error('需要登录才能访问课程内容');
        }

        const topicsList = topicsData.data || [];
        setTopics(topicsList);

        // 为每个主题获取练习和资源
        const exercisesData: { [topicId: string]: Exercise[] } = {};
        const resourcesData: { [topicId: string]: LearningResource[] } = {};

        for (const topic of topicsList) {
          // 获取练习
          try {
            let exercisesResult;
            
            if (!usingDemo && token) {
              // 尝试认证API
              try {
                const authHeaders = {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                };
                const exercisesResponse = await fetch(`http://localhost:8000/api/topics/${topic.id}/exercises`, {
                  headers: authHeaders
                });
                if (exercisesResponse.ok) {
                  exercisesResult = await exercisesResponse.json();
                } else {
                  throw new Error('认证API无练习数据');
                }
              } catch {
                // 无法获取练习数据
                console.warn('无法获取练习数据');
              }
            } else {
              // 需要登录才能获取练习数据
              console.warn('需要登录才能获取练习数据');
            }
            
            exercisesData[topic.id] = exercisesResult?.data || [];
          } catch (err) {
            console.warn(`获取练习失败: topic ${topic.id}:`, err);
            exercisesData[topic.id] = [];
          }

          // 获取学习资源
          try {
            let resourcesResult;
            
            if (!usingDemo && token) {
              // 尝试认证API
              try {
                const authHeaders = {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                };
                const resourcesResponse = await fetch(`http://localhost:8000/api/topics/${topic.id}/resources`, {
                  headers: authHeaders
                });
                if (resourcesResponse.ok) {
                  resourcesResult = await resourcesResponse.json();
                } else {
                  throw new Error('认证API无资源数据');
                }
              } catch {
                // 无法获取资源数据
                console.warn('无法获取资源数据');
              }
            } else {
              // 需要登录才能获取资源数据
              console.warn('需要登录才能获取资源数据');
            }
            
            resourcesData[topic.id] = resourcesResult?.data || [];
          } catch (err) {
            console.warn(`获取资源失败: topic ${topic.id}:`, err);
            resourcesData[topic.id] = [];
          }
        }

        setExercises(exercisesData);
        setResources(resourcesData);
        
        // 默认选择第一个主题
        if (topicsList.length > 0) {
          setSelectedTopic(topicsList[0].id);
        }

      } catch (err) {
        console.error('获取课程内容失败:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchUnitContent();
  }, [unitId, token]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-600';
      case 'medium': return 'bg-yellow-100 text-yellow-600';
      case 'hard': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'video': return Play;
      case 'external_link': return ExternalLink;
      case 'article': return FileText;
      case 'interactive_exercise': return Code;
      case 'reading': return BookOpen;
      default: return BookOpen;
    }
  };

  // 新增：渲染Khan Academy + CodeHS内容的函数
  const renderLessonContent = () => {
    if (!selectedLesson || !lessonContent[selectedLesson]) {
      return (
        <div className="glass-effect rounded-lg p-6">
          <div className="text-center text-gray-500">
            <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>选择一个lesson查看Khan Academy + CodeHS内容</p>
          </div>
        </div>
      );
    }

    const content = lessonContent[selectedLesson];
    
    return (
      <div className="space-y-6">
        {/* Khan Academy Videos */}
        {content.khan_academy_videos.length > 0 && (
          <div className="glass-effect rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center space-x-2">
              <Youtube className="w-6 h-6 text-red-500" />
              <span>Khan Academy Videos</span>
              <span className="text-sm bg-red-100 text-red-600 px-2 py-1 rounded-full">
                {content.khan_academy_videos.length}
              </span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {content.khan_academy_videos.map((video: any) => (
                <div key={video.id} className="border border-red-200 bg-red-50 rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 rounded-lg bg-red-100 text-red-600">
                      <Play className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{video.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{video.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{video.estimated_time_minutes}分钟</span>
                        </span>
                        <span className="px-2 py-1 bg-red-500 text-white rounded-full">
                          Khan Academy
                        </span>
                        <span className={`px-2 py-1 rounded-full ${getDifficultyColor(video.difficulty_level)}`}>
                          {video.difficulty_level}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* CodeHS Resources */}
        {(content.codehs_notes.length > 0 || content.codehs_documents.length > 0) && (
          <div className="glass-effect rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center space-x-2">
              <BookOpen className="w-6 h-6 text-blue-500" />
              <span>CodeHS Learning Materials</span>
              <span className="text-sm bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                {content.codehs_notes.length + content.codehs_documents.length}
              </span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* CodeHS Notes */}
              {content.codehs_notes.map((note: any) => (
                <div key={note.id} className="border border-blue-200 bg-blue-50 rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                      <FileText className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{note.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{note.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{note.estimated_time_minutes}分钟</span>
                        </span>
                        <span className="px-2 py-1 bg-blue-500 text-white rounded-full">
                          CodeHS Notes
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* CodeHS Documents */}
              {content.codehs_documents.map((doc: any) => (
                <div key={doc.id} className="border border-green-200 bg-green-50 rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 rounded-lg bg-green-100 text-green-600">
                      <FileText className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{doc.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{doc.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{doc.estimated_time_minutes}分钟</span>
                        </span>
                        <span className="px-2 py-1 bg-green-500 text-white rounded-full">
                          CodeHS Doc
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quizzes and Assignments */}
        {(content.quizzes.length > 0 || content.assignments.length > 0) && (
          <div className="glass-effect rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center space-x-2">
              <Award className="w-6 h-6 text-purple-500" />
              <span>Practice & Assessment</span>
              <span className="text-sm bg-purple-100 text-purple-600 px-2 py-1 rounded-full">
                {content.quizzes.length + content.assignments.length}
              </span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Quizzes */}
              {content.quizzes.map((quiz: any) => (
                <div key={quiz.id} className="border border-purple-200 bg-purple-50 rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                      <Target className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{quiz.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{quiz.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Target className="w-3 h-3" />
                          <span>{quiz.metadata?.questions_count || 'N/A'}题</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{quiz.estimated_time_minutes}分钟</span>
                        </span>
                        <span className="px-2 py-1 bg-purple-500 text-white rounded-full">
                          Quiz
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Assignments */}
              {content.assignments.map((assignment: any) => (
                <div key={assignment.id} className="border border-orange-200 bg-orange-50 rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 rounded-lg bg-orange-100 text-orange-600">
                      <Code className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{assignment.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{assignment.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{assignment.estimated_time_minutes}分钟</span>
                        </span>
                        <span className="px-2 py-1 bg-orange-500 text-white rounded-full">
                          Assignment
                        </span>
                        <span className={`px-2 py-1 rounded-full ${getDifficultyColor(assignment.difficulty_level)}`}>
                          {assignment.difficulty_level}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="min-h-screen p-6 flex items-center justify-center"
      >
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-primary-500 mx-auto mb-4" />
          <p className="text-dark-400">Loading unit content...</p>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="min-h-screen p-6 flex items-center justify-center"
      >
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">Error loading content</div>
          <p className="text-dark-400">{error}</p>
          <button
            onClick={() => navigate('/units')}
            className="mt-4 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
          >
            Back to Units
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen p-6"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/units')}
            className="flex items-center space-x-2 text-primary-500 hover:text-primary-600 mb-4"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Units</span>
          </button>
          
          <h1 className="text-4xl font-bold gradient-text mb-2">
            Unit 1: Primitive Types
          </h1>
          <p className="text-dark-400">
            Explore Khan Academy videos, CodeHS resources, and practice exercises
          </p>
          
          {/* Content Toggle Buttons */}
          <div className="mt-4 flex space-x-4">
            <button
              onClick={() => setShowKhanAcademyContent(true)}
              className={`px-4 py-2 rounded-lg transition-all duration-300 flex items-center space-x-2 ${
                showKhanAcademyContent 
                  ? 'bg-gradient-to-r from-red-500 to-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <Brain className="w-4 h-4" />
              <span>Khan Academy + CodeHS</span>
            </button>
            
            <button
              onClick={() => setShowKhanAcademyContent(false)}
              className={`px-4 py-2 rounded-lg transition-all duration-300 flex items-center space-x-2 ${
                !showKhanAcademyContent 
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <BookOpen className="w-4 h-4" />
              <span>Traditional Topics</span>
            </button>
            
            <button
              onClick={() => setExternalResourcesOpen(true)}
              className="px-4 py-2 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-lg hover:from-green-600 hover:to-teal-700 transition-all duration-300 flex items-center space-x-2"
            >
              <ExternalLink className="w-4 h-4" />
              <span>External Resources</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="glass-effect rounded-lg p-6 sticky top-6">
              {showKhanAcademyContent ? (
                <>
                  <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
                    <Brain className="w-5 h-5 text-red-500" />
                    <span>Lessons ({unitLessons.length})</span>
                  </h3>
                  {unitLessons.length === 0 && (
                    <div className="text-gray-500 text-sm">
                      🔄 Loading lessons... (Check console for details)
                    </div>
                  )}
                  <div className="space-y-2">
                    {unitLessons.map((lesson) => (
                      <button
                        key={lesson.lesson_code}
                        onClick={() => setSelectedLesson(lesson.lesson_code)}
                        className={`w-full text-left p-3 rounded-lg transition-colors ${
                          selectedLesson === lesson.lesson_code
                            ? 'bg-primary-100 text-primary-700 border border-primary-200'
                            : 'hover:bg-gray-50'
                        }`}
                      >
                        <div className="font-medium text-sm">{lesson.lesson_key}</div>
                        <div className="text-xs text-dark-400 mt-1">
                          Khan Academy: {lesson.content_summary?.khan_academy_videos || 0} videos
                        </div>
                        <div className="text-xs text-dark-400">
                          CodeHS: {(lesson.content_summary?.codehs_notes || 0) + (lesson.content_summary?.codehs_documents || 0)} resources
                        </div>
                        <div className="text-xs text-dark-400">
                          Assessment: {(lesson.content_summary?.quizzes || 0) + (lesson.content_summary?.assignments || 0)} items
                        </div>
                      </button>
                    ))}
                  </div>
                </>
              ) : (
                <>
              <h3 className="text-lg font-semibold mb-4">Topics</h3>
              <div className="space-y-2">
                {topics.map((topic) => (
                  <button
                    key={topic.id}
                    onClick={() => setSelectedTopic(topic.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedTopic === topic.id
                        ? 'bg-primary-100 text-primary-700 border border-primary-200'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium text-sm">{topic.title}</div>
                    <div className="text-xs text-dark-400 mt-1">
                      {topic.estimated_hours}h • {exercises[topic.id]?.length || 0} exercises
                    </div>
                  </button>
                ))}
              </div>
                </>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {showKhanAcademyContent ? (
              // Khan Academy + CodeHS Content
              renderLessonContent()
            ) : (
              // Traditional Topic Content
              selectedTopic && topics.find(t => t.id === selectedTopic) && (
              <motion.div
                key={selectedTopic}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                {/* Topic Header */}
                <div className="glass-effect rounded-lg p-6">
                    <h2 className="text-2xl font-bold mb-3">{topics.find(t => t.id === selectedTopic)?.title}</h2>
                    <p className="text-dark-600 mb-4">{topics.find(t => t.id === selectedTopic)?.description}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-dark-500 mb-4">
                    <span className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                        <span>{topics.find(t => t.id === selectedTopic)?.estimated_hours} hours</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Target className="w-4 h-4" />
                        <span>{topics.find(t => t.id === selectedTopic)?.learning_objectives?.length || 0} objectives</span>
                    </span>
                  </div>

                  {/* Learning Objectives */}
                                          {topics.find(t => t.id === selectedTopic)?.learning_objectives && (
                    <div>
                      <h4 className="font-semibold mb-2">Learning Objectives:</h4>
                      <ul className="space-y-1">
                            {topics.find(t => t.id === selectedTopic)?.learning_objectives?.map((objective, index) => (
                          <li key={index} className="flex items-start space-x-2 text-sm">
                            <CheckCircle2 className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>{objective}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Learning Resources */}
                {selectedTopic && resources[selectedTopic] && resources[selectedTopic].length > 0 && (
                  <div className="glass-effect rounded-lg p-6">
                      <h3 className="text-xl font-semibold mb-4 flex items-center space-x-2">
                        <BookOpen className="w-6 h-6 text-primary-500" />
                        <span>Learning Resources</span>
                      </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {resources[selectedTopic].map((resource: LearningResource) => {
                        const IconComponent = getResourceIcon(resource.type);
                          const isExternal = resource.id?.startsWith('ext_');
                          
                        return (
                            <div key={resource.id} className={`border rounded-lg p-4 hover:shadow-md transition-all duration-300 ${
                              isExternal ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                            }`}>
                            <div className="flex items-start space-x-3">
                                <div className={`p-2 rounded-lg ${
                                  resource.type === 'video' ? 'bg-red-100 text-red-600' :
                                  resource.type === 'article' ? 'bg-green-100 text-green-600' :
                                  resource.type === 'interactive_exercise' ? 'bg-purple-100 text-purple-600' :
                                  'bg-blue-100 text-blue-600'
                                }`}>
                                  <IconComponent className="w-4 h-4" />
                                </div>
                              <div className="flex-1">
                                  <div className="flex items-start justify-between">
                                    <h4 className="font-medium text-gray-900">{resource.title}</h4>
                                    {isExternal && (
                                      <span className="text-xs px-2 py-1 bg-blue-500 text-white rounded-full">
                                        External
                                      </span>
                                    )}
                                  </div>
                                  <p className="text-sm text-gray-600 mt-1">{resource.description}</p>
                                  
                                  {/* 显示额外信息 */}
                                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    {(resource as any).source && (
                                      <span className="flex items-center space-x-1">
                                        <span>📚</span>
                                        <span>{(resource as any).source}</span>
                                      </span>
                                    )}
                                    {(resource as any).channel && (
                                      <span className="flex items-center space-x-1">
                                        <span>🎥</span>
                                        <span>{(resource as any).channel}</span>
                                      </span>
                                    )}
                                    {(resource as any).difficulty && (
                                      <span className={`px-2 py-1 rounded-full text-xs ${
                                        (resource as any).difficulty === 'easy' ? 'bg-green-100 text-green-600' :
                                        (resource as any).difficulty === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                                        'bg-red-100 text-red-600'
                                      }`}>
                                        {(resource as any).difficulty}
                                      </span>
                                    )}
                                    {(resource as any).estimated_time && (
                                      <span className="flex items-center space-x-1">
                                        <Clock className="w-3 h-3" />
                                        <span>{(resource as any).estimated_time}</span>
                                      </span>
                                    )}
                                  </div>
                                  
                                {resource.url && (
                                  <a
                                    href={resource.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                      className="text-primary-500 hover:text-primary-600 text-sm mt-3 inline-flex items-center space-x-1 font-medium"
                                  >
                                      <span>
                                        {resource.type === 'video' ? 'Watch Video' :
                                         resource.type === 'interactive_exercise' ? 'Start Exercise' :
                                         'Read Article'}
                                      </span>
                                    <ExternalLink className="w-3 h-3" />
                                  </a>
                                )}
                              </div>
                            </div>
                              
                              {/* 视频缩略图 */}
                              {resource.type === 'video' && (resource as any).thumbnail && (
                                <div className="mt-3">
                                  <img 
                                    src={(resource as any).thumbnail} 
                                    alt={resource.title}
                                    className="w-full h-32 object-cover rounded-lg"
                                  />
                                </div>
                              )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Exercises */}
                {selectedTopic && exercises[selectedTopic] && exercises[selectedTopic].length > 0 && (
                  <div className="glass-effect rounded-lg p-6">
                    <h3 className="text-xl font-semibold mb-4">Practice Exercises</h3>
                    <div className="space-y-4">
                      {exercises[selectedTopic].map((exercise: Exercise) => (
                        <div key={exercise.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <Code className="w-5 h-5 text-primary-500" />
                                <h4 className="font-medium">{exercise.title}</h4>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>
                                  {exercise.difficulty}
                                </span>
                              </div>
                              <p className="text-sm text-dark-400 mb-2">{exercise.description}</p>
                              <div className="flex items-center space-x-4 text-xs text-dark-500">
                                <span>Type: {exercise.type}</span>
                                <span>Points: {exercise.points}</span>
                              </div>
                            </div>
                            <button 
                              onClick={() => navigate(`/exercise/${exercise.id}`)}
                              className="px-4 py-2 bg-primary-100 text-primary-600 rounded-lg hover:bg-primary-200 transition-colors"
                            >
                              Start Exercise
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
              )
            )}
          </div>
        </div>
      </div>

      {/* AI Assistant */}
      <AnimatePresence>
        {aiAssistantOpen && (
          <AIAssistant
            isOpen={aiAssistantOpen}
            onClose={() => setAiAssistantOpen(false)}
            context={{
              topic: showKhanAcademyContent ? selectedLesson || undefined : selectedTopic || undefined,
              unitId: unitId
            }}
          />
        )}
      </AnimatePresence>

      {/* External Resources Panel */}
      <AnimatePresence>
        {externalResourcesOpen && (
          <ExternalResourcesPanel
            unitId={unitId || '1'}
            unitTitle="Primitive Types"
            isOpen={externalResourcesOpen}
            onClose={() => setExternalResourcesOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* AI Assistant Floating Button */}
      <button
        onClick={() => setAiAssistantOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-50"
      >
        <Brain className="w-6 h-6" />
      </button>
    </motion.div>
  );
};

export default UnitDetail; 