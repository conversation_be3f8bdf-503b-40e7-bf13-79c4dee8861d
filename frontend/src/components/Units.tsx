import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle2, 
  Clock, 
  ArrowRight,
  Lock,
  Play,
  FileText,
  Loader2,
  Brain
} from 'lucide-react';
import AIAssistant from './AIAssistant';

interface Unit {
  id: string;
  title: string;
  description: string;
  order_index: number;
  estimated_hours: number;
  prerequisites: string[] | null;
  is_active: boolean;
  created_at: string;
}

const Units: React.FC = () => {
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false);
  const { token, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUnits = async () => {
      try {
        let data;
        
        // 尝试使用认证API
        if (token) {
          try {
            const response = await fetch('http://localhost:8000/api/units', {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            });
            
            if (response.ok) {
              data = await response.json();
            } else {
              throw new Error('Authenticated API failed');
            }
          } catch (authError) {
            console.warn('Authenticated API failed, using public API');
            // Fallback to public API
            const publicResponse = await fetch('http://localhost:8000/api/public/units');
            if (!publicResponse.ok) {
              throw new Error('Public API also failed');
            }
            data = await publicResponse.json();
          }
        } else {
          // No token, use public API directly
          const response = await fetch('http://localhost:8000/api/public/units');
          if (!response.ok) {
            throw new Error('Failed to fetch units');
          }
          data = await response.json();
        }
        
        setUnits(data);
      } catch (err) {
        console.error('Error fetching units:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchUnits();
  }, [token]);

  const getStatusColor = (order: number) => {
    // 所有单元都从未开始状态
    return 'text-gray-400';
  };

  const getDifficultyColor = (order: number) => {
    if (order <= 3) return 'bg-green-100 text-green-600';
    if (order <= 7) return 'bg-yellow-100 text-yellow-600';
    if (order <= 9) return 'bg-orange-100 text-orange-600';
    return 'bg-red-100 text-red-600';
  };

  const getDifficultyLabel = (order: number) => {
    if (order <= 3) return 'Beginner';
    if (order <= 7) return 'Intermediate';
    if (order <= 9) return 'Advanced';
    return 'Expert';
  };

  const getStatusIcon = (order: number) => {
    // 第一个单元可以开始，其他暂时锁定
    if (order === 1) return Play;
    return Lock;
  };

  const getProgress = (order: number) => {
    // 所有进度都从0开始
    return 0;
  };

  const getStatus = (order: number) => {
    // 第一个单元可以开始，其他锁定
    if (order === 1) return 'available';
    return 'locked';
  };

  const handleUnitClick = (unit: Unit) => {
    const status = getStatus(unit.order_index);
    if (status !== 'locked') {
      navigate(`/units/${unit.id}`);
    }
  };

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="min-h-screen p-6 flex items-center justify-center"
      >
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-primary-500 mx-auto mb-4" />
          <p className="text-dark-400">Loading APCSA units...</p>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="min-h-screen p-6 flex items-center justify-center"
      >
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">Error loading units</div>
          <p className="text-dark-400">{error}</p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen p-6"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header with User Welcome */}
        <div className="mb-8">
          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="text-4xl font-bold gradient-text mb-2"
          >
            Welcome back, {user?.full_name}! 👋
          </motion.h1>
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.15 }}
            className="space-y-1"
          >
            <h2 className="text-2xl font-semibold text-dark-700">APCSA Course Units</h2>
            <p className="text-dark-400">Continue your Java programming journey with our comprehensive curriculum</p>
            <div className="flex items-center space-x-4 text-sm text-dark-500 mt-2">
              <span className="flex items-center space-x-1">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>Role: {user?.role}</span>
              </span>
              {user?.school && (
                <span className="flex items-center space-x-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>School: {user.school}</span>
                </span>
              )}
              {user?.grade_level && (
                <span className="flex items-center space-x-1">
                  <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                  <span>Grade: {user.grade_level}</span>
                </span>
              )}
            </div>
          </motion.div>
        </div>

        {/* Progress Overview */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="glass-effect rounded-lg p-6 mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-500">1/{units.length}</div>
              <div className="text-sm text-dark-400">Units Started</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-500">0</div>
              <div className="text-sm text-dark-400">Units Completed</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-500">{units.reduce((acc, unit) => acc + unit.estimated_hours, 0)}h</div>
              <div className="text-sm text-dark-400">Total Content</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-500">0h</div>
              <div className="text-sm text-dark-400">Time Invested</div>
            </div>
          </div>
        </motion.div>

        {/* Units Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {units.map((unit, index) => {
            const StatusIcon = getStatusIcon(unit.order_index);
            const progress = getProgress(unit.order_index);
            const status = getStatus(unit.order_index);
            
            return (
              <motion.div
                key={unit.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 * index }}
                className={`glass-effect rounded-lg p-6 transition-all duration-300 ${
                  status === 'locked' ? 'opacity-60' : 'cursor-pointer hover:scale-105 hover:shadow-xl'
                }`}
                onClick={() => handleUnitClick(unit)}
              >
                {/* Unit Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg bg-primary-100 ${getStatusColor(unit.order_index)}`}>
                      <StatusIcon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">{unit.title}</h3>
                      <p className="text-sm text-dark-400">Unit {unit.order_index}</p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(unit.order_index)}`}>
                    {getDifficultyLabel(unit.order_index)}
                  </span>
                </div>

                {/* Description */}
                <p className="text-dark-400 text-sm mb-4 line-clamp-2">
                  {unit.description}
                </p>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Progress</span>
                    <span className="text-sm text-dark-400">{progress}%</span>
                  </div>
                  <div className="w-full bg-dark-200 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${progress}%` }}
                      transition={{ delay: 0.5 + index * 0.1, duration: 0.8 }}
                      className="h-2 rounded-full bg-gray-400"
                    />
                  </div>
                </div>

                {/* Stats */}
                <div className="flex justify-between items-center text-sm text-dark-400 mb-4">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{unit.estimated_hours}h</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <FileText className="w-4 h-4" />
                    <span>5-8 topics</span>
                  </div>
                </div>

                {/* Action Button */}
                <button
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    status === 'locked'
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-primary-100 text-primary-600 hover:bg-primary-200'
                  }`}
                  disabled={status === 'locked'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleUnitClick(unit);
                  }}
                >
                  {status === 'locked' ? 'Locked' : 'Start Learning'}
                  {status !== 'locked' && <ArrowRight className="w-4 h-4 inline ml-2" />}
                </button>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* AI助手浮动按钮 */}
      <motion.button
        onClick={() => setAiAssistantOpen(true)}
        className="fixed bottom-6 right-6 bg-gradient-to-r from-purple-500 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-40"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
      >
        <Brain className="w-6 h-6" />
      </motion.button>

      {/* AI助手组件 */}
      <AIAssistant
        isOpen={aiAssistantOpen}
        onClose={() => setAiAssistantOpen(false)}
        context={{
          topic: "APCSA Units Overview",
          unitId: "units-page",
          exerciseId: "units-questions"
        }}
      />
    </motion.div>
  );
};

export default Units; 