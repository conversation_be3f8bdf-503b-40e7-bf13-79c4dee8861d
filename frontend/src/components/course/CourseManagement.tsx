import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Plus, 
  Search, 
  Filter, 
  Grid3X3, 
  List,
  Users,
  Clock,
  TrendingUp,
  Award,
  ChevronRight,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { Unit, CourseStats } from '../../types/course';
import { courseService } from '../../services/courseService';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import CreateUnitModal from './CreateUnitModal';

const CourseManagement: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [units, setUnits] = useState<Unit[]>([]);
  const [stats, setStats] = useState<CourseStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDifficulty, setFilterDifficulty] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedUnit, setSelectedUnit] = useState<Unit | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [unitsData, statsData] = await Promise.all([
        courseService.getUnits(),
        courseService.getCourseStats()
      ]);
      setUnits(unitsData);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load course data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredUnits = units.filter(unit => {
    const matchesSearch = unit.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         unit.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDifficulty = filterDifficulty === null || unit.difficulty_level === filterDifficulty;
    return matchesSearch && matchesDifficulty;
  });

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'text-green-600 bg-green-100';
      case 2: return 'text-blue-600 bg-blue-100';
      case 3: return 'text-yellow-600 bg-yellow-100';
      case 4: return 'text-orange-600 bg-orange-100';
      case 5: return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getDifficultyText = (level: number) => {
    switch (level) {
      case 1: return '入门';
      case 2: return '基础';
      case 3: return '中级';
      case 4: return '进阶';
      case 5: return '高级';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-dark-400">加载课程数据...</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen p-6 bg-gray-50"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold gradient-text mb-2">课程管理</h1>
            <p className="text-dark-400">管理 APCSA 课程单元和学习内容</p>
          </div>
          
          {user?.role === 'admin' || user?.role === 'teacher' ? (
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary flex items-center space-x-2"
            >
              <Plus className="w-5 h-5" />
              <span>新建单元</span>
            </button>
          ) : null}
        </div>

        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-dark-500 mb-1">总单元数</p>
                  <p className="text-2xl font-bold text-dark-900">{stats.total_units}</p>
                  <p className="text-xs text-blue-600 mt-1">
                    已完成 {stats.completed_units} 个
                  </p>
                </div>
                <BookOpen className="w-8 h-8 text-blue-500" />
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-dark-500 mb-1">学习时长</p>
                  <p className="text-2xl font-bold text-dark-900">
                    {Math.floor(stats.total_study_time / 3600)}h
                  </p>
                  <p className="text-xs text-green-600 mt-1">
                    连续学习 {stats.current_streak} 天
                  </p>
                </div>
                <Clock className="w-8 h-8 text-green-500" />
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-dark-500 mb-1">练习完成</p>
                  <p className="text-2xl font-bold text-dark-900">{stats.completed_exercises}</p>
                  <p className="text-xs text-purple-600 mt-1">
                    共 {stats.total_exercises} 个练习
                  </p>
                </div>
                <Award className="w-8 h-8 text-purple-500" />
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="glass-effect rounded-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-dark-500 mb-1">平均分数</p>
                  <p className="text-2xl font-bold text-dark-900">{stats.average_score.toFixed(1)}%</p>
                  <p className="text-xs text-orange-600 mt-1">
                    <TrendingUp className="w-3 h-3 inline mr-1" />
                    表现优秀
                  </p>
                </div>
                <TrendingUp className="w-8 h-8 text-orange-500" />
              </div>
            </motion.div>
          </div>
        )}

        {/* Search and Filter */}
        <div className="glass-effect rounded-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-1 items-center space-x-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="搜索课程单元..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Filter className="w-5 h-5 text-dark-400" />
                <select
                  value={filterDifficulty || ''}
                  onChange={(e) => setFilterDifficulty(e.target.value ? parseInt(e.target.value) : null)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">所有难度</option>
                  <option value="1">入门</option>
                  <option value="2">基础</option>
                  <option value="3">中级</option>
                  <option value="4">进阶</option>
                  <option value="5">高级</option>
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-dark-400 hover:bg-gray-100'}`}
              >
                <Grid3X3 className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-dark-400 hover:bg-gray-100'}`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Units Display */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredUnits.map((unit, index) => (
              <motion.div
                key={unit.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: index * 0.1 }}
                className="glass-effect rounded-lg p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group"
                onClick={() => navigate(`/course/unit/${unit.id}`)}
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center text-white font-bold">
                      {unit.order_index}
                    </div>
                    <div>
                      <h3 className="font-bold text-dark-900 group-hover:text-primary-600 transition-colors">
                        {unit.title}
                      </h3>
                      <p className="text-sm text-dark-500">{unit.unit_code}</p>
                    </div>
                  </div>
                  
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(unit.difficulty_level)}`}>
                    {getDifficultyText(unit.difficulty_level)}
                  </span>
                </div>

                <p className="text-dark-600 text-sm mb-4 line-clamp-2">
                  {unit.description}
                </p>

                <div className="flex items-center justify-between text-sm text-dark-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{unit.estimated_hours}h</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{unit.student_count}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="w-4 h-4" />
                    <span>{unit.completion_rate}%</span>
                  </div>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                  <div
                    className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${unit.completion_rate}%` }}
                  ></div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {unit.learning_objectives.slice(0, 2).map((objective, idx) => (
                      <span key={idx} className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">
                        {objective.length > 15 ? objective.substring(0, 15) + '...' : objective}
                      </span>
                    ))}
                  </div>
                  
                  <ChevronRight className="w-5 h-5 text-dark-400 group-hover:text-primary-600 transition-colors" />
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="glass-effect rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-dark-500 uppercase tracking-wider">
                      单元
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-dark-500 uppercase tracking-wider">
                      难度
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-dark-500 uppercase tracking-wider">
                      时长
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-dark-500 uppercase tracking-wider">
                      学生数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-dark-500 uppercase tracking-wider">
                      完成率
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-dark-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUnits.map((unit) => (
                    <tr key={unit.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                            {unit.order_index}
                          </div>
                          <div>
                            <div className="text-sm font-medium text-dark-900">{unit.title}</div>
                            <div className="text-sm text-dark-500">{unit.unit_code}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(unit.difficulty_level)}`}>
                          {getDifficultyText(unit.difficulty_level)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-dark-500">
                        {unit.estimated_hours}h
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-dark-500">
                        {unit.student_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full"
                              style={{ width: `${unit.completion_rate}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-dark-500">{unit.completion_rate}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            <Eye className="w-4 h-4" />
                          </button>
                          {(user?.role === 'admin' || user?.role === 'teacher') && (
                            <>
                              <button className="text-green-600 hover:text-green-900">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="text-red-600 hover:text-red-900">
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {filteredUnits.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-16 h-16 text-dark-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-dark-500 mb-2">没有找到匹配的课程单元</h3>
            <p className="text-dark-400">尝试调整搜索条件或筛选器</p>
          </div>
        )}

        {/* Create Unit Modal */}
        <CreateUnitModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={loadData}
        />
      </div>
    </motion.div>
  );
};

export default CourseManagement;
