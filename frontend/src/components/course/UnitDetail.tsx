import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  BookOpen, 
  Clock, 
  Users, 
  Target, 
  CheckCircle,
  PlayCircle,
  FileText,
  Code,
  Award,
  TrendingUp,
  Plus,
  Edit,
  Trash2
} from 'lucide-react';
import { Unit, Topic } from '../../types/course';
import { courseService } from '../../services/courseService';
import { useAuth } from '../../contexts/AuthContext';

const UnitDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [unit, setUnit] = useState<Unit | null>(null);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'topics' | 'progress' | 'settings'>('overview');

  useEffect(() => {
    if (id) {
      loadUnitData(parseInt(id));
    }
  }, [id]);

  const loadUnitData = async (unitId: number) => {
    try {
      setLoading(true);
      const [unitData, topicsData] = await Promise.all([
        courseService.getUnit(unitId),
        courseService.getTopics(unitId)
      ]);
      setUnit(unitData);
      setTopics(topicsData);
    } catch (error) {
      console.error('Failed to load unit data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'text-green-600 bg-green-100';
      case 2: return 'text-blue-600 bg-blue-100';
      case 3: return 'text-yellow-600 bg-yellow-100';
      case 4: return 'text-orange-600 bg-orange-100';
      case 5: return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getDifficultyText = (level: number) => {
    switch (level) {
      case 1: return '入门';
      case 2: return '基础';
      case 3: return '中级';
      case 4: return '进阶';
      case 5: return '高级';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-dark-400">加载单元数据...</p>
        </div>
      </div>
    );
  }

  if (!unit) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-dark-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-dark-500 mb-2">单元未找到</h3>
          <p className="text-dark-400 mb-4">请检查单元ID是否正确</p>
          <button onClick={() => navigate('/course')} className="btn-primary">
            返回课程列表
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen p-6 bg-gray-50"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/course')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-dark-600" />
            </button>
            <div>
              <h1 className="text-4xl font-bold gradient-text mb-2">{unit.title}</h1>
              <p className="text-dark-400">{unit.unit_code} • {unit.description}</p>
            </div>
          </div>
          
          {(user?.role === 'admin' || user?.role === 'teacher') && (
            <div className="flex items-center space-x-2">
              <button className="btn-secondary flex items-center space-x-2">
                <Edit className="w-4 h-4" />
                <span>编辑单元</span>
              </button>
              <button className="btn-danger flex items-center space-x-2">
                <Trash2 className="w-4 h-4" />
                <span>删除单元</span>
              </button>
            </div>
          )}
        </div>

        {/* Unit Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="glass-effect rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-dark-500 mb-1">难度等级</p>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(unit.difficulty_level)}`}>
                  {getDifficultyText(unit.difficulty_level)}
                </span>
              </div>
              <Target className="w-8 h-8 text-blue-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="glass-effect rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-dark-500 mb-1">预计时长</p>
                <p className="text-2xl font-bold text-dark-900">{unit.estimated_hours}h</p>
              </div>
              <Clock className="w-8 h-8 text-green-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="glass-effect rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-dark-500 mb-1">学生数量</p>
                <p className="text-2xl font-bold text-dark-900">{unit.student_count}</p>
              </div>
              <Users className="w-8 h-8 text-purple-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="glass-effect rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-dark-500 mb-1">完成率</p>
                <p className="text-2xl font-bold text-dark-900">{unit.completion_rate}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-500" />
            </div>
          </motion.div>
        </div>

        {/* Tabs */}
        <div className="glass-effect rounded-lg mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: '概览', icon: BookOpen },
                { id: 'topics', label: '主题', icon: FileText },
                { id: 'progress', label: '进度', icon: TrendingUp },
                ...(user?.role === 'admin' || user?.role === 'teacher' ? [{ id: 'settings', label: '设置', icon: Edit }] : [])
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-dark-500 hover:text-dark-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Learning Objectives */}
                <div>
                  <h3 className="text-lg font-semibold text-dark-900 mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-blue-500" />
                    学习目标
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {unit.learning_objectives.map((objective, index) => (
                      <div key={index} className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                        <span className="text-dark-700">{objective}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Prerequisites */}
                {unit.prerequisites.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-dark-900 mb-4 flex items-center">
                      <Award className="w-5 h-5 mr-2 text-orange-500" />
                      前置要求
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {unit.prerequisites.map((prereq, index) => (
                        <span key={index} className="px-3 py-1 bg-orange-100 text-orange-600 rounded-full text-sm">
                          {prereq}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-dark-900 mb-4">详细描述</h3>
                  <p className="text-dark-600 leading-relaxed">{unit.description}</p>
                </div>
              </div>
            )}

            {activeTab === 'topics' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-dark-900">单元主题</h3>
                  {(user?.role === 'admin' || user?.role === 'teacher') && (
                    <button className="btn-primary flex items-center space-x-2">
                      <Plus className="w-4 h-4" />
                      <span>添加主题</span>
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {topics.map((topic, index) => (
                    <motion.div
                      key={topic.id}
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1 }}
                      className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                            {topic.order_index}
                          </div>
                          <div>
                            <h4 className="font-semibold text-dark-900">{topic.title}</h4>
                            <p className="text-sm text-dark-500">{topic.topic_code}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-1 text-sm text-dark-500">
                            <Clock className="w-4 h-4" />
                            <span>{topic.estimated_minutes}min</span>
                          </div>
                          
                          {topic.completion_rate && (
                            <div className="flex items-center space-x-2">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full"
                                  style={{ width: `${topic.completion_rate}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-dark-500">{topic.completion_rate}%</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <p className="text-dark-600 mb-4">{topic.description}</p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <button className="flex items-center space-x-2 text-blue-600 hover:text-blue-800">
                            <PlayCircle className="w-4 h-4" />
                            <span>开始学习</span>
                          </button>
                          <button className="flex items-center space-x-2 text-green-600 hover:text-green-800">
                            <Code className="w-4 h-4" />
                            <span>练习题</span>
                          </button>
                        </div>
                        
                        {(user?.role === 'admin' || user?.role === 'teacher') && (
                          <div className="flex items-center space-x-2">
                            <button className="text-gray-600 hover:text-gray-800">
                              <Edit className="w-4 h-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-800">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>

                {topics.length === 0 && (
                  <div className="text-center py-12">
                    <FileText className="w-16 h-16 text-dark-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-dark-500 mb-2">暂无主题</h3>
                    <p className="text-dark-400">该单元还没有添加任何主题</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'progress' && (
              <div>
                <h3 className="text-lg font-semibold text-dark-900 mb-6">学习进度统计</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="bg-blue-50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-blue-900">总体进度</h4>
                      <TrendingUp className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-blue-900 mb-2">{unit.completion_rate}%</div>
                    <p className="text-blue-600 text-sm">已完成学生比例</p>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-green-900">活跃学生</h4>
                      <Users className="w-5 h-5 text-green-600" />
                    </div>
                    <div className="text-3xl font-bold text-green-900 mb-2">{Math.floor((unit.student_count || 0) * 0.8)}</div>
                    <p className="text-green-600 text-sm">本周活跃学生数</p>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-purple-900">平均用时</h4>
                      <Clock className="w-5 h-5 text-purple-600" />
                    </div>
                    <div className="text-3xl font-bold text-purple-900 mb-2">{unit.estimated_hours}h</div>
                    <p className="text-purple-600 text-sm">学生平均完成时间</p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (user?.role === 'admin' || user?.role === 'teacher') && (
              <div>
                <h3 className="text-lg font-semibold text-dark-900 mb-6">单元设置</h3>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-dark-700 mb-2">单元状态</label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500">
                        <option value="active">激活</option>
                        <option value="inactive">停用</option>
                        <option value="draft">草稿</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-dark-700 mb-2">难度等级</label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500">
                        <option value="1">入门</option>
                        <option value="2">基础</option>
                        <option value="3">中级</option>
                        <option value="4">进阶</option>
                        <option value="5">高级</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-4">
                    <button className="btn-secondary">取消</button>
                    <button className="btn-primary">保存更改</button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default UnitDetail;
