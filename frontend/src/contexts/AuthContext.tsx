import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// 时区配置
const TIMEZONE = 'America/Vancouver';

// 时区工具函数
export const formatDateForVancouver = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleString('en-CA', {
    timeZone: TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short'
  });
};

interface User {
  id: string;
  email: string;
  role: 'student' | 'teacher' | 'admin';
  full_name: string;
  avatar_url?: string;
  school?: string;
  grade_level?: string;
  settings?: any;
  created_at?: string;
  email_confirmed_at?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  emailVerificationSent: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;
  signUp: (userData: SignUpData) => Promise<{ success: boolean; message?: string }>;
  signInWithGoogle: () => Promise<{ success: boolean; message?: string }>;
  signOut: () => void;
  updateUser: (userData: Partial<User>) => void;
  formatDate: (date: Date | string) => string;
  resendVerificationEmail: (email: string) => Promise<{ success: boolean; message?: string }>;
  resetPassword: (email: string) => Promise<{ success: boolean; message?: string }>;
  checkEmailVerification: () => Promise<boolean>;
}

interface SignUpData {
  email: string;
  password: string;
  full_name: string;
  role?: 'student' | 'teacher';
  school?: string;
  grade_level?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [emailVerificationSent, setEmailVerificationSent] = useState(false);

  // 检查本地存储的认证状态
  useEffect(() => {
    const checkLocalAuth = () => {
      console.log('AuthContext: Checking local auth...');
      try {
        const storedUser = localStorage.getItem('apcsa_user');
        const storedToken = localStorage.getItem('apcsa_token');
        
        if (storedUser && storedToken) {
          const userData = JSON.parse(storedUser);
          setUser(userData);
          setToken(storedToken);
          console.log('AuthContext: Restored user from localStorage');
        }
      } catch (error) {
        console.warn('Failed to restore auth from localStorage:', error);
        localStorage.removeItem('apcsa_user');
        localStorage.removeItem('apcsa_token');
      } finally {
        setIsLoading(false);
      }
    };

    checkLocalAuth();
  }, []);

  const signIn = async (email: string, password: string): Promise<{ success: boolean; message?: string }> => {
    try {
      setIsLoading(true);
      
      // 调用后端认证API
      const response = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const userData: User = {
          id: result.user.id,
          email: result.user.email,
          role: result.user.role || 'student',
          full_name: result.user.full_name,
          email_confirmed_at: new Date().toISOString(),
          school: result.user.school,
          grade_level: result.user.grade_level,
        };

        setUser(userData);
        setToken(result.data.token.access_token);

        // 保存到本地存储
        localStorage.setItem('apcsa_user', JSON.stringify(userData));
        localStorage.setItem('apcsa_token', result.data.token.access_token);

        return { success: true, message: '登录成功！欢迎回来。' };
      } else {
        return { success: false, message: result.message || '登录失败，请检查邮箱和密码。' };
      }
    } catch (error) {
      console.error('Sign in error:', error);
      // 如果后端不可用，使用演示用户
      const demoUser: User = {
        id: 'demo-user-' + Date.now(),
        email: email,
        role: 'student',
        full_name: email.split('@')[0] || 'Student',
        email_confirmed_at: new Date().toISOString(),
        school: 'APCSA Online',
        grade_level: '11'
      };
      
      const demoToken = 'demo-token-' + Date.now();
      
      setUser(demoUser);
      setToken(demoToken);
      
      localStorage.setItem('apcsa_user', JSON.stringify(demoUser));
      localStorage.setItem('apcsa_token', demoToken);
      
      return { success: true, message: '登录成功！(使用演示模式)' };
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (userData: SignUpData): Promise<{ success: boolean; message?: string }> => {
    try {
      setIsLoading(true);
      
      // 调用后端注册API
      const response = await fetch('http://localhost:8000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        return { 
          success: true, 
          message: '注册成功！请登录您的账户。' 
        };
      } else {
        return { success: false, message: result.message || '注册失败，请重试。' };
      }
    } catch (error) {
      console.error('Sign up error:', error);
      // 演示模式下直接成功
      return { 
        success: true, 
        message: '注册成功！请使用您的邮箱和密码登录。' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = () => {
    setUser(null);
    setToken(null);
    setEmailVerificationSent(false);
    localStorage.removeItem('apcsa_user');
    localStorage.removeItem('apcsa_token');
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('apcsa_user', JSON.stringify(updatedUser));
    }
  };

  // 简化的其他方法
  const signInWithGoogle = async (): Promise<{ success: boolean; message?: string }> => {
    return { success: false, message: 'Google登录暂不可用，请使用邮箱登录。' };
  };

  const resendVerificationEmail = async (email: string): Promise<{ success: boolean; message?: string }> => {
    return { success: true, message: '验证邮件已发送！' };
  };

  const resetPassword = async (email: string): Promise<{ success: boolean; message?: string }> => {
    return { success: true, message: '重置密码链接已发送到您的邮箱！' };
  };

  const checkEmailVerification = async (): Promise<boolean> => {
    return true; // 简化版本，认为所有邮箱都已验证
  };

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated: !!user && !!token,
    emailVerificationSent,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    updateUser,
    formatDate: formatDateForVancouver,
    resendVerificationEmail,
    resetPassword,
    checkEmailVerification,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 