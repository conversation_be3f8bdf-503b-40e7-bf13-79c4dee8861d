import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// 主题定义
export interface Theme {
  id: string;
  name: string;
  description: string;
  icon: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  cssVars: Record<string, string>;
}

// 预定义主题
export const themes: Record<string, Theme> = {
  matrix: {
    id: 'matrix',
    name: 'Matrix Code',
    description: '经典黑客风格，绿色代码雨',
    icon: '💻',
    colors: {
      primary: '#00ff41',
      secondary: '#008f11',
      accent: '#00ff41',
      background: '#0d1117',
      surface: '#161b22',
      text: '#c9d1d9',
      textSecondary: '#8b949e',
      border: '#30363d',
      success: '#238636',
      warning: '#f85149',
      error: '#f85149',
      info: '#58a6ff'
    },
    cssVars: {
      '--color-primary': '#00ff41',
      '--color-secondary': '#008f11',
      '--color-background': '#0d1117',
      '--color-surface': '#161b22',
      '--color-text': '#c9d1d9',
      '--color-text-secondary': '#8b949e',
      '--color-border': '#30363d',
      '--color-success': '#238636',
      '--color-warning': '#f85149',
      '--color-error': '#f85149',
      '--color-info': '#58a6ff'
    }
  },
  
  cyberpunk: {
    id: 'cyberpunk',
    name: 'Cyberpunk 2077',
    description: '未来科幻风格，紫红霓虹',
    icon: '🌃',
    colors: {
      primary: '#ff0080',
      secondary: '#8000ff',
      accent: '#00ffff',
      background: '#0a0a0a',
      surface: '#1a1a2e',
      text: '#ffffff',
      textSecondary: '#b3b3b3',
      border: '#16213e',
      success: '#00ff41',
      warning: '#ffaa00',
      error: '#ff0040',
      info: '#00d4ff'
    },
    cssVars: {
      '--color-primary': '#ff0080',
      '--color-secondary': '#8000ff',
      '--color-background': '#0a0a0a',
      '--color-surface': '#1a1a2e',
      '--color-text': '#ffffff',
      '--color-text-secondary': '#b3b3b3',
      '--color-border': '#16213e',
      '--color-success': '#00ff41',
      '--color-warning': '#ffaa00',
      '--color-error': '#ff0040',
      '--color-info': '#00d4ff'
    }
  },

  oceanic: {
    id: 'oceanic',
    name: 'Deep Ocean',
    description: '深海主题，蓝色渐变',
    icon: '🌊',
    colors: {
      primary: '#0ea5e9',
      secondary: '#0284c7',
      accent: '#06b6d4',
      background: '#0c1326',
      surface: '#1e293b',
      text: '#f1f5f9',
      textSecondary: '#94a3b8',
      border: '#334155',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    cssVars: {
      '--color-primary': '#0ea5e9',
      '--color-secondary': '#0284c7',
      '--color-background': '#0c1326',
      '--color-surface': '#1e293b',
      '--color-text': '#f1f5f9',
      '--color-text-secondary': '#94a3b8',
      '--color-border': '#334155',
      '--color-success': '#10b981',
      '--color-warning': '#f59e0b',
      '--color-error': '#ef4444',
      '--color-info': '#3b82f6'
    }
  },

  forest: {
    id: 'forest',
    name: 'Forest Code',
    description: '自然森林主题，绿色护眼',
    icon: '🌲',
    colors: {
      primary: '#22c55e',
      secondary: '#16a34a',
      accent: '#84cc16',
      background: '#0f1419',
      surface: '#1c2128',
      text: '#e6fffa',
      textSecondary: '#a7f3d0',
      border: '#065f46',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4'
    },
    cssVars: {
      '--color-primary': '#22c55e',
      '--color-secondary': '#16a34a',
      '--color-background': '#0f1419',
      '--color-surface': '#1c2128',
      '--color-text': '#e6fffa',
      '--color-text-secondary': '#a7f3d0',
      '--color-border': '#065f46',
      '--color-success': '#10b981',
      '--color-warning': '#f59e0b',
      '--color-error': '#ef4444',
      '--color-info': '#06b6d4'
    }
  },

  sunset: {
    id: 'sunset',
    name: 'Sunset Glow',
    description: '日落黄昏，温暖橙红',
    icon: '🌅',
    colors: {
      primary: '#f97316',
      secondary: '#ea580c',
      accent: '#fbbf24',
      background: '#1c1917',
      surface: '#292524',
      text: '#fef3c7',
      textSecondary: '#d6d3d1',
      border: '#44403c',
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4'
    },
    cssVars: {
      '--color-primary': '#f97316',
      '--color-secondary': '#ea580c',
      '--color-background': '#1c1917',
      '--color-surface': '#292524',
      '--color-text': '#fef3c7',
      '--color-text-secondary': '#d6d3d1',
      '--color-border': '#44403c',
      '--color-success': '#22c55e',
      '--color-warning': '#f59e0b',
      '--color-error': '#ef4444',
      '--color-info': '#06b6d4'
    }
  },

  light: {
    id: 'light',
    name: 'Clean Light',
    description: '简洁亮色主题，护眼白底',
    icon: '☀️',
    colors: {
      primary: '#3b82f6',
      secondary: '#1d4ed8',
      accent: '#06b6d4',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      textSecondary: '#64748b',
      border: '#e2e8f0',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    cssVars: {
      '--color-primary': '#3b82f6',
      '--color-secondary': '#1d4ed8',
      '--color-background': '#ffffff',
      '--color-surface': '#f8fafc',
      '--color-text': '#1e293b',
      '--color-text-secondary': '#64748b',
      '--color-border': '#e2e8f0',
      '--color-success': '#10b981',
      '--color-warning': '#f59e0b',
      '--color-error': '#ef4444',
      '--color-info': '#3b82f6'
    }
  },

  retrowave: {
    id: 'retrowave',
    name: 'Retro Wave',
    description: '80年代复古蒸汽波',
    icon: '🌴',
    colors: {
      primary: '#ff00ff',
      secondary: '#00ffff',
      accent: '#ffff00',
      background: '#000814',
      surface: '#001d3d',
      text: '#ffffff',
      textSecondary: '#ffd60a',
      border: '#003566',
      success: '#00ff41',
      warning: '#ffaa00',
      error: '#ff0040',
      info: '#00d4ff'
    },
    cssVars: {
      '--color-primary': '#ff00ff',
      '--color-secondary': '#00ffff',
      '--color-background': '#000814',
      '--color-surface': '#001d3d',
      '--color-text': '#ffffff',
      '--color-text-secondary': '#ffd60a',
      '--color-border': '#003566',
      '--color-success': '#00ff41',
      '--color-warning': '#ffaa00',
      '--color-error': '#ff0040',
      '--color-info': '#00d4ff'
    }
  },

  arctic: {
    id: 'arctic',
    name: 'Arctic Code',
    description: '极地冰雪，蓝白清冷',
    icon: '❄️',
    colors: {
      primary: '#67e8f9',
      secondary: '#06b6d4',
      accent: '#e0e7ff',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
      textSecondary: '#cbd5e1',
      border: '#475569',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    cssVars: {
      '--color-primary': '#67e8f9',
      '--color-secondary': '#06b6d4',
      '--color-background': '#0f172a',
      '--color-surface': '#1e293b',
      '--color-text': '#f1f5f9',
      '--color-text-secondary': '#cbd5e1',
      '--color-border': '#475569',
      '--color-success': '#10b981',
      '--color-warning': '#f59e0b',
      '--color-error': '#ef4444',
      '--color-info': '#3b82f6'
    }
  }
};

interface ThemeContextType {
  currentTheme: Theme;
  setTheme: (themeId: string) => void;
  availableThemes: Theme[];
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(themes.matrix);

  useEffect(() => {
    // 从localStorage加载保存的主题
    const savedTheme = localStorage.getItem('apcsa-theme');
    if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(themes[savedTheme]);
    }
  }, []);

  useEffect(() => {
    // 应用CSS变量到根元素
    const root = document.documentElement;
    Object.entries(currentTheme.cssVars).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });

    // 更新body的data-theme属性
    document.body.setAttribute('data-theme', currentTheme.id);
  }, [currentTheme]);

  const setTheme = (themeId: string) => {
    if (themes[themeId]) {
      setCurrentTheme(themes[themeId]);
      localStorage.setItem('apcsa-theme', themeId);
    }
  };

  const value = {
    currentTheme,
    setTheme,
    availableThemes: Object.values(themes)
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}; 