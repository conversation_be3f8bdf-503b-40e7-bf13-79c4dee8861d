@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&family=Source+Code+Pro:wght@400;500;600;700&display=swap');

/* CSS 变量默认值 */
:root {
  --color-primary: #00ff41;
  --color-secondary: #008f11;
  --color-background: #0d1117;
  --color-surface: #161b22;
  --color-text: #c9d1d9;
  --color-text-secondary: #8b949e;
  --color-border: #30363d;
  --color-success: #238636;
  --color-warning: #f85149;
  --color-error: #f85149;
  --color-info: #58a6ff;
}

@layer base {
  body {
    @apply bg-theme-background text-theme-text font-mono transition-colors duration-300;
  }

  html {
    scroll-behavior: smooth;
  }

  * {
    @apply selection:bg-theme-primary selection:text-white transition-colors duration-200;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-theme-surface;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-theme-border rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-theme-primary;
  }
}

@layer components {
  .code-terminal {
    @apply bg-theme-surface border border-theme-border rounded-lg p-4 font-code text-sm text-green-400 transition-colors duration-300;
    background-image: 
      radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }

  .glass-effect {
    @apply bg-theme-surface bg-opacity-80 backdrop-blur-lg border border-theme-border shadow-theme transition-all duration-300;
  }

  .neon-border {
    box-shadow: 0 0 5px var(--color-primary),
                0 0 10px var(--color-primary),
                0 0 15px var(--color-primary);
  }

  .code-block {
    @apply bg-theme-surface border-l-4 border-theme-primary p-4 rounded-r-lg font-code text-sm transition-colors duration-300;
  }

  .matrix-bg {
    background: linear-gradient(45deg, var(--color-background), rgba(0,0,0,0.8));
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(var(--color-primary), 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(var(--color-secondary), 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
    transition: background 0.3s ease;
  }

  .typing-cursor::after {
    content: '';
    @apply inline-block w-0.5 h-5 ml-1 bg-theme-primary animate-blink;
  }

  /* 主题特定背景 */
  [data-theme="matrix"] .matrix-bg {
    background: linear-gradient(45deg, #0a0a0a, #1a1a1a);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(0, 255, 65, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(0, 143, 17, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  [data-theme="cyberpunk"] .matrix-bg {
    background: linear-gradient(45deg, #0a0a0a, #1a1a2e);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(255, 0, 128, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(128, 0, 255, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  [data-theme="oceanic"] .matrix-bg {
    background: linear-gradient(45deg, #0c1326, #1e293b);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(14, 165, 233, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(2, 132, 199, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  [data-theme="forest"] .matrix-bg {
    background: linear-gradient(45deg, #0f1419, #1c2128);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(34, 197, 94, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(22, 163, 74, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  [data-theme="sunset"] .matrix-bg {
    background: linear-gradient(45deg, #1c1917, #292524);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(249, 115, 22, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(234, 88, 12, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  [data-theme="light"] .matrix-bg {
    background: linear-gradient(45deg, #ffffff, #f8fafc);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.05) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(29, 78, 216, 0.05) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  [data-theme="retrowave"] .matrix-bg {
    background: linear-gradient(45deg, #000814, #001d3d);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(255, 0, 255, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(0, 255, 255, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  [data-theme="arctic"] .matrix-bg {
    background: linear-gradient(45deg, #0f172a, #1e293b);
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(103, 232, 249, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(6, 182, 212, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  /* 主题切换动画 */
  .theme-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 发光效果 */
  .glow-effect {
    @apply shadow-glow;
  }

  .glow-effect:hover {
    @apply shadow-glow-lg;
  }

  /* 浮动动画 */
  .float-animation {
    @apply animate-float;
  }

  /* 主题卡片样式 */
  .theme-card {
    @apply glass-effect p-6 rounded-xl hover:shadow-glow transition-all duration-300 hover:scale-105;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .text-glow {
    text-shadow: 0 0 10px var(--color-primary);
  }

  .hover-lift {
    @apply transition-transform hover:scale-105;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-theme-primary to-green-500 bg-clip-text text-transparent;
  }

  .bg-theme-gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  }

  .border-theme-gradient {
    border-image: linear-gradient(135deg, var(--color-primary), var(--color-secondary)) 1;
  }
}
