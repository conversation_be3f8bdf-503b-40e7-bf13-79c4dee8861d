import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { APIResponse, AuthResponse, SignUpData, User, UserProgress } from '@/types';

// API 基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('apcsa_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token 过期，清除本地存储并重定向到登录页
          localStorage.removeItem('apcsa_token');
          localStorage.removeItem('apcsa_user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // 认证相关 API
  async login(email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await this.api.post<AuthResponse>('/auth/login', {
        email,
        password,
      });
      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      throw this.handleError(error);
    }
  }

  async register(userData: SignUpData): Promise<AuthResponse> {
    try {
      const response = await this.api.post<AuthResponse>('/auth/register', userData);
      return response.data;
    } catch (error) {
      console.error('Register error:', error);
      throw this.handleError(error);
    }
  }

  async getCurrentUser(): Promise<APIResponse<User>> {
    try {
      const response = await this.api.get<APIResponse<User>>('/me');
      return response.data;
    } catch (error) {
      console.error('Get current user error:', error);
      throw this.handleError(error);
    }
  }

  // 课程相关 API
  async getUnits(): Promise<APIResponse> {
    try {
      const response = await this.api.get('/units');
      return response.data;
    } catch (error) {
      console.error('Get units error:', error);
      throw this.handleError(error);
    }
  }

  async getUnit(unitId: string): Promise<APIResponse> {
    try {
      const response = await this.api.get(`/units/${unitId}`);
      return response.data;
    } catch (error) {
      console.error('Get unit error:', error);
      throw this.handleError(error);
    }
  }

  async getTopics(unitId: string): Promise<APIResponse> {
    try {
      const response = await this.api.get(`/units/${unitId}/topics`);
      return response.data;
    } catch (error) {
      console.error('Get topics error:', error);
      throw this.handleError(error);
    }
  }

  async getExercises(topicId: string): Promise<APIResponse> {
    try {
      const response = await this.api.get(`/topics/${topicId}/exercises`);
      return response.data;
    } catch (error) {
      console.error('Get exercises error:', error);
      throw this.handleError(error);
    }
  }

  // 用户进度相关 API
  async getUserProgress(): Promise<APIResponse<UserProgress>> {
    try {
      const response = await this.api.get<APIResponse<UserProgress>>('/user/progress');
      return response.data;
    } catch (error) {
      console.error('Get user progress error:', error);
      throw this.handleError(error);
    }
  }

  async getUserStats(): Promise<APIResponse> {
    try {
      const response = await this.api.get('/user/stats');
      return response.data;
    } catch (error) {
      console.error('Get user stats error:', error);
      throw this.handleError(error);
    }
  }

  // AI 相关 API
  async explainConcept(data: {
    topic: string;
    concept: string;
    user_level?: string;
  }): Promise<APIResponse> {
    try {
      const response = await this.api.post('/ai/explain', data);
      return response.data;
    } catch (error) {
      console.error('AI explain concept error:', error);
      throw this.handleError(error);
    }
  }

  async reviewCode(data: {
    code: string;
    exercise_type?: string;
  }): Promise<APIResponse> {
    try {
      const response = await this.api.post('/ai/review-code', data);
      return response.data;
    } catch (error) {
      console.error('AI review code error:', error);
      throw this.handleError(error);
    }
  }

  async generateHint(data: {
    exercise_description: string;
    student_code?: string;
  }): Promise<APIResponse> {
    try {
      const response = await this.api.post('/ai/hint', data);
      return response.data;
    } catch (error) {
      console.error('AI generate hint error:', error);
      throw this.handleError(error);
    }
  }

  async askAI(data: {
    question: string;
    context?: string;
  }): Promise<APIResponse> {
    try {
      const response = await this.api.post('/ai/ask', data);
      return response.data;
    } catch (error) {
      console.error('AI ask error:', error);
      throw this.handleError(error);
    }
  }

  // 错误处理
  private handleError(error: any): Error {
    if (error.response) {
      // 服务器响应错误
      const message = error.response.data?.message || 'Server error occurred';
      return new Error(message);
    } else if (error.request) {
      // 网络错误
      return new Error('Network error: Unable to connect to server');
    } else {
      // 其他错误
      return new Error(error.message || 'An unexpected error occurred');
    }
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.api.get('/health');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
}

// 导出单例实例
export const apiService = new ApiService();
export default apiService;
