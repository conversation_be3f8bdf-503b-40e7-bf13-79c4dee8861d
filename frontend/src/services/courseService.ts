// =============================================
// APCSA Course Management Service
// =============================================

import { 
  Unit, Topic, Exercise, LearningResource,
  CreateUnitRequest, UpdateUnitRequest,
  CreateTopicRequest, UpdateTopicRequest,
  CreateExerciseRequest, UpdateExerciseRequest,
  SubmitExerciseRequest, UpdateProgressRequest,
  CourseStats, UnitStats
} from '../types/course';

const API_BASE_URL = 'http://localhost:8000/api';

// Mock data for development
const mockUnits: Unit[] = [
  {
    id: 1,
    unit_code: 'unit1',
    title: 'Primitive Types',
    description: 'Introduction to Java primitive data types, variables, and basic operations',
    order_index: 1,
    is_active: true,
    estimated_hours: 8,
    difficulty_level: 1,
    prerequisites: [],
    learning_objectives: [
      'Understand primitive data types',
      'Declare and initialize variables',
      'Perform arithmetic operations',
      'Use casting and type conversion'
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 85,
    student_count: 120
  },
  {
    id: 2,
    unit_code: 'unit2',
    title: 'Using Objects',
    description: 'Working with objects, methods, and the String class',
    order_index: 2,
    is_active: true,
    estimated_hours: 10,
    difficulty_level: 2,
    prerequisites: ['unit1'],
    learning_objectives: [
      'Create and use objects',
      'Call methods on objects',
      'Work with String objects',
      'Understand object references'
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 72,
    student_count: 98
  },
  {
    id: 3,
    unit_code: 'unit3',
    title: 'Boolean Expressions and if Statements',
    description: 'Conditional logic and boolean expressions',
    order_index: 3,
    is_active: true,
    estimated_hours: 8,
    difficulty_level: 2,
    prerequisites: ['unit1', 'unit2'],
    learning_objectives: [
      'Write boolean expressions',
      'Use if statements',
      'Implement nested conditionals',
      'Apply De Morgan\'s laws'
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 68,
    student_count: 87
  },
  {
    id: 4,
    unit_code: 'unit4',
    title: 'Iteration',
    description: 'Loops and iterative algorithms',
    order_index: 4,
    is_active: true,
    estimated_hours: 10,
    difficulty_level: 3,
    prerequisites: ['unit1', 'unit2', 'unit3'],
    learning_objectives: [
      'Write for loops',
      'Use while loops',
      'Implement nested loops',
      'Develop iterative algorithms'
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 45,
    student_count: 65
  },
  {
    id: 5,
    unit_code: 'unit5',
    title: 'Writing Classes',
    description: 'Object-oriented programming fundamentals',
    order_index: 5,
    is_active: true,
    estimated_hours: 12,
    difficulty_level: 3,
    prerequisites: ['unit1', 'unit2', 'unit3', 'unit4'],
    learning_objectives: [
      'Design classes',
      'Write constructors',
      'Implement methods',
      'Use access modifiers'
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 32,
    student_count: 45
  }
];

const mockTopics: Topic[] = [
  {
    id: 1,
    unit_id: 1,
    topic_code: '1-1',
    title: 'Variables and Data Types',
    description: 'Introduction to primitive data types and variable declaration',
    order_index: 1,
    is_active: true,
    estimated_minutes: 45,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 90
  },
  {
    id: 2,
    unit_id: 1,
    topic_code: '1-2',
    title: 'Arithmetic Operations',
    description: 'Mathematical operations and operator precedence',
    order_index: 2,
    is_active: true,
    estimated_minutes: 45,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 85
  },
  {
    id: 3,
    unit_id: 1,
    topic_code: '1-3',
    title: 'Type Casting',
    description: 'Converting between data types',
    order_index: 3,
    is_active: true,
    estimated_minutes: 30,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    completion_rate: 78
  }
];

class CourseService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('apcsa_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Units API
  async getUnits(): Promise<Unit[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/units`, {
        headers: this.getAuthHeaders()
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock data');
    }
    
    // Return mock data if API is not available
    return mockUnits;
  }

  async getUnit(id: number): Promise<Unit | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/units/${id}`, {
        headers: this.getAuthHeaders()
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock data');
    }
    
    // Return mock data
    return mockUnits.find(unit => unit.id === id) || null;
  }

  async createUnit(unitData: CreateUnitRequest): Promise<Unit> {
    try {
      const response = await fetch(`${API_BASE_URL}/units`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(unitData)
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock response');
    }
    
    // Mock response
    const newUnit: Unit = {
      id: Date.now(),
      ...unitData,
      is_active: true,
      prerequisites: unitData.prerequisites || [],
      learning_objectives: unitData.learning_objectives || [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      completion_rate: 0,
      student_count: 0
    };
    
    return newUnit;
  }

  async updateUnit(id: number, unitData: UpdateUnitRequest): Promise<Unit> {
    try {
      const response = await fetch(`${API_BASE_URL}/units/${id}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(unitData)
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock response');
    }
    
    // Mock response
    const existingUnit = mockUnits.find(unit => unit.id === id);
    if (!existingUnit) throw new Error('Unit not found');
    
    return {
      ...existingUnit,
      ...unitData,
      updated_at: new Date().toISOString()
    };
  }

  async deleteUnit(id: number): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/units/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      
      return response.ok;
    } catch (error) {
      console.warn('API not available, using mock response');
      return true;
    }
  }

  // Topics API
  async getTopics(unitId?: number): Promise<Topic[]> {
    try {
      const url = unitId 
        ? `${API_BASE_URL}/units/${unitId}/topics`
        : `${API_BASE_URL}/topics`;
        
      const response = await fetch(url, {
        headers: this.getAuthHeaders()
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock data');
    }
    
    // Return mock data
    return unitId 
      ? mockTopics.filter(topic => topic.unit_id === unitId)
      : mockTopics;
  }

  async getTopic(id: number): Promise<Topic | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/topics/${id}`, {
        headers: this.getAuthHeaders()
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock data');
    }
    
    return mockTopics.find(topic => topic.id === id) || null;
  }

  // Course Statistics
  async getCourseStats(): Promise<CourseStats> {
    try {
      const response = await fetch(`${API_BASE_URL}/stats/course`, {
        headers: this.getAuthHeaders()
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock data');
    }
    
    // Mock stats
    return {
      total_units: 10,
      completed_units: 2,
      total_topics: 25,
      completed_topics: 8,
      total_exercises: 150,
      completed_exercises: 45,
      total_study_time: 3600, // seconds
      average_score: 87.5,
      current_streak: 5,
      last_activity: new Date().toISOString()
    };
  }

  async getUnitStats(): Promise<UnitStats[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/stats/units`, {
        headers: this.getAuthHeaders()
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.data || result;
      }
    } catch (error) {
      console.warn('API not available, using mock data');
    }
    
    // Mock unit stats
    return mockUnits.map(unit => ({
      unit_id: unit.id,
      unit_title: unit.title,
      total_students: unit.student_count || 0,
      completed_students: Math.floor((unit.completion_rate || 0) * (unit.student_count || 0) / 100),
      average_completion_time: unit.estimated_hours * 3600, // convert to seconds
      average_score: 75 + Math.random() * 20, // Random score between 75-95
      difficulty_rating: unit.difficulty_level,
      most_challenging_topic: 'Sample Topic'
    }));
  }
}

export const courseService = new CourseService();
export default courseService;
