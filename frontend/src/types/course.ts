// =============================================
// APCSA Course Management Types
// =============================================

export interface Unit {
  id: number;
  unit_code: string;
  title: string;
  description: string;
  order_index: number;
  is_active: boolean;
  estimated_hours: number;
  difficulty_level: number; // 1-5
  prerequisites: string[];
  learning_objectives: string[];
  created_at: string;
  updated_at: string;
  
  // Extended properties
  topics?: Topic[];
  progress?: UnitProgress;
  completion_rate?: number;
  student_count?: number;
}

export interface Topic {
  id: number;
  unit_id: number;
  topic_code: string;
  title: string;
  description: string;
  order_index: number;
  is_active: boolean;
  estimated_minutes: number;
  created_at: string;
  updated_at: string;
  
  // Extended properties
  lessons?: Lesson[];
  exercises?: Exercise[];
  progress?: TopicProgress;
  completion_rate?: number;
}

export interface Lesson {
  id: number;
  topic_id: number;
  lesson_code: string;
  title: string;
  content_type: 'text' | 'video' | 'interactive' | 'quiz';
  content_data: any;
  order_index: number;
  is_active: boolean;
  estimated_minutes: number;
  created_at: string;
  updated_at: string;
  
  // Extended properties
  progress?: LessonProgress;
  is_completed?: boolean;
}

export interface Exercise {
  id: number;
  topic_id: number;
  exercise_code: string;
  title: string;
  description: string;
  exercise_type: 'coding' | 'multiple_choice' | 'fill_blank' | 'essay';
  difficulty_level: number; // 1-5
  points: number;
  time_limit_minutes?: number;
  content_data: any;
  solution_data?: any;
  hints: string[];
  tags: string[];
  is_active: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
  
  // Extended properties
  submissions?: ExerciseSubmission[];
  best_score?: number;
  attempt_count?: number;
  is_completed?: boolean;
}

export interface UnitProgress {
  id: number;
  user_id: string;
  unit_id: number;
  progress_type: 'unit';
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  completion_percentage: number;
  time_spent_seconds: number;
  last_accessed_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface TopicProgress {
  id: number;
  user_id: string;
  topic_id: number;
  progress_type: 'topic';
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  completion_percentage: number;
  time_spent_seconds: number;
  last_accessed_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface LessonProgress {
  id: number;
  user_id: string;
  lesson_id: number;
  progress_type: 'lesson';
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  completion_percentage: number;
  time_spent_seconds: number;
  last_accessed_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ExerciseSubmission {
  id: number;
  user_id: string;
  exercise_id: number;
  submission_data: any;
  score?: number;
  max_score?: number;
  is_correct?: boolean;
  feedback?: string;
  time_spent_seconds?: number;
  attempt_number: number;
  submitted_at: string;
}

export interface LearningResource {
  id: number;
  title: string;
  description?: string;
  resource_type: 'lesson_plan' | 'video' | 'document' | 'exercise' | 'quiz' | 'image' | 'link';
  unit_id?: number;
  topic_id?: number;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  external_url?: string;
  content_data: any;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  download_count: number;
  view_count: number;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

// API Request/Response types
export interface CreateUnitRequest {
  unit_code: string;
  title: string;
  description?: string;
  order_index: number;
  estimated_hours?: number;
  difficulty_level?: number;
  prerequisites?: string[];
  learning_objectives?: string[];
}

export interface UpdateUnitRequest {
  title?: string;
  description?: string;
  is_active?: boolean;
  estimated_hours?: number;
  difficulty_level?: number;
  prerequisites?: string[];
  learning_objectives?: string[];
}

export interface CreateTopicRequest {
  unit_id: number;
  topic_code: string;
  title: string;
  description?: string;
  order_index: number;
  estimated_minutes?: number;
}

export interface UpdateTopicRequest {
  title?: string;
  description?: string;
  is_active?: boolean;
  estimated_minutes?: number;
}

export interface CreateExerciseRequest {
  topic_id: number;
  exercise_code: string;
  title: string;
  description?: string;
  exercise_type: 'coding' | 'multiple_choice' | 'fill_blank' | 'essay';
  difficulty_level?: number;
  points?: number;
  time_limit_minutes?: number;
  content_data: any;
  solution_data?: any;
  hints?: string[];
  tags?: string[];
}

export interface UpdateExerciseRequest {
  title?: string;
  description?: string;
  difficulty_level?: number;
  points?: number;
  time_limit_minutes?: number;
  content_data?: any;
  solution_data?: any;
  hints?: string[];
  tags?: string[];
  is_active?: boolean;
}

export interface SubmitExerciseRequest {
  exercise_id: number;
  submission_data: any;
  time_spent_seconds?: number;
}

export interface UpdateProgressRequest {
  status?: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  completion_percentage?: number;
  time_spent_seconds?: number;
}

// Dashboard and Analytics types
export interface CourseStats {
  total_units: number;
  completed_units: number;
  total_topics: number;
  completed_topics: number;
  total_exercises: number;
  completed_exercises: number;
  total_study_time: number;
  average_score: number;
  current_streak: number;
  last_activity: string;
}

export interface UnitStats {
  unit_id: number;
  unit_title: string;
  total_students: number;
  completed_students: number;
  average_completion_time: number;
  average_score: number;
  difficulty_rating: number;
  most_challenging_topic?: string;
}

export interface LearningPath {
  id: string;
  name: string;
  description: string;
  units: Unit[];
  estimated_duration: number;
  difficulty_level: number;
  prerequisites: string[];
  learning_outcomes: string[];
}
