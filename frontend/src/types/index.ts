// 用户相关类型
export interface User {
  id: string;
  email: string;
  role: 'student' | 'teacher' | 'admin';
  full_name: string;
  avatar_url?: string;
  school?: string;
  grade_level?: string;
  settings?: Record<string, any>;
  created_at?: string;
  email_confirmed_at?: string;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  emailVerificationSent: boolean;
  signIn: (email: string, password: string) => Promise<AuthResponse>;
  signUp: (userData: SignUpData) => Promise<AuthResponse>;
  signInWithGoogle: () => Promise<AuthResponse>;
  signOut: () => void;
  updateUser: (userData: Partial<User>) => void;
  formatDate: (date: Date | string) => string;
  resendVerificationEmail: (email: string) => Promise<AuthResponse>;
  resetPassword: (email: string) => Promise<AuthResponse>;
  checkEmailVerification: () => Promise<boolean>;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  user?: User;
  token?: string;
}

export interface SignUpData {
  email: string;
  password: string;
  full_name: string;
  role?: 'student' | 'teacher';
  school?: string | undefined;
  grade_level?: string | undefined;
}

// 课程相关类型
export interface Unit {
  id: string;
  title: string;
  description?: string;
  order_index: number;
  estimated_hours: number;
  prerequisites?: string[];
  is_active: boolean;
  created_at?: string;
}

export interface Topic {
  id: string;
  unit_id: string;
  title: string;
  description?: string;
  order_index: number;
  estimated_hours: number;
  learning_objectives?: string[];
  is_active: boolean;
  created_at?: string;
}

export interface Exercise {
  id: string;
  topic_id: string;
  title: string;
  description?: string;
  difficulty: 1 | 2 | 3 | 4 | 5;
  points: number;
  content: Record<string, any>;
  solution?: Record<string, any>;
  hints?: string[];
  created_at?: string;
}

// 学习进度相关类型
export interface UserProgress {
  totalUnits: number;
  completedUnits: number;
  inProgressUnits: number;
  totalExercises: number;
  completedExercises: number;
  totalTimeSpent: number;
  currentStreak: number;
  achievements: Achievement[];
  weeklyProgress: WeeklyProgress[];
  recentActivities: RecentActivity[];
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  earned_at: string;
}

export interface WeeklyProgress {
  week: string;
  hours: number;
  exercises: number;
}

export interface RecentActivity {
  type: 'quiz' | 'assignment' | 'video' | 'exercise';
  title: string;
  score?: number;
  duration?: string;
  time: string;
  unit?: string;
}

// API 响应类型
export interface APIResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

// 主题相关类型
export interface Theme {
  name: string;
  displayName: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

export interface ThemeContextType {
  currentTheme: Theme;
  themeName: string;
  setTheme: (themeName: string) => void;
  themes: Record<string, Theme>;
}

// AI 助手相关类型
export interface AIContext {
  topic: string;
  unitId?: string;
  exerciseId?: string;
  code?: string;
}

export interface AIMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// 错误处理类型
export interface AppError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// 组件 Props 类型
export interface LayoutProps {
  children: React.ReactNode;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

// 表单相关类型
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

// 路由相关类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  protected?: boolean;
  exact?: boolean;
}
