// 应用配置
export const config = {
  // API 配置
  api: {
    baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:8000',
    timeout: parseInt(process.env.REACT_APP_API_TIMEOUT || '10000'),
  },

  // 应用信息
  app: {
    name: process.env.REACT_APP_NAME || 'APCSA AI Learning Platform',
    version: process.env.REACT_APP_VERSION || '1.0.0',
    environment: process.env.REACT_APP_ENVIRONMENT || 'development',
  },

  // 功能开关
  features: {
    aiAssistant: process.env.REACT_APP_ENABLE_AI_ASSISTANT === 'true',
    codeEditor: process.env.REACT_APP_ENABLE_CODE_EDITOR === 'true',
    analytics: process.env.REACT_APP_ENABLE_ANALYTICS === 'true',
  },

  // 外部服务
  services: {
    googleAnalyticsId: process.env.REACT_APP_GOOGLE_ANALYTICS_ID,
    sentryDsn: process.env.REACT_APP_SENTRY_DSN,
  },

  // 开发配置
  development: {
    debug: process.env.REACT_APP_DEBUG === 'true',
    logLevel: process.env.REACT_APP_LOG_LEVEL || 'info',
  },

  // 主题配置
  theme: {
    default: process.env.REACT_APP_DEFAULT_THEME || 'matrix',
  },

  // 本地存储键名
  storage: {
    user: 'apcsa_user',
    token: 'apcsa_token',
    theme: 'apcsa_theme',
    preferences: 'apcsa_preferences',
  },

  // 分页配置
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100,
  },

  // 文件上传配置
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['.java', '.txt', '.pdf', '.doc', '.docx'],
  },

  // 代码编辑器配置
  editor: {
    theme: 'vs-dark',
    fontSize: 14,
    tabSize: 2,
    wordWrap: 'on',
  },
} as const;

// 类型定义
export type Config = typeof config;

// 环境检查函数
export const isDevelopment = () => config.app.environment === 'development';
export const isProduction = () => config.app.environment === 'production';
export const isTest = () => config.app.environment === 'test';

// 调试日志函数
export const debugLog = (...args: any[]) => {
  if (config.development.debug && isDevelopment()) {
    console.log('[DEBUG]', ...args);
  }
};

export default config;
