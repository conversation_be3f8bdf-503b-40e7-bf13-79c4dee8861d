{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/contexts/*": ["contexts/*"], "@/hooks/*": ["hooks/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/services/*": ["services/*"]}, "incremental": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false}, "include": ["src/**/*"], "exclude": ["node_modules", "build", "dist"]}