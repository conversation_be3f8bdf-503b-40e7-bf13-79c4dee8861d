{"name": "apcsa-ai-platform", "version": "1.0.0", "private": true, "description": "APCSA AI Learning Platform - Monorepo", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm start", "dev:backend": "cd backend && poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "echo 'No frontend tests'", "test:backend": "cd backend && pytest", "lint": "npm run lint:frontend", "lint:frontend": "cd frontend && npm run lint", "clean": "rm -rf frontend/dist frontend/node_modules backend/__pycache__ backend/.pytest_cache"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/apcsa-ai-platform.git"}, "keywords": ["apcsa", "education", "ai", "learning", "react", "<PERSON><PERSON><PERSON>", "supabase"], "author": "Your Name", "license": "MIT"}