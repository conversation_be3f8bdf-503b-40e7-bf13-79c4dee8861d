"""
CodeHS APCSA 内容爬虫
使用Scrapy框架实现分布式抓取
"""

import scrapy
import json
import hashlib
from urllib.parse import urljoin, urlparse
from scrapy_redis.spiders import RedisSpider
from scrapy.http import Request
from datetime import datetime
import logging

from ..items import CodeHSContentItem, LessonItem, QuizItem
from ..utils.content_processor import ContentProcessor
from ..utils.database_manager import DatabaseManager


class CodeHSSpider(RedisSpider):
    """
    CodeHS APCSA 课程内容爬虫
    
    特性:
    - 分布式抓取支持 (基于Redis)
    - 智能去重和增量更新
    - 内容结构化处理
    - 自动重试和错误恢复
    - 实时监控和指标收集
    """
    
    name = 'codehs_spider'
    redis_key = 'codehs:start_urls'
    
    # 爬虫配置
    allowed_domains = ['codehs.com']
    
    # 请求设置
    download_delay = 2  # 2秒延迟，避免过于频繁的请求
    randomize_download_delay = True
    concurrent_requests = 16
    concurrent_requests_per_domain = 8
    
    # 自定义设置
    custom_settings = {
        'ITEM_PIPELINES': {
            'apcsa_scraper.pipelines.ValidationPipeline': 300,
            'apcsa_scraper.pipelines.DuplicationFilterPipeline': 400,
            'apcsa_scraper.pipelines.ContentProcessingPipeline': 500,
            'apcsa_scraper.pipelines.DatabasePipeline': 600,
            'apcsa_scraper.pipelines.MonitoringPipeline': 700,
        },
        'DOWNLOADER_MIDDLEWARES': {
            'apcsa_scraper.middlewares.RotateUserAgentMiddleware': 400,
            'apcsa_scraper.middlewares.ProxyMiddleware': 410,
            'apcsa_scraper.middlewares.RetryMiddleware': 500,
            'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
        },
        'EXTENSIONS': {
            'apcsa_scraper.extensions.PrometheusExtension': 500,
        }
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.content_processor = ContentProcessor()
        self.db_manager = DatabaseManager()
        self.logger = logging.getLogger(self.name)
        
        # 统计信息
        self.stats = {
            'pages_crawled': 0,
            'lessons_extracted': 0,
            'quizzes_extracted': 0,
            'errors': 0,
            'duplicates_filtered': 0
        }
    
    def start_requests(self):
        """
        生成初始请求
        支持从Redis队列和预定义URL列表启动
        """
        # 预定义的起始URL
        start_urls = [
            'https://codehs.com/textbook/apcsa_textbook/',
            'https://codehs.com/textbook/apcsa_textbook/1.1/',
            'https://codehs.com/textbook/apcsa_textbook/1.2/',
            'https://codehs.com/textbook/apcsa_textbook/1.3/',
            'https://codehs.com/textbook/apcsa_textbook/1.4/',
            'https://codehs.com/textbook/apcsa_textbook/1.5/',
            'https://codehs.com/textbook/apcsa_textbook/1.6/',
        ]
        
        for url in start_urls:
            yield Request(
                url=url,
                callback=self.parse_lesson_page,
                meta={
                    'unit_number': self.extract_unit_number(url),
                    'lesson_number': self.extract_lesson_number(url),
                    'priority': 10
                },
                dont_filter=False
            )
    
    def parse_lesson_page(self, response):
        """
        解析课程页面
        提取课程内容、练习题和相关资源
        """
        try:
            self.stats['pages_crawled'] += 1
            
            # 提取基本信息
            unit_number = response.meta.get('unit_number')
            lesson_number = response.meta.get('lesson_number')
            
            # 提取页面标题
            title = response.css('h1.lesson-title::text').get()
            if not title:
                title = response.css('title::text').get()
            
            # 提取课程内容
            content_sections = response.css('.lesson-content')
            
            lesson_content = {
                'title': title,
                'unit_number': unit_number,
                'lesson_number': lesson_number,
                'url': response.url,
                'sections': []
            }
            
            # 处理每个内容部分
            for section in content_sections:
                section_data = self.extract_content_section(section)
                if section_data:
                    lesson_content['sections'].append(section_data)
            
            # 创建课程Item
            lesson_item = LessonItem()
            lesson_item['title'] = title
            lesson_item['unit_number'] = unit_number
            lesson_item['lesson_number'] = lesson_number
            lesson_item['content'] = lesson_content
            lesson_item['url'] = response.url
            lesson_item['scraped_at'] = datetime.now().isoformat()
            lesson_item['content_hash'] = self.generate_content_hash(lesson_content)
            
            yield lesson_item
            self.stats['lessons_extracted'] += 1
            
            # 查找并处理练习题
            quiz_sections = response.css('.quiz-section, .exercise-section')
            for quiz_section in quiz_sections:
                quiz_item = self.extract_quiz_content(quiz_section, unit_number, lesson_number)
                if quiz_item:
                    yield quiz_item
                    self.stats['quizzes_extracted'] += 1
            
            # 查找下一页链接
            next_page_links = response.css('a.next-lesson, a.continue-button::attr(href)').getall()
            for next_url in next_page_links:
                if next_url:
                    next_full_url = urljoin(response.url, next_url)
                    yield Request(
                        url=next_full_url,
                        callback=self.parse_lesson_page,
                        meta={
                            'unit_number': self.extract_unit_number(next_full_url),
                            'lesson_number': self.extract_lesson_number(next_full_url),
                            'priority': 5
                        }
                    )
            
            # 查找相关资源链接
            resource_links = response.css('a[href*="resource"], a[href*="example"]::attr(href)').getall()
            for resource_url in resource_links:
                if resource_url:
                    resource_full_url = urljoin(response.url, resource_url)
                    yield Request(
                        url=resource_full_url,
                        callback=self.parse_resource_page,
                        meta={
                            'unit_number': unit_number,
                            'lesson_number': lesson_number,
                            'resource_type': 'example',
                            'priority': 3
                        }
                    )
                    
        except Exception as e:
            self.logger.error(f"解析课程页面时出错 {response.url}: {str(e)}")
            self.stats['errors'] += 1
    
    def extract_content_section(self, section_selector):
        """
        提取内容部分的详细信息
        """
        try:
            section_data = {
                'type': 'content',
                'heading': section_selector.css('h2, h3, h4::text').get(),
                'paragraphs': section_selector.css('p::text').getall(),
                'code_blocks': [],
                'images': [],
                'links': []
            }
            
            # 提取代码块
            code_blocks = section_selector.css('pre code, .code-block')
            for code_block in code_blocks:
                code_text = code_block.css('::text').get()
                language = code_block.css('::attr(class)').re_first(r'language-(\w+)')
                if code_text:
                    section_data['code_blocks'].append({
                        'code': code_text.strip(),
                        'language': language or 'java'
                    })
            
            # 提取图片
            images = section_selector.css('img')
            for img in images:
                img_src = img.css('::attr(src)').get()
                img_alt = img.css('::attr(alt)').get()
                if img_src:
                    section_data['images'].append({
                        'src': img_src,
                        'alt': img_alt,
                        'caption': img.xpath('following-sibling::*[1][self::p]/text()').get()
                    })
            
            # 提取链接
            links = section_selector.css('a[href]')
            for link in links:
                href = link.css('::attr(href)').get()
                text = link.css('::text').get()
                if href and text:
                    section_data['links'].append({
                        'url': href,
                        'text': text
                    })
            
            return section_data
            
        except Exception as e:
            self.logger.error(f"提取内容部分时出错: {str(e)}")
            return None
    
    def extract_quiz_content(self, quiz_selector, unit_number, lesson_number):
        """
        提取练习题内容
        """
        try:
            quiz_item = QuizItem()
            
            # 基本信息
            quiz_item['unit_number'] = unit_number
            quiz_item['lesson_number'] = lesson_number
            quiz_item['title'] = quiz_selector.css('h3, h4::text').get()
            quiz_item['type'] = 'practice'
            
            # 提取问题
            questions = []
            question_elements = quiz_selector.css('.question, .quiz-question')
            
            for i, question_elem in enumerate(question_elements):
                question_data = {
                    'id': f"{unit_number}.{lesson_number}.{i+1}",
                    'question_text': question_elem.css('.question-text::text').get(),
                    'question_type': 'multiple_choice',  # 默认类型
                    'options': [],
                    'correct_answer': None,
                    'explanation': None
                }
                
                # 提取选项
                options = question_elem.css('.option, .choice')
                for j, option in enumerate(options):
                    option_text = option.css('::text').get()
                    is_correct = 'correct' in option.css('::attr(class)').get('')
                    
                    option_data = {
                        'id': chr(65 + j),  # A, B, C, D
                        'text': option_text,
                        'is_correct': is_correct
                    }
                    question_data['options'].append(option_data)
                    
                    if is_correct:
                        question_data['correct_answer'] = chr(65 + j)
                
                # 提取解释
                explanation = question_elem.css('.explanation, .answer-explanation::text').get()
                if explanation:
                    question_data['explanation'] = explanation
                
                questions.append(question_data)
            
            quiz_item['questions'] = questions
            quiz_item['question_count'] = len(questions)
            quiz_item['scraped_at'] = datetime.now().isoformat()
            quiz_item['content_hash'] = self.generate_content_hash(questions)
            
            return quiz_item
            
        except Exception as e:
            self.logger.error(f"提取练习题时出错: {str(e)}")
            return None
    
    def parse_resource_page(self, response):
        """
        解析资源页面（示例代码、补充材料等）
        """
        try:
            unit_number = response.meta.get('unit_number')
            lesson_number = response.meta.get('lesson_number')
            resource_type = response.meta.get('resource_type', 'resource')
            
            # 创建资源Item
            resource_item = CodeHSContentItem()
            resource_item['title'] = response.css('h1, h2::text').get()
            resource_item['unit_number'] = unit_number
            resource_item['lesson_number'] = lesson_number
            resource_item['content_type'] = resource_type
            resource_item['url'] = response.url
            
            # 提取内容
            content = {
                'text': response.css('.content, .resource-content::text').getall(),
                'code_examples': [],
                'downloads': []
            }
            
            # 提取代码示例
            code_blocks = response.css('pre code, .code-example')
            for code_block in code_blocks:
                code_text = code_block.css('::text').get()
                if code_text:
                    content['code_examples'].append({
                        'code': code_text.strip(),
                        'language': 'java',
                        'description': code_block.xpath('preceding-sibling::*[1]/text()').get()
                    })
            
            # 提取下载链接
            download_links = response.css('a[href$=".java"], a[href$=".zip"], a[href$=".pdf"]')
            for link in download_links:
                href = link.css('::attr(href)').get()
                text = link.css('::text').get()
                if href:
                    content['downloads'].append({
                        'url': urljoin(response.url, href),
                        'filename': text or href.split('/')[-1],
                        'type': href.split('.')[-1]
                    })
            
            resource_item['content'] = content
            resource_item['scraped_at'] = datetime.now().isoformat()
            resource_item['content_hash'] = self.generate_content_hash(content)
            
            yield resource_item
            
        except Exception as e:
            self.logger.error(f"解析资源页面时出错 {response.url}: {str(e)}")
            self.stats['errors'] += 1
    
    def extract_unit_number(self, url):
        """从URL中提取单元号"""
        try:
            # 匹配模式: /1.1/, /2.3/, etc.
            import re
            match = re.search(r'/(\d+)\.\d+/', url)
            if match:
                return int(match.group(1))
            return None
        except:
            return None
    
    def extract_lesson_number(self, url):
        """从URL中提取课程号"""
        try:
            import re
            match = re.search(r'/\d+\.(\d+)/', url)
            if match:
                return int(match.group(1))
            return None
        except:
            return None
    
    def generate_content_hash(self, content):
        """生成内容哈希用于去重"""
        content_str = json.dumps(content, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(content_str.encode('utf-8')).hexdigest()
    
    def closed(self, reason):
        """爬虫关闭时的清理工作"""
        self.logger.info(f"爬虫关闭: {reason}")
        self.logger.info(f"统计信息: {self.stats}")
        
        # 发送统计信息到监控系统
        try:
            from ..utils.metrics_collector import MetricsCollector
            metrics = MetricsCollector()
            metrics.record_spider_stats(self.name, self.stats)
        except Exception as e:
            self.logger.error(f"发送统计信息失败: {str(e)}") 