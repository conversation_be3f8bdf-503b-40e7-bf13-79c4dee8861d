#!/bin/bash

# 🚀 一键部署到Ubuntu服务器脚本
# 使用方法: ./scripts/deploy-to-ubuntu.sh

set -e  # 遇到错误立即退出

echo "🌍 开始跨平台部署流程..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量 (可以通过环境变量覆盖)
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"your-registry"}
IMAGE_NAME=${IMAGE_NAME:-"apcsa-backend"}
SERVER_HOST=${SERVER_HOST:-"your-server.com"}
SERVER_USER=${SERVER_USER:-"ubuntu"}
DEPLOY_PATH=${DEPLOY_PATH:-"/opt/apcsa-ai-platform"}

# 函数: 打印彩色消息
print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    print_step "检查部署要求..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! docker buildx version &> /dev/null; then
        print_error "Docker buildx 不可用，请升级 Docker"
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        print_error "SSH 不可用"
        exit 1
    fi
    
    echo "✅ 所有要求已满足"
}

# 构建跨平台镜像
build_image() {
    print_step "构建 x86_64 Docker 镜像..."
    
    # 确保 buildx 可用
    docker buildx create --use --name apcsa-builder --driver docker-container 2>/dev/null || true
    
    # 获取 Git commit hash 作为 tag
    GIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "latest")
    
    # 构建镜像 (只构建 x86_64，因为目标是Ubuntu服务器)
    docker buildx build \
        --platform linux/amd64 \
        --tag ${DOCKER_REGISTRY}/${IMAGE_NAME}:${GIT_HASH} \
        --tag ${DOCKER_REGISTRY}/${IMAGE_NAME}:latest \
        --load \
        backend/
    
    echo "✅ 镜像构建完成: ${IMAGE_NAME}:${GIT_HASH}"
}

# 推送镜像 (如果使用外部仓库)
push_image() {
    if [[ "${DOCKER_REGISTRY}" != "local" ]]; then
        print_step "推送镜像到仓库..."
        docker push ${DOCKER_REGISTRY}/${IMAGE_NAME}:latest
        docker push ${DOCKER_REGISTRY}/${IMAGE_NAME}:${GIT_HASH}
        echo "✅ 镜像推送完成"
    else
        print_warning "使用本地镜像，跳过推送步骤"
    fi
}

# 准备部署文件
prepare_deployment() {
    print_step "准备部署文件..."
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    
    # 复制 docker-compose 文件
    cp deploy/docker-compose.prod.yml ${TEMP_DIR}/docker-compose.yml
    
    # 复制环境变量文件 (不包含敏感信息)
    if [[ -f ".env.production" ]]; then
        cp .env.production ${TEMP_DIR}/.env
    else
        print_warning ".env.production 不存在，请确保服务器上有正确的环境变量"
    fi
    
    echo "✅ 部署文件准备完成: ${TEMP_DIR}"
}

# 部署到服务器
deploy_to_server() {
    print_step "部署到 Ubuntu 服务器..."
    
    # 检查服务器连接
    if ! ssh -o ConnectTimeout=10 ${SERVER_USER}@${SERVER_HOST} "echo 'SSH连接成功'" &>/dev/null; then
        print_error "无法连接到服务器 ${SERVER_HOST}"
        print_warning "请检查: 1) SSH密钥配置 2) 服务器地址 3) 网络连接"
        exit 1
    fi
    
    # 上传部署文件
    print_step "上传部署文件..."
    ssh ${SERVER_USER}@${SERVER_HOST} "mkdir -p ${DEPLOY_PATH}"
    scp -r ${TEMP_DIR}/* ${SERVER_USER}@${SERVER_HOST}:${DEPLOY_PATH}/
    
    # 在服务器上执行部署
    print_step "在服务器上执行部署..."
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        cd ${DEPLOY_PATH}
        
        # 停止现有服务
        docker-compose down || true
        
        # 拉取最新镜像 (如果使用外部仓库)
        if [[ "${DOCKER_REGISTRY}" != "local" ]]; then
            docker-compose pull
        fi
        
        # 启动服务
        docker-compose up -d
        
        # 检查服务状态
        sleep 10
        docker-compose ps
        
        # 清理未使用的镜像
        docker image prune -f
        
        echo "🎉 部署完成！"
EOF
    
    echo "✅ 服务器部署完成"
}

# 验证部署
verify_deployment() {
    print_step "验证部署状态..."
    
    # 检查服务健康状态
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        cd ${DEPLOY_PATH}
        
        # 等待服务启动
        echo "等待服务启动..."
        sleep 15
        
        # 检查容器状态
        echo "=== 容器状态 ==="
        docker-compose ps
        
        # 检查日志
        echo "=== 最近日志 ==="
        docker-compose logs --tail=20 backend
        
        # 健康检查
        echo "=== 健康检查 ==="
        if curl -f http://localhost:8000/health 2>/dev/null; then
            echo "✅ 后端服务运行正常"
        else
            echo "❌ 后端服务可能有问题"
        fi
EOF
}

# 清理临时文件
cleanup() {
    if [[ -n "${TEMP_DIR}" && -d "${TEMP_DIR}" ]]; then
        rm -rf ${TEMP_DIR}
        echo "🧹 清理临时文件完成"
    fi
}

# 主函数
main() {
    echo "🚀 APCSA AI Platform - Ubuntu 部署脚本"
    echo "========================================"
    echo "目标服务器: ${SERVER_USER}@${SERVER_HOST}"
    echo "部署路径: ${DEPLOY_PATH}"
    echo "镜像名称: ${DOCKER_REGISTRY}/${IMAGE_NAME}"
    echo ""
    
    # 确认部署
    read -p "确认开始部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 0
    fi
    
    # 执行部署流程
    check_requirements
    build_image
    push_image
    prepare_deployment
    deploy_to_server
    verify_deployment
    cleanup
    
    echo ""
    echo "🎉 部署完成！"
    echo "前端访问: https://your-frontend-domain.com"
    echo "后端API: http://${SERVER_HOST}:8000"
    echo "API文档: http://${SERVER_HOST}:8000/docs"
}

# 错误处理
trap cleanup EXIT

# 运行主函数
main "$@" 