#!/bin/bash

# APCSA Platform Development Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env exists
check_env() {
    if [ ! -f .env ]; then
        log_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        log_info "Please edit .env file with your configuration"
    fi
}

# Start development environment
start_dev() {
    log_info "Starting APCSA Platform development environment..."
    check_env
    docker-compose up -d
    log_success "Development environment started!"
    log_info "Frontend: http://localhost:3000"
    log_info "Backend API: http://localhost:8000"
    log_info "API Docs: http://localhost:8000/docs"
    log_info "PgAdmin: http://localhost:5050"
}

# Stop development environment
stop_dev() {
    log_info "Stopping development environment..."
    docker-compose down
    log_success "Development environment stopped!"
}

# Restart development environment
restart_dev() {
    log_info "Restarting development environment..."
    docker-compose restart
    log_success "Development environment restarted!"
}

# View logs
logs() {
    if [ -z "$1" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$1"
    fi
}

# Clean up
clean() {
    log_warning "This will remove all containers, volumes, and images. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up..."
        docker-compose down -v --rmi all
        docker system prune -f
        log_success "Cleanup completed!"
    else
        log_info "Cleanup cancelled."
    fi
}

# Database operations
db_migrate() {
    log_info "Running database migrations..."
    docker-compose exec backend alembic upgrade head
    log_success "Database migrations completed!"
}

db_reset() {
    log_warning "This will reset the database. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Resetting database..."
        docker-compose exec backend alembic downgrade base
        docker-compose exec backend alembic upgrade head
        log_success "Database reset completed!"
    else
        log_info "Database reset cancelled."
    fi
}

# Show help
show_help() {
    echo "APCSA Platform Development Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start development environment"
    echo "  stop      Stop development environment"
    echo "  restart   Restart development environment"
    echo "  logs      View logs (optional: specify service name)"
    echo "  clean     Clean up all containers and volumes"
    echo "  migrate   Run database migrations"
    echo "  reset-db  Reset database"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs backend"
    echo "  $0 clean"
}

# Main script logic
case "$1" in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        restart_dev
        ;;
    logs)
        logs "$2"
        ;;
    clean)
        clean
        ;;
    migrate)
        db_migrate
        ;;
    reset-db)
        db_reset
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
