#!/bin/bash

# 🚀 APCSA 分布式架构迁移脚本
# 从当前FastAPI单体架构迁移到Scrapy+Redis+Celery分布式架构

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查可用内存
    available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -lt 4096 ]; then
        log_warning "可用内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    available_disk=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$available_disk" -lt 20 ]; then
        log_warning "可用磁盘空间少于20GB，可能影响运行"
    fi
    
    log_success "依赖检查完成"
}

# 备份当前系统
backup_current_system() {
    log_info "备份当前系统..."
    
    backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    if docker ps | grep -q postgres; then
        log_info "备份PostgreSQL数据库..."
        docker exec $(docker ps -qf "name=postgres") pg_dump -U postgres apcsa > "$backup_dir/postgres_backup.sql"
    fi
    
    # 备份Redis数据
    if docker ps | grep -q redis; then
        log_info "备份Redis数据..."
        docker exec $(docker ps -qf "name=redis") redis-cli BGSAVE
        docker cp $(docker ps -qf "name=redis"):/data/dump.rdb "$backup_dir/redis_backup.rdb"
    fi
    
    # 备份配置文件
    cp -r backend "$backup_dir/"
    cp -r frontend "$backup_dir/"
    cp docker-compose.yml "$backup_dir/" 2>/dev/null || true
    
    log_success "系统备份完成: $backup_dir"
}

# 停止当前服务
stop_current_services() {
    log_info "停止当前服务..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
    fi
    
    # 清理孤立容器
    docker container prune -f
    
    log_success "当前服务已停止"
}

# 创建新的项目结构
create_new_structure() {
    log_info "创建新的项目结构..."
    
    # 创建目录结构
    mkdir -p apcsa_distributed/{api_gateway,scrapy_cluster,task_queue,monitoring,infrastructure,shared,tests,scripts,docs}
    mkdir -p apcsa_distributed/api_gateway/{app/{api/v1,core,models,schemas,services},requirements.txt}
    mkdir -p apcsa_distributed/scrapy_cluster/apcsa_scraper/{spiders,items.py,pipelines,middlewares,settings,utils}
    mkdir -p apcsa_distributed/task_queue/{tasks,celery_app.py,celery_config.py,beat_schedule.py}
    mkdir -p apcsa_distributed/monitoring/{prometheus,grafana/{dashboards,provisioning},alertmanager,exporters}
    mkdir -p apcsa_distributed/infrastructure/{docker,kubernetes,terraform}
    mkdir -p apcsa_distributed/shared/{database,utils,schemas}
    
    log_success "项目结构创建完成"
}

# 迁移现有代码
migrate_existing_code() {
    log_info "迁移现有代码..."
    
    # 迁移API代码
    if [ -d "backend" ]; then
        log_info "迁移后端API代码..."
        cp -r backend/* apcsa_distributed/api_gateway/app/
        
        # 重构main.py为FastAPI集群模式
        cat > apcsa_distributed/api_gateway/app/main.py << 'EOF'
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from prometheus_fastapi_instrumentator import Instrumentator
import os
import uvicorn

# 导入现有的API路由
from api import router as api_router

app = FastAPI(
    title="APCSA API Gateway",
    description="分布式APCSA学习平台API网关",
    version="2.0.0"
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 添加Prometheus监控
instrumentator = Instrumentator()
instrumentator.instrument(app).expose(app)

# 添加实例标识中间件
@app.middleware("http")
async def add_instance_header(request: Request, call_next):
    response = await call_next(request)
    response.headers["X-Instance-ID"] = os.getenv("INSTANCE_ID", "unknown")
    return response

# 注册路由
app.include_router(api_router, prefix="/api")

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "version": "2.0.0",
        "instance": os.getenv("INSTANCE_ID", "unknown")
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        workers=1
    )
EOF
    fi
    
    # 迁移前端代码
    if [ -d "frontend" ]; then
        log_info "迁移前端代码..."
        cp -r frontend apcsa_distributed/
    fi
    
    log_success "代码迁移完成"
}

# 生成Scrapy项目
generate_scrapy_project() {
    log_info "生成Scrapy项目..."
    
    cd apcsa_distributed/scrapy_cluster
    
    # 创建Scrapy项目结构
    cat > requirements.txt << 'EOF'
scrapy==2.11.0
scrapy-redis==0.7.3
scrapy-splash==0.8.0
scrapy-rotating-proxies==0.6.2
prometheus-client==0.17.1
psycopg2-binary==2.9.7
pymongo==4.5.0
redis==4.6.0
requests==2.31.0
beautifulsoup4==4.12.2
lxml==4.9.3
itemadapter==0.8.0
twisted==22.10.0
EOF

    # 创建Dockerfile
    cat > Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libxml2-dev \
    libxslt1-dev \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 安装Scrapyd
RUN pip install scrapyd==1.4.3

# 复制项目文件
COPY . .

# 创建Scrapyd配置
RUN mkdir -p /var/lib/scrapyd

EXPOSE 6800

CMD ["scrapyd"]
EOF

    # 创建scrapyd.conf
    cat > scrapyd.conf << 'EOF'
[scrapyd]
eggs_dir    = eggs
logs_dir    = logs
items_dir   = items
jobs_to_keep = 5
dbs_dir     = dbs
max_proc    = 0
max_proc_per_cpu = 4
finished_to_keep = 100
poll_interval = 5.0
bind_address = 0.0.0.0
http_port   = 6800
debug       = off
runner      = scrapyd.runner
application = scrapyd.app.application
launcher    = scrapyd.launcher.Launcher
webroot     = scrapyd.website.Root

[services]
schedule.json     = scrapyd.webservice.Schedule
cancel.json       = scrapyd.webservice.Cancel
addversion.json   = scrapyd.webservice.AddVersion
listprojects.json = scrapyd.webservice.ListProjects
listversions.json = scrapyd.webservice.ListVersions
listspiders.json  = scrapyd.webservice.ListSpiders
delproject.json   = scrapyd.webservice.DeleteProject
delversion.json   = scrapyd.webservice.DeleteVersion
listjobs.json     = scrapyd.webservice.ListJobs
daemonstatus.json = scrapyd.webservice.DaemonStatus
EOF

    cd ../..
    
    log_success "Scrapy项目生成完成"
}

# 生成Celery任务队列
generate_celery_tasks() {
    log_info "生成Celery任务队列..."
    
    cd apcsa_distributed/task_queue
    
    # 创建requirements.txt
    cat > requirements.txt << 'EOF'
celery[redis]==5.3.1
flower==2.0.1
prometheus-client==0.17.1
psycopg2-binary==2.9.7
requests==2.31.0
psutil==5.9.5
kombu==5.3.1
billiard==4.1.0
EOF

    # 创建Dockerfile
    cat > Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash celery
USER celery

CMD ["celery", "-A", "celery_app", "worker", "--loglevel=info"]
EOF

    cd ../..
    
    log_success "Celery任务队列生成完成"
}

# 生成监控配置
generate_monitoring_config() {
    log_info "生成监控配置..."
    
    # 复制监控配置文件
    cp -r monitoring/* apcsa_distributed/monitoring/ 2>/dev/null || true
    
    # 如果没有现有配置，创建基础配置
    if [ ! -f "apcsa_distributed/monitoring/prometheus/prometheus.yml" ]; then
        mkdir -p apcsa_distributed/monitoring/prometheus
        cat > apcsa_distributed/monitoring/prometheus/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway-1:8000', 'api-gateway-2:8000', 'api-gateway-3:8000']
    metrics_path: '/metrics'

  - job_name: 'scrapy-cluster'
    static_configs:
      - targets: ['scrapyd-1:6800', 'scrapyd-2:6800', 'scrapyd-3:6800']

  - job_name: 'celery-workers'
    static_configs:
      - targets: ['celery-worker-1:9540', 'celery-worker-2:9540', 'celery-worker-3:9540']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
EOF
    fi
    
    log_success "监控配置生成完成"
}

# 部署分布式系统
deploy_distributed_system() {
    log_info "部署分布式系统..."
    
    cd apcsa_distributed
    
    # 复制Docker Compose文件
    cp ../docker-compose.distributed.yml docker-compose.yml
    
    # 创建必要的目录
    mkdir -p infrastructure/nginx
    mkdir -p database/init
    mkdir -p monitoring/grafana/dashboards
    
    # 创建基础Nginx配置
    cat > infrastructure/nginx/nginx.conf << 'EOF'
upstream api_backend {
    least_conn;
    server api-gateway-1:8000;
    server api-gateway-2:8000;
    server api-gateway-3:8000;
}

server {
    listen 80;
    server_name localhost;

    location /api/ {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
    
    # 启动基础服务
    log_info "启动基础服务..."
    docker-compose up -d postgres redis-master mongodb
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动监控服务
    log_info "启动监控服务..."
    docker-compose up -d prometheus grafana alertmanager
    
    # 启动Celery服务
    log_info "启动Celery服务..."
    docker-compose up -d celery-beat celery-worker-1 celery-worker-2 celery-worker-3
    
    # 启动Scrapy集群
    log_info "启动Scrapy集群..."
    docker-compose up -d scrapyd-1 scrapyd-2 scrapyd-3
    
    # 启动API网关
    log_info "启动API网关..."
    docker-compose up -d api-gateway-1 api-gateway-2 api-gateway-3
    
    # 启动负载均衡器
    log_info "启动负载均衡器..."
    docker-compose up -d nginx
    
    # 启动前端
    log_info "启动前端..."
    docker-compose up -d frontend
    
    # 启动监控exporters
    log_info "启动监控exporters..."
    docker-compose up -d redis-exporter postgres-exporter node-exporter
    
    cd ..
    
    log_success "分布式系统部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    services=(
        "apcsa-postgres:5432"
        "apcsa-redis-master:6379"
        "apcsa-api-1:8000"
        "apcsa-api-2:8000"
        "apcsa-api-3:8000"
        "apcsa-scrapyd-1:6800"
        "apcsa-prometheus:9090"
        "apcsa-grafana:3000"
        "apcsa-nginx:80"
    )
    
    failed_services=()
    
    for service in "${services[@]}"; do
        container_name=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        if docker ps | grep -q $container_name; then
            log_success "✓ $container_name 运行正常"
        else
            log_error "✗ $container_name 未运行"
            failed_services+=($container_name)
        fi
    done
    
    # 检查API健康状态
    log_info "检查API健康状态..."
    sleep 10
    
    if curl -f http://localhost/api/health &>/dev/null; then
        log_success "✓ API网关健康检查通过"
    else
        log_error "✗ API网关健康检查失败"
        failed_services+=("api-gateway")
    fi
    
    # 检查监控服务
    if curl -f http://localhost:9090/-/healthy &>/dev/null; then
        log_success "✓ Prometheus运行正常"
    else
        log_error "✗ Prometheus运行异常"
        failed_services+=("prometheus")
    fi
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "所有服务验证通过！"
        return 0
    else
        log_error "以下服务验证失败: ${failed_services[*]}"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    log_info "系统访问信息:"
    echo ""
    echo "🌐 前端应用:      http://localhost"
    echo "🔧 API网关:       http://localhost/api"
    echo "📊 Prometheus:    http://localhost:9090"
    echo "📈 Grafana:       http://localhost:3001 (admin/admin123)"
    echo "🚨 AlertManager:  http://localhost:9093"
    echo "🌸 Flower:        http://localhost:5555"
    echo "🕷️ Scrapyd-1:     http://localhost:6800"
    echo "🕷️ Scrapyd-2:     http://localhost:6801"
    echo "🕷️ Scrapyd-3:     http://localhost:6802"
    echo ""
    echo "📋 监控指标:"
    echo "   - Redis:       http://localhost:9121/metrics"
    echo "   - PostgreSQL:  http://localhost:9187/metrics"
    echo "   - Node:        http://localhost:9100/metrics"
    echo ""
}

# 清理函数
cleanup_on_error() {
    log_error "迁移过程中发生错误，正在清理..."
    
    if [ -d "apcsa_distributed" ]; then
        cd apcsa_distributed
        docker-compose down 2>/dev/null || true
        cd ..
    fi
    
    log_info "清理完成，可以重新运行迁移脚本"
}

# 主函数
main() {
    echo "🚀 APCSA 分布式架构迁移脚本"
    echo "=================================="
    echo ""
    
    # 设置错误处理
    trap cleanup_on_error ERR
    
    # 执行迁移步骤
    check_dependencies
    backup_current_system
    stop_current_services
    create_new_structure
    migrate_existing_code
    generate_scrapy_project
    generate_celery_tasks
    generate_monitoring_config
    deploy_distributed_system
    
    # 验证部署
    if verify_deployment; then
        show_access_info
        log_success "🎉 分布式架构迁移完成！"
        echo ""
        echo "📝 下一步:"
        echo "1. 访问 http://localhost:3001 配置Grafana仪表板"
        echo "2. 访问 http://localhost:5555 监控Celery任务"
        echo "3. 查看 MIGRATION_TO_DISTRIBUTED_ARCHITECTURE.md 了解详细信息"
        echo ""
    else
        log_error "部署验证失败，请检查日志"
        exit 1
    fi
}

# 运行主函数
main "$@" 