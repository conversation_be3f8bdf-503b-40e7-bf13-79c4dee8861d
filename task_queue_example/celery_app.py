"""
APCSA 分布式任务队列系统
基于Celery + Redis实现智能调度和监控
"""

import os
import logging
from celery import Celery
from celery.schedules import crontab
from kombu import Queue, Exchange
from prometheus_client import Counter, Histogram, Gauge
import redis

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis连接配置
REDIS_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/1')
REDIS_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/1')

# 创建Celery应用
app = Celery('apcsa_tasks')

# Celery配置
app.conf.update(
    # Broker设置
    broker_url=REDIS_URL,
    result_backend=REDIS_RESULT_BACKEND,
    
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # 任务路由
    task_routes={
        'apcsa_tasks.scraping.*': {'queue': 'scraping'},
        'apcsa_tasks.content.*': {'queue': 'content_processing'},
        'apcsa_tasks.ai.*': {'queue': 'ai_processing'},
        'apcsa_tasks.monitoring.*': {'queue': 'monitoring'},
        'apcsa_tasks.alerts.*': {'queue': 'alerts'},
        'apcsa_tasks.cleanup.*': {'queue': 'cleanup'},
    },
    
    # 队列配置
    task_default_queue='default',
    task_queues=(
        Queue('default', Exchange('default'), routing_key='default'),
        Queue('scraping', Exchange('scraping'), routing_key='scraping',
              queue_arguments={'x-max-priority': 10}),
        Queue('content_processing', Exchange('content'), routing_key='content'),
        Queue('ai_processing', Exchange('ai'), routing_key='ai'),
        Queue('monitoring', Exchange('monitoring'), routing_key='monitoring'),
        Queue('alerts', Exchange('alerts'), routing_key='alerts',
              queue_arguments={'x-max-priority': 10}),
        Queue('cleanup', Exchange('cleanup'), routing_key='cleanup'),
    ),
    
    # Worker配置
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # 任务执行配置
    task_soft_time_limit=300,  # 5分钟软限制
    task_time_limit=600,       # 10分钟硬限制
    task_reject_on_worker_lost=True,
    
    # 结果配置
    result_expires=3600,  # 1小时后过期
    result_persistent=True,
    
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # 重试配置
    task_annotations={
        '*': {
            'rate_limit': '100/m',
            'max_retries': 3,
            'default_retry_delay': 60,
        },
        'apcsa_tasks.scraping.scrape_codehs_content': {
            'rate_limit': '30/m',
            'max_retries': 5,
            'default_retry_delay': 120,
        },
        'apcsa_tasks.ai.generate_lesson_plan': {
            'rate_limit': '10/m',
            'max_retries': 3,
            'default_retry_delay': 180,
        }
    }
)

# Prometheus指标
TASK_COUNTER = Counter('celery_tasks_total', 'Total number of tasks', ['task_name', 'status'])
TASK_DURATION = Histogram('celery_task_duration_seconds', 'Task duration', ['task_name'])
QUEUE_SIZE = Gauge('celery_queue_size', 'Queue size', ['queue_name'])
WORKER_COUNT = Gauge('celery_workers_active', 'Active workers')

# 定时任务配置
app.conf.beat_schedule = {
    # 每小时检查内容更新
    'check-content-updates': {
        'task': 'apcsa_tasks.scraping.check_content_updates',
        'schedule': crontab(minute=0),  # 每小时执行
        'options': {'queue': 'scraping', 'priority': 5}
    },
    
    # 每天凌晨2点执行全量内容抓取
    'daily-full-scrape': {
        'task': 'apcsa_tasks.scraping.full_content_scrape',
        'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点
        'options': {'queue': 'scraping', 'priority': 8}
    },
    
    # 每6小时生成AI内容
    'generate-ai-content': {
        'task': 'apcsa_tasks.ai.batch_generate_content',
        'schedule': crontab(minute=0, hour='*/6'),  # 每6小时
        'options': {'queue': 'ai_processing', 'priority': 6}
    },
    
    # 每15分钟监控系统健康状态
    'system-health-check': {
        'task': 'apcsa_tasks.monitoring.system_health_check',
        'schedule': crontab(minute='*/15'),  # 每15分钟
        'options': {'queue': 'monitoring', 'priority': 9}
    },
    
    # 每5分钟检查队列状态
    'queue-monitoring': {
        'task': 'apcsa_tasks.monitoring.monitor_queues',
        'schedule': crontab(minute='*/5'),  # 每5分钟
        'options': {'queue': 'monitoring', 'priority': 7}
    },
    
    # 每天凌晨3点清理过期数据
    'cleanup-expired-data': {
        'task': 'apcsa_tasks.cleanup.cleanup_expired_data',
        'schedule': crontab(hour=3, minute=0),  # 每天凌晨3点
        'options': {'queue': 'cleanup', 'priority': 3}
    },
    
    # 每周日凌晨4点生成周报
    'weekly-report': {
        'task': 'apcsa_tasks.monitoring.generate_weekly_report',
        'schedule': crontab(hour=4, minute=0, day_of_week=0),  # 每周日凌晨4点
        'options': {'queue': 'monitoring', 'priority': 4}
    }
}

# 任务装饰器，自动添加监控
def monitored_task(*args, **kwargs):
    """添加监控功能的任务装饰器"""
    def decorator(func):
        @app.task(*args, **kwargs)
        def wrapper(*task_args, **task_kwargs):
            task_name = func.__name__
            
            # 记录任务开始
            TASK_COUNTER.labels(task_name=task_name, status='started').inc()
            
            # 计时器
            with TASK_DURATION.labels(task_name=task_name).time():
                try:
                    result = func(*task_args, **task_kwargs)
                    TASK_COUNTER.labels(task_name=task_name, status='success').inc()
                    return result
                except Exception as e:
                    TASK_COUNTER.labels(task_name=task_name, status='failed').inc()
                    logger.error(f"任务 {task_name} 执行失败: {str(e)}")
                    raise
        
        return wrapper
    return decorator

# 导入任务模块
from . import tasks

# 自动发现任务
app.autodiscover_tasks([
    'apcsa_tasks.scraping',
    'apcsa_tasks.content',
    'apcsa_tasks.ai',
    'apcsa_tasks.monitoring',
    'apcsa_tasks.alerts',
    'apcsa_tasks.cleanup'
])

# 健康检查任务
@app.task(bind=True)
def health_check(self):
    """系统健康检查任务"""
    try:
        # 检查Redis连接
        redis_client = redis.from_url(REDIS_URL)
        redis_client.ping()
        
        # 检查数据库连接
        from .utils.database import get_db_connection
        db = get_db_connection()
        db.execute("SELECT 1")
        
        return {
            'status': 'healthy',
            'timestamp': self.request.id,
            'worker_id': self.request.hostname,
            'checks': {
                'redis': 'ok',
                'database': 'ok'
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': self.request.id,
            'worker_id': self.request.hostname
        }

# 任务失败回调
@app.task(bind=True)
def task_failure_handler(self, task_id, error, traceback):
    """任务失败处理器"""
    logger.error(f"任务 {task_id} 失败: {error}")
    
    # 发送告警
    from .tasks.alerts import send_task_failure_alert
    send_task_failure_alert.delay(task_id, str(error), traceback)

# 启动时的初始化
@app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """设置周期性任务"""
    logger.info("设置周期性任务...")
    
    # 立即执行一次健康检查
    health_check.delay()

# Worker启动时的回调
@app.on_after_finalize.connect
def setup_worker_monitoring(sender, **kwargs):
    """设置Worker监控"""
    logger.info("设置Worker监控...")
    
    # 启动Prometheus指标服务器
    try:
        from prometheus_client import start_http_server
        metrics_port = int(os.getenv('METRICS_PORT', 9540))
        start_http_server(metrics_port)
        logger.info(f"Prometheus指标服务器启动在端口 {metrics_port}")
    except Exception as e:
        logger.error(f"启动Prometheus指标服务器失败: {str(e)}")

# 任务重试配置
class TaskRetryConfig:
    """任务重试配置类"""
    
    @staticmethod
    def exponential_backoff(retries):
        """指数退避重试策略"""
        return min(300, (2 ** retries) * 10)  # 最大5分钟
    
    @staticmethod
    def linear_backoff(retries):
        """线性退避重试策略"""
        return min(300, retries * 30)  # 最大5分钟

# 任务优先级配置
class TaskPriority:
    """任务优先级常量"""
    CRITICAL = 10  # 关键任务（告警、健康检查）
    HIGH = 8       # 高优先级（全量抓取）
    NORMAL = 5     # 普通优先级（增量抓取）
    LOW = 3        # 低优先级（清理任务）
    BACKGROUND = 1 # 后台任务（报告生成）

# 队列监控
def get_queue_stats():
    """获取队列统计信息"""
    try:
        redis_client = redis.from_url(REDIS_URL)
        stats = {}
        
        queues = ['default', 'scraping', 'content_processing', 'ai_processing', 
                 'monitoring', 'alerts', 'cleanup']
        
        for queue in queues:
            queue_key = f"celery:{queue}"
            size = redis_client.llen(queue_key)
            stats[queue] = size
            QUEUE_SIZE.labels(queue_name=queue).set(size)
        
        return stats
    except Exception as e:
        logger.error(f"获取队列统计失败: {str(e)}")
        return {}

# 导出配置
__all__ = [
    'app',
    'monitored_task',
    'TaskRetryConfig',
    'TaskPriority',
    'get_queue_stats'
] 